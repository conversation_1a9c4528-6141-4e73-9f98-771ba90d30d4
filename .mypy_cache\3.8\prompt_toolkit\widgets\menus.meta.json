{"data_mtime": 1753839575, "dep_lines": [5, 7, 8, 9, 11, 20, 25, 6, 10, 21, 22, 23, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["prompt_toolkit.application.current", "prompt_toolkit.formatted_text.base", "prompt_toolkit.key_binding.key_bindings", "prompt_toolkit.key_binding.key_processor", "prompt_toolkit.layout.containers", "prompt_toolkit.layout.controls", "prompt_toolkit.widgets.base", "prompt_toolkit.filters", "prompt_toolkit.keys", "prompt_toolkit.mouse_events", "prompt_toolkit.utils", "prompt_toolkit.widgets", "__future__", "typing", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "sys", "logging", "_frozen_importlib", "abc", "enum", "prompt_toolkit.application", "prompt_toolkit.application.application", "prompt_toolkit.buffer", "prompt_toolkit.data_structures", "prompt_toolkit.filters.base", "prompt_toolkit.formatted_text", "prompt_toolkit.key_binding", "prompt_toolkit.layout", "prompt_toolkit.layout.dimension", "prompt_toolkit.layout.layout", "prompt_toolkit.layout.margins", "weakref"], "hash": "20ee198595f28c8ff5ff2a73319853bc4d02b727", "id": "prompt_toolkit.widgets.menus", "ignore_all": true, "interface_hash": "688d783084737a22db65b0d9e8cb6a5fe4b5aebf", "mtime": 1708667605, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\widgets\\menus.py", "plugin_data": null, "size": 13419, "suppressed": [], "version_id": "1.15.0"}