{"data_mtime": 1753839573, "dep_lines": [45, 46, 48, 53, 55, 7, 13, 50, 54, 57, 58, 59, 61, 67, 68, 73, 74, 401, 1146, 1427, 1449, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 36, 38, 49, 1422, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 10, 5, 5, 5, 5, 25, 25, 20, 20, 20, 20, 5, 10, 20, 10, 10, 10, 5, 10, 20, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 5, 20, 5, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.compat", "_pytest.config.exceptions", "_pytest.config.findpaths", "_pytest._code.code", "_pytest.config.argparsing", "collections.abc", "importlib.metadata", "_pytest._code", "_pytest._io", "_pytest.deprecated", "_pytest.hookspec", "_pytest.outcomes", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "_pytest.cacheprovider", "_pytest.terminal", "_pytest.assertion", "_pytest.helpconfig", "packaging.version", "packaging.requirements", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "collections", "copy", "dataclasses", "enum", "functools", "glob", "importlib", "inspect", "os", "pathlib", "re", "shlex", "sys", "textwrap", "types", "typing", "warnings", "pluggy", "_pytest", "pytest", "builtins", "json", "traitlets.utils.warnings", "pprint", "logging", "_frozen_importlib", "_io", "_pytest._io.terminalwriter", "abc", "io", "pluggy._hooks", "pluggy._manager", "pluggy._tracing", "typing_extensions"], "hash": "634d3c8ecef2b5e0d5483e117a47fc0bd6fa3fa6", "id": "_pytest.config", "ignore_all": true, "interface_hash": "d38a287e193cb5f1c9ee48a3ba39537fb2b1102e", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\config\\__init__.py", "plugin_data": null, "size": 70645, "suppressed": [], "version_id": "1.15.0"}