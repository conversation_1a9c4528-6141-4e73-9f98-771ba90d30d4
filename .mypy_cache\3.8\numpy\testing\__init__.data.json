{".class": "MypyFile", "_fullname": "numpy.testing", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "HAS_LAPACK64": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.HAS_LAPACK64", "kind": "Gdef", "module_public": false}, "HAS_REFCOUNT": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.HAS_REFCOUNT", "kind": "Gdef", "module_public": false}, "IS_PYPY": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.IS_PYPY", "kind": "Gdef", "module_public": false}, "IS_PYSTON": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.IS_PYSTON", "kind": "Gdef", "module_public": false}, "IgnoreException": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.IgnoreException", "kind": "Gdef", "module_public": false}, "KnownFailureException": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.KnownFailureException", "kind": "Gdef", "module_public": false}, "PytestTester": {".class": "SymbolTableNode", "cross_ref": "numpy._pytesttester.PytestTester", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SkipTest": {".class": "SymbolTableNode", "cross_ref": "unittest.case.SkipTest", "kind": "Gdef", "module_public": false}, "TestCase": {".class": "SymbolTableNode", "cross_ref": "unittest.case.TestCase", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "assert_": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_", "kind": "Gdef", "module_public": false}, "assert_allclose": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_allclose", "kind": "Gdef", "module_public": false}, "assert_almost_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_almost_equal", "kind": "Gdef", "module_public": false}, "assert_approx_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_approx_equal", "kind": "Gdef", "module_public": false}, "assert_array_almost_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_almost_equal", "kind": "Gdef", "module_public": false}, "assert_array_almost_equal_nulp": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_almost_equal_nulp", "kind": "Gdef", "module_public": false}, "assert_array_compare": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_compare", "kind": "Gdef", "module_public": false}, "assert_array_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_equal", "kind": "Gdef", "module_public": false}, "assert_array_less": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_less", "kind": "Gdef", "module_public": false}, "assert_array_max_ulp": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_array_max_ulp", "kind": "Gdef", "module_public": false}, "assert_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_equal", "kind": "Gdef", "module_public": false}, "assert_no_gc_cycles": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_no_gc_cycles", "kind": "Gdef", "module_public": false}, "assert_no_warnings": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_no_warnings", "kind": "Gdef", "module_public": false}, "assert_raises": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_raises", "kind": "Gdef", "module_public": false}, "assert_raises_regex": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_raises_regex", "kind": "Gdef", "module_public": false}, "assert_string_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_string_equal", "kind": "Gdef", "module_public": false}, "assert_warns": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.assert_warns", "kind": "Gdef", "module_public": false}, "break_cycles": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.break_cycles", "kind": "Gdef", "module_public": false}, "build_err_msg": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.build_err_msg", "kind": "Gdef", "module_public": false}, "clear_and_catch_warnings": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.clear_and_catch_warnings", "kind": "Gdef", "module_public": false}, "decorate_methods": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.decorate_methods", "kind": "Gdef", "module_public": false}, "jiffies": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.jiffies", "kind": "Gdef", "module_public": false}, "measure": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.measure", "kind": "Gdef", "module_public": false}, "memusage": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.memusage", "kind": "Gdef", "module_public": false}, "print_assert_equal": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.print_assert_equal", "kind": "Gdef", "module_public": false}, "rundocs": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.rundocs", "kind": "Gdef", "module_public": false}, "runstring": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.runstring", "kind": "Gdef", "module_public": false}, "suppress_warnings": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.suppress_warnings", "kind": "Gdef", "module_public": false}, "tempdir": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.tempdir", "kind": "Gdef", "module_public": false}, "temppath": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.temppath", "kind": "Gdef", "module_public": false}, "test": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing.test", "name": "test", "type": "numpy._pytesttester.PytestTester"}}, "verbose": {".class": "SymbolTableNode", "cross_ref": "numpy.testing._private.utils.verbose", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\numpy\\testing\\__init__.pyi"}