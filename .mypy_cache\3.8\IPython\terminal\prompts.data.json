{".class": "MypyFile", "_fullname": "IPython.terminal.prompts", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ClassicPrompts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.terminal.prompts.Prompts"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.prompts.ClassicPrompts", "name": "ClassicPrompts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.ClassicPrompts", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.terminal.prompts", "mro": ["IPython.terminal.prompts.ClassicPrompts", "IPython.terminal.prompts.Prompts", "builtins.object"], "names": {".class": "SymbolTable", "continuation_prompt_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.ClassicPrompts.continuation_prompt_tokens", "name": "continuation_prompt_tokens", "type": null}}, "in_prompt_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.ClassicPrompts.in_prompt_tokens", "name": "in_prompt_tokens", "type": null}}, "out_prompt_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.ClassicPrompts.out_prompt_tokens", "name": "out_prompt_tokens", "type": null}}, "rewrite_prompt_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.ClassicPrompts.rewrite_prompt_tokens", "name": "rewrite_prompt_tokens", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.prompts.ClassicPrompts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.prompts.ClassicPrompts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DisplayHook": {".class": "SymbolTableNode", "cross_ref": "IPython.core.displayhook.DisplayHook", "kind": "Gdef"}, "EditingMode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.enums.EditingMode", "kind": "Gdef"}, "Prompts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.prompts.Prompts", "name": "Prompts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.Prompts", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.terminal.prompts", "mro": ["IPython.terminal.prompts.Prompts", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shell"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.Prompts.__init__", "name": "__init__", "type": null}}, "_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.Prompts._width", "name": "_width", "type": null}}, "continuation_prompt_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "width", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.Prompts.continuation_prompt_tokens", "name": "continuation_prompt_tokens", "type": null}}, "current_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.Prompts.current_line", "name": "current_line", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.terminal.prompts.Prompts"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_line of Prompts", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "in_prompt_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.Prompts.in_prompt_tokens", "name": "in_prompt_tokens", "type": null}}, "out_prompt_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.Prompts.out_prompt_tokens", "name": "out_prompt_tokens", "type": null}}, "rewrite_prompt_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.Prompts.rewrite_prompt_tokens", "name": "rewrite_prompt_tokens", "type": null}}, "shell": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.prompts.Prompts.shell", "name": "shell", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vi_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.Prompts.vi_mode", "name": "vi_mode", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.prompts.Prompts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.prompts.Prompts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PygmentsTokens": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.pygments.PygmentsTokens", "kind": "Gdef"}, "RichPromptDisplayHook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.displayhook.DisplayHook"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.prompts.RichPromptDisplayHook", "name": "RichPromptDisplayHook", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.RichPromptDisplayHook", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.terminal.prompts", "mro": ["IPython.terminal.prompts.RichPromptDisplayHook", "IPython.core.displayhook.DisplayHook", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "write_format_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "format_dict", "md_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.RichPromptDisplayHook.write_format_data", "name": "write_format_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "format_dict", "md_dict"], "arg_types": ["IPython.terminal.prompts.RichPromptDisplayHook", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_format_data of RichPromptDisplayHook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_output_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.prompts.RichPromptDisplayHook.write_output_prompt", "name": "write_output_prompt", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.prompts.RichPromptDisplayHook.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.prompts.RichPromptDisplayHook", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.terminal.prompts.Token", "name": "Token", "type": {".class": "AnyType", "missing_import_name": "IPython.terminal.prompts.Token", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.prompts.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.prompts.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.prompts.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.prompts.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.prompts.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.prompts.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "fragment_list_width": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.utils.fragment_list_width", "kind": "Gdef"}, "print_formatted_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.shortcuts.utils.print_formatted_text", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\terminal\\prompts.py"}