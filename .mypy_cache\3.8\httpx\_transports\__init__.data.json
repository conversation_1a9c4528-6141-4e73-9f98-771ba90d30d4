{".class": "MypyFile", "_fullname": "httpx._transports", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGITransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.asgi.ASGITransport", "kind": "Gdef"}, "AsyncBaseTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.base.AsyncBaseTransport", "kind": "Gdef"}, "AsyncHTTPTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.default.AsyncHTTPTransport", "kind": "Gdef"}, "BaseTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.base.BaseTransport", "kind": "Gdef"}, "HTTPTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.default.HTTPTransport", "kind": "Gdef"}, "MockTransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.mock.MockTransport", "kind": "Gdef"}, "WSGITransport": {".class": "SymbolTableNode", "cross_ref": "httpx._transports.wsgi.WSGITransport", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpx._transports.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpx._transports.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\httpx\\_transports\\__init__.py"}