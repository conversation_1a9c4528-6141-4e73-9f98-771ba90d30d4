{"data_mtime": 1753839581, "dep_lines": [25, 26, 27, 28, 13, 15, 16, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 21, 22, 20, 23], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 5, 20, 20, 20, 20, 20, 20, 30, 30, 5, 5, 5, 5], "dependencies": ["structlog._base", "structlog._config", "structlog.processors", "structlog.typing", "__future__", "json", "sys", "typing", "builtins", "re", "traitlets.utils.warnings", "pprint", "functools", "os", "logging", "_frozen_importlib", "abc"], "hash": "55c5bc332b8ba5ae56a9114b1efb206df9069afc", "id": "structlog.twisted", "ignore_all": true, "interface_hash": "1013c0644a89c0ed9e2f6fe0f99a28db7991d0bd", "mtime": 1750470697, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\structlog\\twisted.py", "plugin_data": null, "size": 10091, "suppressed": ["twisted.python.failure", "twisted.python.log", "twisted.python", "zope.interface"], "version_id": "1.15.0"}