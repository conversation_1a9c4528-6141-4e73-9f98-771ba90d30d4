{"data_mtime": 1753839580, "dep_lines": [112, 113, 113, 114, 244, 112, 104, 105, 106, 107, 108, 110, 111, 122, 572, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 20, 20, 10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.utils.PyColorize", "IPython.utils.coloransi", "IPython.utils.py3compat", "IPython.core.excolors", "IPython.terminal.interactiveshell", "IPython.utils", "inspect", "linecache", "sys", "re", "os", "IPython", "contextlib", "pdb", "reprlib", "builtins", "json", "traitlets.utils.warnings", "pprint", "typing", "functools", "logging", "IPython.core.getipython", "IPython.core.interactiveshell", "IPython.terminal", "IPython.utils.colorable", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "bdb", "cmd", "enum", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "types", "typing_extensions"], "hash": "a90a1626103a76df20fa651f784dc358cc5faef8", "id": "IPython.core.debugger", "ignore_all": true, "interface_hash": "bc4126f2676a19898c9fefe99b93604297ff1f95", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\debugger.py", "plugin_data": null, "size": 38954, "suppressed": [], "version_id": "1.15.0"}