{".class": "MypyFile", "_fullname": "prompt_toolkit.key_binding.bindings.page_navigation", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ConditionalKeyBindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings", "kind": "Gdef", "module_public": false}, "KeyBindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "kind": "Gdef", "module_public": false}, "KeyBindingsBase": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.page_navigation.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.page_navigation.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.page_navigation.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.page_navigation.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.page_navigation.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.page_navigation.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.page_navigation.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "buffer_has_focus": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.buffer_has_focus", "kind": "Gdef", "module_public": false}, "emacs_mode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.emacs_mode", "kind": "Gdef", "module_public": false}, "load_emacs_page_navigation_bindings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.bindings.page_navigation.load_emacs_page_navigation_bindings", "name": "load_emacs_page_navigation_bindings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_emacs_page_navigation_bindings", "ret_type": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_page_navigation_bindings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.bindings.page_navigation.load_page_navigation_bindings", "name": "load_page_navigation_bindings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_page_navigation_bindings", "ret_type": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_vi_page_navigation_bindings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.bindings.page_navigation.load_vi_page_navigation_bindings", "name": "load_vi_page_navigation_bindings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_vi_page_navigation_bindings", "ret_type": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge_key_bindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.merge_key_bindings", "kind": "Gdef", "module_public": false}, "scroll_backward": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.scroll.scroll_backward", "kind": "Gdef", "module_public": false}, "scroll_forward": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.scroll.scroll_forward", "kind": "Gdef", "module_public": false}, "scroll_half_page_down": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.scroll.scroll_half_page_down", "kind": "Gdef", "module_public": false}, "scroll_half_page_up": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.scroll.scroll_half_page_up", "kind": "Gdef", "module_public": false}, "scroll_one_line_down": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.scroll.scroll_one_line_down", "kind": "Gdef", "module_public": false}, "scroll_one_line_up": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.scroll.scroll_one_line_up", "kind": "Gdef", "module_public": false}, "scroll_page_down": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.scroll.scroll_page_down", "kind": "Gdef", "module_public": false}, "scroll_page_up": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.scroll.scroll_page_up", "kind": "Gdef", "module_public": false}, "vi_mode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.vi_mode", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\key_binding\\bindings\\page_navigation.py"}