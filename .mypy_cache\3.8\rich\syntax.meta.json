{"data_mtime": 1753839578, "dep_lines": [1, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 1, 2, 3, 4, 5, 6, 7, 8, 842, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22, 23, 24, 25, 26, 38], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5, 5], "dependencies": ["os.path", "rich.containers", "rich.padding", "rich._loop", "rich.cells", "rich.color", "rich.console", "rich.jupyter", "rich.measure", "rich.segment", "rich.style", "rich.text", "os", "platform", "re", "sys", "textwrap", "abc", "pathlib", "typing", "<PERSON><PERSON><PERSON><PERSON>", "builtins", "json", "traitlets.utils.warnings", "pprint", "functools", "logging", "_frozen_importlib", "_typeshed", "datetime", "enum", "rich.color_triplet", "rich.theme", "typing_extensions"], "hash": "c49e97723d993e8adaebd6e8fee312e064147d26", "id": "rich.syntax", "ignore_all": true, "interface_hash": "b762b03f75c46b1c1dbe72cd0bb2a70868fbd9ed", "mtime": 1723156628, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\rich\\syntax.py", "plugin_data": null, "size": 35367, "suppressed": ["pygments.lexer", "pygments.lexers", "pygments.style", "pygments.styles", "pygments.token", "pygments.util"], "version_id": "1.15.0"}