{".class": "MypyFile", "_fullname": "IPython.terminal.debugger", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EditingMode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.enums.EditingMode", "kind": "Gdef"}, "FileHistory": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.history.FileHistory", "kind": "Gdef"}, "IPCompleter": {".class": "SymbolTableNode", "cross_ref": "IPython.core.completer.IPCompleter", "kind": "Gdef"}, "IPythonPTCompleter": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.ptutils.IPythonPTCompleter", "kind": "Gdef"}, "InMemoryHistory": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.history.InMemoryHistory", "kind": "Gdef"}, "PTK3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.debugger.PTK3", "name": "PTK3", "type": "builtins.bool"}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Pdb": {".class": "SymbolTableNode", "cross_ref": "IPython.core.debugger.Pdb", "kind": "Gdef"}, "PromptSession": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.shortcuts.prompt.PromptSession", "kind": "Gdef"}, "PygmentsTokens": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.pygments.PygmentsTokens", "kind": "Gdef"}, "TerminalPdb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.debugger.Pdb"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.debugger.TerminalPdb", "name": "TerminalPdb", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.debugger.TerminalPdb", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.terminal.debugger", "mro": ["IPython.terminal.debugger.TerminalPdb", "IPython.core.debugger.Pdb", "pdb.Pdb", "bdb.Bdb", "cmd.Cmd", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "pt_session_options", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.debugger.TerminalPdb.__init__", "name": "__init__", "type": null}}, "_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.debugger.TerminalPdb._prompt", "name": "_prompt", "type": null}}, "_ptcomp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "IPython.terminal.debugger.TerminalPdb._ptcomp", "name": "_ptcomp", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cmdloop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "intro"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.debugger.TerminalPdb.cmdloop", "name": "cmdloop", "type": null}}, "debugger_history": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.debugger.TerminalPdb.debugger_history", "name": "debugger_history", "type": "prompt_toolkit.history.FileHistory"}}, "do_interact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.debugger.TerminalPdb.do_interact", "name": "do_interact", "type": null}}, "pt_app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.debugger.TerminalPdb.pt_app", "name": "pt_app", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}}}, "pt_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "pt_session_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.debugger.TerminalPdb.pt_init", "name": "pt_init", "type": null}}, "pt_loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.debugger.TerminalPdb.pt_loop", "name": "pt_loop", "type": "asyncio.events.AbstractEventLoop"}}, "thread_executor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.debugger.TerminalPdb.thread_executor", "name": "thread_executor", "type": "concurrent.futures.thread.ThreadPoolExecutor"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.debugger.TerminalPdb.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.debugger.TerminalPdb", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ThreadPoolExecutor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures.thread.ThreadPoolExecutor", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.terminal.debugger.Token", "name": "Token", "type": {".class": "AnyType", "missing_import_name": "IPython.terminal.debugger.Token", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.debugger.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.debugger.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.debugger.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.debugger.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.debugger.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.debugger.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_use_simple_prompt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.debugger._use_simple_prompt", "name": "_use_simple_prompt", "type": "builtins.bool"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "create_app_session": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.create_app_session", "kind": "Gdef"}, "create_ipython_shortcuts": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.create_ipython_shortcuts", "kind": "Gdef"}, "embed": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.embed", "kind": "Gdef"}, "old_trace_dispatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.debugger.old_trace_dispatch", "name": "old_trace_dispatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "frame", "event", "arg"], "arg_types": ["bdb.Bdb", "types.FrameType", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.TraceFunction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pdb": {".class": "SymbolTableNode", "cross_ref": "pdb", "kind": "Gdef"}, "ptk_version": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.__version__", "kind": "Gdef"}, "set_trace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.debugger.set_trace", "name": "set_trace", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\terminal\\debugger.py"}