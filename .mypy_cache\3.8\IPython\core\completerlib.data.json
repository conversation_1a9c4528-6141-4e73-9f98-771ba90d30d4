{".class": "MypyFile", "_fullname": "IPython.core.completerlib", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "TIMEOUT_GIVEUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completerlib.TIMEOUT_GIVEUP", "name": "TIMEOUT_GIVEUP", "type": "builtins.int"}}, "TIMEOUT_STORAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completerlib.TIMEOUT_STORAGE", "name": "TIMEOUT_STORAGE", "type": "builtins.int"}}, "TryNext": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.TryNext", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completerlib.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completerlib.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completerlib.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completerlib.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completerlib.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completerlib.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_suffixes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.completerlib._suffixes", "name": "_suffixes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "all_suffixes": {".class": "SymbolTableNode", "cross_ref": "importlib.machinery.all_suffixes", "kind": "Gdef"}, "arg_split": {".class": "SymbolTableNode", "cross_ref": "IPython.utils._process_common.arg_split", "kind": "Gdef"}, "cd_completer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completerlib.cd_completer", "name": "cd_completer", "type": null}}, "compress_user": {".class": "SymbolTableNode", "cross_ref": "IPython.core.completer.compress_user", "kind": "Gdef"}, "expand_user": {".class": "SymbolTableNode", "cross_ref": "IPython.core.completer.expand_user", "kind": "Gdef"}, "get_ipython": {".class": "SymbolTableNode", "cross_ref": "IPython.core.getipython.get_ipython", "kind": "Gdef"}, "get_root_modules": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completerlib.get_root_modules", "name": "get_root_modules", "type": null}}, "glob": {".class": "SymbolTableNode", "cross_ref": "glob", "kind": "Gdef"}, "import_module": {".class": "SymbolTableNode", "cross_ref": "importlib.import_module", "kind": "Gdef"}, "import_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.completerlib.import_re", "name": "import_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_importable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["module", "attr", "only_modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completerlib.is_importable", "name": "is_importable", "type": null}}, "is_possible_submodule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module", "attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completerlib.is_possible_submodule", "name": "is_possible_submodule", "type": null}}, "magic_run_completer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completerlib.magic_run_completer", "name": "magic_run_completer", "type": null}}, "magic_run_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.completerlib.magic_run_re", "name": "magic_run_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "module_completer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completerlib.module_completer", "name": "module_completer", "type": null}}, "module_completion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completerlib.module_completion", "name": "module_completion", "type": null}}, "module_list": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completerlib.module_list", "name": "module_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_list", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "quick_completer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cmd", "completions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completerlib.quick_completer", "name": "quick_completer", "type": null}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "reset_completer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completerlib.reset_completer", "name": "reset_completer", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time.time", "kind": "Gdef"}, "try_import": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["mod", "only_modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completerlib.try_import", "name": "try_import", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["mod", "only_modules"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "try_import", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "zipimporter": {".class": "SymbolTableNode", "cross_ref": "zipimport.zipimporter", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\completerlib.py"}