{"data_mtime": 1753839573, "dep_lines": [13, 15, 26, 22, 28, 32, 3, 5, 6, 7, 163, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 5, 10, 10, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.mark.expression", "_pytest.mark.structures", "_pytest.config.argparsing", "_pytest.config", "_pytest.stash", "_pytest.nodes", "__future__", "collections", "dataclasses", "typing", "pytest", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "sys", "logging", "_frozen_importlib", "_pytest.compat", "abc", "enum", "pluggy", "pluggy._hooks"], "hash": "e9c1c4d16d98347216023b0c16d2e7af3cc978ea", "id": "_pytest.mark", "ignore_all": true, "interface_hash": "7572e41066775eddb75c2cf64fceb62b135817c2", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\mark\\__init__.py", "plugin_data": null, "size": 9307, "suppressed": [], "version_id": "1.15.0"}