{".class": "MypyFile", "_fullname": "numpy._typing._ufunc", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like.DTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_2Tuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._2Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "numpy._typing._ufunc._2Tuple", "line": 29, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._2Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._2Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_3Tuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._3Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "numpy._typing._ufunc._3Tuple", "line": 30, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._3Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._3Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._3Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_4Tuple": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._4Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "numpy._typing._ufunc._4Tuple", "line": 31, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._4Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._4Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._4Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "id": 1, "name": "_T", "namespace": "numpy._typing._ufunc._4Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_ArrayLikeBool_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeBool_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeInt_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeInt_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_CastingKind": {".class": "SymbolTableNode", "cross_ref": "numpy._Casting<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_GUFunc_Nin2_Nout1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ufunc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "name": "_GUFunc_Nin2_Nout1", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._ufunc", "mro": ["numpy._typing._ufunc._GUFunc_Nin2_Nout1", "numpy.ufunc", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "casting", "order", "dtype", "subok", "signature", "extobj", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "casting", "order", "dtype", "subok", "signature", "extobj", "axes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["typing.SupportsIndex"], "type_ref": "numpy._typing._ufunc._2Tuple"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _GUFunc_Nin2_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "casting", "order", "dtype", "subok", "signature", "extobj", "axes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["typing.SupportsIndex"], "type_ref": "numpy._typing._ufunc._2Tuple"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _GUFunc_Nin2_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "casting", "order", "dtype", "subok", "signature", "extobj", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "casting", "order", "dtype", "subok", "signature", "extobj", "axes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["typing.SupportsIndex"], "type_ref": "numpy._typing._ufunc._2Tuple"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _GUFunc_Nin2_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "casting", "order", "dtype", "subok", "signature", "extobj", "axes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["typing.SupportsIndex"], "type_ref": "numpy._typing._ufunc._2Tuple"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _GUFunc_Nin2_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "casting", "order", "dtype", "subok", "signature", "extobj", "axes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["typing.SupportsIndex"], "type_ref": "numpy._typing._ufunc._2Tuple"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _GUFunc_Nin2_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "casting", "order", "dtype", "subok", "signature", "extobj", "axes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": ["typing.SupportsIndex"], "type_ref": "numpy._typing._ufunc._2Tuple"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _GUFunc_Nin2_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of _GUFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of _GUFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "accumulate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.accumulate", "name": "accumulate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accumulate of _GUFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.accumulate", "name": "accumulate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accumulate of _GUFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.at", "name": "at", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at of _GUFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.at", "name": "at", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at of _GUFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of _GUFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of _GUFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.nargs", "name": "nargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nargs of _GUFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.nargs", "name": "nargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nargs of _GUFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.nin", "name": "nin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nin of _GUFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.nin", "name": "nin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nin of _GUFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.nout", "name": "nout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nout of _GUFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.nout", "name": "nout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nout of _GUFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ntypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.ntypes", "name": "ntypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ntypes of _GUFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.ntypes", "name": "ntypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ntypes of _GUFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "outer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _GUFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _GUFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.reduce", "name": "reduce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce of _GUFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.reduce", "name": "reduce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce of _GUFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reduceat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.reduceat", "name": "reduceat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduceat of _GUFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.reduceat", "name": "reduceat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduceat of _GUFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature of _GUFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "(n?,k),(k,m?)->(n?,m?)"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature of _GUFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "(n?,k),(k,m?)->(n?,m?)"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._GUFunc_Nin2_Nout1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._GUFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._GUFunc_Nin2_Nout1"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NameType", "_NTypes", "_IDType"], "typeddict_type": null}}, "_IDType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "name": "_IDType", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_NTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "name": "_NTypes", "upper_bound": "builtins.int", "values": [], "variance": 0}}, "_NameType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "name": "_NameType", "upper_bound": "builtins.str", "values": [], "variance": 0}}, "_OrderKACF": {".class": "SymbolTableNode", "cross_ref": "numpy._OrderKACF", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ScalarLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._ScalarLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ShapeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._shape._ShapeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SupportsArrayUFunc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._ufunc._SupportsArrayUFunc", "name": "_SupportsArrayUFunc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy._typing._ufunc._SupportsArrayUFunc", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy._typing._ufunc", "mro": ["numpy._typing._ufunc._SupportsArrayUFunc", "builtins.object"], "names": {".class": "SymbolTable", "__array_ufunc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "ufunc", "method", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._SupportsArrayUFunc.__array_ufunc__", "name": "__array_ufunc__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "ufunc", "method", "inputs", "kwargs"], "arg_types": ["numpy._typing._ufunc._SupportsArrayUFunc", "numpy.ufunc", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "__call__"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reduce"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reduceat"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "accumulate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "outer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inner"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__array_ufunc__ of _SupportsArrayUFunc", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._SupportsArrayUFunc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy._typing._ufunc._SupportsArrayUFunc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_UFunc_Nin1_Nout1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ufunc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "name": "_UFunc_Nin1_Nout1", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._ufunc", "mro": ["numpy._typing._ufunc._UFunc_Nin1_Nout1", "numpy.ufunc", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._2Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._2Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._2Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._2Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}, "numpy._typing._ufunc._SupportsArrayUFunc", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._2Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}, "numpy._typing._ufunc._SupportsArrayUFunc", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._2Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._2Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._2Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}, "numpy._typing._ufunc._SupportsArrayUFunc", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._2Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of _UFunc_Nin1_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of _UFunc_Nin1_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "accumulate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.accumulate", "name": "accumulate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accumulate of _UFunc_Nin1_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.accumulate", "name": "accumulate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accumulate of _UFunc_Nin1_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.at", "name": "at", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}, "numpy._typing._ufunc._SupportsArrayUFunc", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at of _UFunc_Nin1_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of _UFunc_Nin1_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of _UFunc_Nin1_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.nargs", "name": "nargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nargs of _UFunc_Nin1_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.nargs", "name": "nargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nargs of _UFunc_Nin1_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.nin", "name": "nin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nin of _UFunc_Nin1_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.nin", "name": "nin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nin of _UFunc_Nin1_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.nout", "name": "nout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nout of _UFunc_Nin1_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.nout", "name": "nout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nout of _UFunc_Nin1_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ntypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.ntypes", "name": "ntypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ntypes of _UFunc_Nin1_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.ntypes", "name": "ntypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ntypes of _UFunc_Nin1_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "outer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin1_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin1_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.reduce", "name": "reduce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce of _UFunc_Nin1_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.reduce", "name": "reduce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce of _UFunc_Nin1_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reduceat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.reduceat", "name": "reduceat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduceat of _UFunc_Nin1_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.reduceat", "name": "reduceat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduceat of _UFunc_Nin1_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature of _UFunc_Nin1_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature of _UFunc_Nin1_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout1"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NameType", "_NTypes", "_IDType"], "typeddict_type": null}}, "_UFunc_Nin1_Nout2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ufunc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "name": "_UFunc_Nin1_Nout2", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._ufunc", "mro": ["numpy._typing._ufunc._UFunc_Nin1_Nout2", "numpy.ufunc", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}, "numpy._typing._ufunc._SupportsArrayUFunc", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}, "numpy._typing._ufunc._SupportsArrayUFunc", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}, "numpy._typing._ufunc._SupportsArrayUFunc", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "accumulate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.accumulate", "name": "accumulate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accumulate of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.accumulate", "name": "accumulate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accumulate of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.at", "name": "at", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.at", "name": "at", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.nargs", "name": "nargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nargs of _UFunc_Nin1_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.nargs", "name": "nargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nargs of _UFunc_Nin1_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.nin", "name": "nin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nin of _UFunc_Nin1_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.nin", "name": "nin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nin of _UFunc_Nin1_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.nout", "name": "nout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nout of _UFunc_Nin1_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.nout", "name": "nout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nout of _UFunc_Nin1_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ntypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.ntypes", "name": "ntypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ntypes of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.ntypes", "name": "ntypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ntypes of _UFunc_Nin1_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "outer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.reduce", "name": "reduce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.reduce", "name": "reduce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reduceat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.reduceat", "name": "reduceat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduceat of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.reduceat", "name": "reduceat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduceat of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature of _UFunc_Nin1_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._UFunc_Nin1_Nout2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin1_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin1_Nout2"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NameType", "_NTypes", "_IDType"], "typeddict_type": null}}, "_UFunc_Nin2_Nout1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ufunc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "name": "_UFunc_Nin2_Nout1", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._ufunc", "mro": ["numpy._typing._ufunc._UFunc_Nin2_Nout1", "numpy.ufunc", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "accumulate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "array", "axis", "dtype", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.accumulate", "name": "accumulate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "array", "axis", "dtype", "out"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "typing.SupportsIndex", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accumulate of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.at", "name": "at", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at of _UFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.nargs", "name": "nargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nargs of _UFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.nargs", "name": "nargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nargs of _UFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.nin", "name": "nin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nin of _UFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.nin", "name": "nin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nin of _UFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.nout", "name": "nout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nout of _UFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.nout", "name": "nout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nout of _UFunc_Nin2_Nout1", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ntypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.ntypes", "name": "ntypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ntypes of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.ntypes", "name": "ntypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ntypes of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "outer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.outer", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin2_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin2_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin2_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._3Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "array", "axis", "dtype", "out", "keepdims", "initial", "where"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.reduce", "name": "reduce", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "array", "axis", "dtype", "out", "keepdims", "initial", "where"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._shape._ShapeLike"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce of _UFunc_Nin2_Nout1", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reduceat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "array", "indices", "axis", "dtype", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.reduceat", "name": "reduceat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "array", "indices", "axis", "dtype", "out"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "typing.SupportsIndex", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduceat of _UFunc_Nin2_Nout1", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature of _UFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature of _UFunc_Nin2_Nout1", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout1.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout1", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout1"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NameType", "_NTypes", "_IDType"], "typeddict_type": null}}, "_UFunc_Nin2_Nout2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.ufunc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "name": "_UFunc_Nin2_Nout2", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy._typing._ufunc", "mro": ["numpy._typing._ufunc._UFunc_Nin2_Nout2", "numpy.ufunc", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.__call__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, null, "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, null, "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._4Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, null, "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._4Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._4Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._4Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, null, "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, {".class": "NoneType"}, {".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._4Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", null, null, null, null, "out", "where", "casting", "order", "dtype", "subok", "signature", "extobj"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeBool_co"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._Casting<PERSON><PERSON>"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._OrderKACF"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "type_ref": "numpy._typing._ufunc._4Tuple"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "type_ref": "numpy._typing._ufunc._2Tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.__name__", "name": "__name__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__name__ of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "accumulate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.accumulate", "name": "accumulate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accumulate of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.accumulate", "name": "accumulate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accumulate of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.at", "name": "at", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.at", "name": "at", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "identity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.identity", "name": "identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.nargs", "name": "nargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nargs of _UFunc_Nin2_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.nargs", "name": "nargs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nargs of _UFunc_Nin2_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.nin", "name": "nin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nin of _UFunc_Nin2_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.nin", "name": "nin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nin of _UFunc_Nin2_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.nout", "name": "nout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nout of _UFunc_Nin2_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.nout", "name": "nout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nout of _UFunc_Nin2_Nout2", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ntypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.ntypes", "name": "ntypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ntypes of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.ntypes", "name": "ntypes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ntypes of _UFunc_Nin2_Nout2", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "outer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.outer", "name": "outer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outer of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reduce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.reduce", "name": "reduce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.reduce", "name": "reduce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduce of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reduceat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.reduceat", "name": "reduceat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduceat of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.reduceat", "name": "reduceat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reduceat of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature of _UFunc_Nin2_Nout2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._UFunc_Nin2_Nout2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NameType", "id": 1, "name": "_NameType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.str", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._NTypes", "id": 2, "name": "_NTypes", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": "builtins.int", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy._typing._ufunc._IDType", "id": 3, "name": "_IDType", "namespace": "numpy._typing._ufunc._UFunc_Nin2_Nout2", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy._typing._ufunc._UFunc_Nin2_Nout2"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_NameType", "_NTypes", "_IDType"], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._ufunc.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._ufunc.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._ufunc.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._ufunc.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._ufunc.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy._typing._ufunc.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ufunc": {".class": "SymbolTableNode", "cross_ref": "numpy.ufunc", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\numpy\\_typing\\_ufunc.pyi"}