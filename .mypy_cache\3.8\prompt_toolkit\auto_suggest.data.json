{".class": "MypyFile", "_fullname": "prompt_toolkit.auto_suggest", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_public": false}, "AutoSuggest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_suggestion", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.auto_suggest.AutoSuggest", "name": "AutoSuggest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "prompt_toolkit.auto_suggest.AutoSuggest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.auto_suggest", "mro": ["prompt_toolkit.auto_suggest.AutoSuggest", "builtins.object"], "names": {".class": "SymbolTable", "get_suggestion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "prompt_toolkit.auto_suggest.AutoSuggest.get_suggestion", "name": "get_suggestion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "document"], "arg_types": ["prompt_toolkit.auto_suggest.AutoSuggest", "prompt_toolkit.buffer.Buffer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_suggestion of AutoSuggest", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.Suggestion", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.auto_suggest.AutoSuggest.get_suggestion", "name": "get_suggestion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "document"], "arg_types": ["prompt_toolkit.auto_suggest.AutoSuggest", "prompt_toolkit.buffer.Buffer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_suggestion of AutoSuggest", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.Suggestion", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_suggestion_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buff", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "prompt_toolkit.auto_suggest.AutoSuggest.get_suggestion_async", "name": "get_suggestion_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buff", "document"], "arg_types": ["prompt_toolkit.auto_suggest.AutoSuggest", "prompt_toolkit.buffer.Buffer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_suggestion_async of AutoSuggest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.Suggestion", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.auto_suggest.AutoSuggest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.auto_suggest.AutoSuggest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutoSuggestFromHistory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.auto_suggest.AutoSuggest"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.auto_suggest.AutoSuggestFromHistory", "name": "AutoSuggestFromHistory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.AutoSuggestFromHistory", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.auto_suggest", "mro": ["prompt_toolkit.auto_suggest.AutoSuggestFromHistory", "prompt_toolkit.auto_suggest.AutoSuggest", "builtins.object"], "names": {".class": "SymbolTable", "get_suggestion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.AutoSuggestFromHistory.get_suggestion", "name": "get_suggestion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "document"], "arg_types": ["prompt_toolkit.auto_suggest.AutoSuggestFromHistory", "prompt_toolkit.buffer.Buffer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_suggestion of AutoSuggestFromHistory", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.Suggestion", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.auto_suggest.AutoSuggestFromHistory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.auto_suggest.AutoSuggestFromHistory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.buffer.Buffer", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ConditionalAutoSuggest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.auto_suggest.AutoSuggest"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.auto_suggest.ConditionalAutoSuggest", "name": "ConditionalAutoSuggest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.ConditionalAutoSuggest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.auto_suggest", "mro": ["prompt_toolkit.auto_suggest.ConditionalAutoSuggest", "prompt_toolkit.auto_suggest.AutoSuggest", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "auto_suggest", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.ConditionalAutoSuggest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "auto_suggest", "filter"], "arg_types": ["prompt_toolkit.auto_suggest.ConditionalAutoSuggest", "prompt_toolkit.auto_suggest.AutoSuggest", {".class": "UnionType", "items": ["builtins.bool", "prompt_toolkit.filters.base.Filter"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConditionalAutoSuggest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_suggest": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.auto_suggest.ConditionalAutoSuggest.auto_suggest", "name": "auto_suggest", "type": "prompt_toolkit.auto_suggest.AutoSuggest"}}, "filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.auto_suggest.ConditionalAutoSuggest.filter", "name": "filter", "type": "prompt_toolkit.filters.base.Filter"}}, "get_suggestion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.ConditionalAutoSuggest.get_suggestion", "name": "get_suggestion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "document"], "arg_types": ["prompt_toolkit.auto_suggest.ConditionalAutoSuggest", "prompt_toolkit.buffer.Buffer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_suggestion of ConditionalAutoSuggest", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.Suggestion", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.auto_suggest.ConditionalAutoSuggest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.auto_suggest.ConditionalAutoSuggest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Document": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.document.Document", "kind": "Gdef", "module_public": false}, "DummyAutoSuggest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.auto_suggest.AutoSuggest"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.auto_suggest.DummyAutoSuggest", "name": "DummyAutoSuggest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.DummyAutoSuggest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.auto_suggest", "mro": ["prompt_toolkit.auto_suggest.DummyAutoSuggest", "prompt_toolkit.auto_suggest.AutoSuggest", "builtins.object"], "names": {".class": "SymbolTable", "get_suggestion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.DummyAutoSuggest.get_suggestion", "name": "get_suggestion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "document"], "arg_types": ["prompt_toolkit.auto_suggest.DummyAutoSuggest", "prompt_toolkit.buffer.Buffer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_suggestion of DummyAutoSuggest", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.Suggestion", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.auto_suggest.DummyAutoSuggest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.auto_suggest.DummyAutoSuggest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DynamicAutoSuggest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.auto_suggest.AutoSuggest"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.auto_suggest.DynamicAutoSuggest", "name": "DynamicAutoSuggest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.DynamicAutoSuggest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.auto_suggest", "mro": ["prompt_toolkit.auto_suggest.DynamicAutoSuggest", "prompt_toolkit.auto_suggest.AutoSuggest", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "get_auto_suggest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.DynamicAutoSuggest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "get_auto_suggest"], "arg_types": ["prompt_toolkit.auto_suggest.DynamicAutoSuggest", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.AutoSuggest", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DynamicAutoSuggest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_auto_suggest": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.auto_suggest.DynamicAutoSuggest.get_auto_suggest", "name": "get_auto_suggest", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.AutoSuggest", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_suggestion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buff", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.DynamicAutoSuggest.get_suggestion", "name": "get_suggestion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buff", "document"], "arg_types": ["prompt_toolkit.auto_suggest.DynamicAutoSuggest", "prompt_toolkit.buffer.Buffer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_suggestion of DynamicAutoSuggest", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.Suggestion", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_suggestion_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buff", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "prompt_toolkit.auto_suggest.DynamicAutoSuggest.get_suggestion_async", "name": "get_suggestion_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buff", "document"], "arg_types": ["prompt_toolkit.auto_suggest.DynamicAutoSuggest", "prompt_toolkit.buffer.Buffer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_suggestion_async of DynamicAutoSuggest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.Suggestion", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.auto_suggest.DynamicAutoSuggest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.auto_suggest.DynamicAutoSuggest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.Filter", "kind": "Gdef", "module_public": false}, "Suggestion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.auto_suggest.Suggestion", "name": "Suggestion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.Suggestion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.auto_suggest", "mro": ["prompt_toolkit.auto_suggest.Suggestion", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.Suggestion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["prompt_toolkit.auto_suggest.Suggestion", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Suggestion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.Suggestion.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.auto_suggest.Suggestion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Suggestion", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.auto_suggest.Suggestion.text", "name": "text", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.auto_suggest.Suggestion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.auto_suggest.Suggestion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "ThreadedAutoSuggest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.auto_suggest.AutoSuggest"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.auto_suggest.ThreadedAutoSuggest", "name": "ThreadedAutoSuggest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.ThreadedAutoSuggest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.auto_suggest", "mro": ["prompt_toolkit.auto_suggest.ThreadedAutoSuggest", "prompt_toolkit.auto_suggest.AutoSuggest", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auto_suggest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.ThreadedAutoSuggest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "auto_suggest"], "arg_types": ["prompt_toolkit.auto_suggest.ThreadedAutoSuggest", "prompt_toolkit.auto_suggest.AutoSuggest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ThreadedAutoSuggest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_suggest": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.auto_suggest.ThreadedAutoSuggest.auto_suggest", "name": "auto_suggest", "type": "prompt_toolkit.auto_suggest.AutoSuggest"}}, "get_suggestion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buff", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.auto_suggest.ThreadedAutoSuggest.get_suggestion", "name": "get_suggestion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buff", "document"], "arg_types": ["prompt_toolkit.auto_suggest.ThreadedAutoSuggest", "prompt_toolkit.buffer.Buffer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_suggestion of ThreadedAutoSuggest", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.Suggestion", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_suggestion_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buff", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "prompt_toolkit.auto_suggest.ThreadedAutoSuggest.get_suggestion_async", "name": "get_suggestion_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buff", "document"], "arg_types": ["prompt_toolkit.auto_suggest.ThreadedAutoSuggest", "prompt_toolkit.buffer.Buffer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_suggestion_async of ThreadedAutoSuggest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.Suggestion", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.auto_suggest.ThreadedAutoSuggest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.auto_suggest.ThreadedAutoSuggest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.auto_suggest.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.auto_suggest.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.auto_suggest.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.auto_suggest.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.auto_suggest.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.auto_suggest.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.auto_suggest.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "run_in_executor_with_context": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.utils.run_in_executor_with_context", "kind": "Gdef", "module_public": false}, "to_filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.to_filter", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\auto_suggest.py"}