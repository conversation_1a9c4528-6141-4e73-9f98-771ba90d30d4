{".class": "MypyFile", "_fullname": "prompt_toolkit.completion.fuzzy_completer", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnyFormattedText": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CompleteEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.CompleteEvent", "kind": "Gdef", "module_public": false}, "Completer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.Completer", "kind": "Gdef", "module_public": false}, "Completion": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.Completion", "kind": "Gdef", "module_public": false}, "Document": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.document.Document", "kind": "Gdef", "module_public": false}, "FilterOrBool": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.FilterOrBool", "kind": "Gdef", "module_public": false}, "FuzzyCompleter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.completion.base.Completer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter", "name": "FuzzyCompleter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.completion.fuzzy_completer", "mro": ["prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter", "prompt_toolkit.completion.base.Completer", "builtins.object"], "names": {".class": "SymbolTable", "WORD": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter.WORD", "name": "WORD", "type": "builtins.bool"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "completer", "WORD", "pattern", "enable_fuzzy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "completer", "WORD", "pattern", "enable_fuzzy"], "arg_types": ["prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter", "prompt_toolkit.completion.base.Completer", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FuzzyCompleter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_display": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fuzzy_match", "word_before_cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter._get_display", "name": "_get_display", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fuzzy_match", "word_before_cursor"], "arg_types": ["prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_display of FuzzyCompleter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_fuzzy_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "complete_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter._get_fuzzy_completions", "name": "_get_fuzzy_completions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "complete_event"], "arg_types": ["prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter", "prompt_toolkit.document.Document", "prompt_toolkit.completion.base.CompleteEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_fuzzy_completions of FuzzyCompleter", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.completion.base.Completion"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter._get_pattern", "name": "_get_pattern", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_pattern of FuzzyCompleter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "completer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter.completer", "name": "completer", "type": "prompt_toolkit.completion.base.Completer"}}, "enable_fuzzy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter.enable_fuzzy", "name": "enable_fuzzy", "type": "prompt_toolkit.filters.base.Filter"}}, "get_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "complete_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter.get_completions", "name": "get_completions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "complete_event"], "arg_types": ["prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter", "prompt_toolkit.document.Document", "prompt_toolkit.completion.base.CompleteEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_completions of FuzzyCompleter", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.completion.base.Completion"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pattern": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter.pattern", "name": "pattern", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FuzzyWordCompleter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.completion.base.Completer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter", "name": "<PERSON><PERSON>Word<PERSON>ompleter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.completion.fuzzy_completer", "mro": ["prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter", "prompt_toolkit.completion.base.Completer", "builtins.object"], "names": {".class": "SymbolTable", "WORD": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter.WORD", "name": "WORD", "type": "builtins.bool"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "words", "meta_dict", "WORD"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "words", "meta_dict", "WORD"], "arg_types": ["prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FuzzyWordCompleter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fuzzy_completer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter.fuzzy_completer", "name": "fuzzy_completer", "type": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter"}}, "get_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "complete_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter.get_completions", "name": "get_completions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "complete_event"], "arg_types": ["prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter", "prompt_toolkit.document.Document", "prompt_toolkit.completion.base.CompleteEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_completions of FuzzyWordCompleter", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.completion.base.Completion"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "meta_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter.meta_dict", "name": "meta_dict", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "word_completer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter.word_completer", "name": "word_completer", "type": "prompt_toolkit.completion.word_completer.WordCompleter"}}, "words": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter.words", "name": "words", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_public": false}, "StyleAndTextTuples": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples", "kind": "Gdef", "module_public": false}, "WordCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.word_completer.WordCompleter", "kind": "Gdef", "module_public": false}, "_FuzzyMatch": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch", "name": "_FuzzyMatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["match_length", "start_pos", "completion"]}}, "module_name": "prompt_toolkit.completion.fuzzy_completer", "mro": ["prompt_toolkit.completion.fuzzy_completer._FuzzyMatch", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "match_length", "start_pos", "completion"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "match_length", "start_pos", "completion"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _FuzzyMatch", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _FuzzyMatch", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _FuzzyMatch", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _FuzzyMatch", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "match_length", "start_pos", "completion"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "match_length", "start_pos", "completion"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _FuzzyMatch", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch._source", "name": "_source", "type": "builtins.str"}}, "completion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.completion", "name": "completion", "type": "prompt_toolkit.completion.base.Completion"}}, "completion-redefinition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.completion", "kind": "<PERSON><PERSON><PERSON>"}, "match_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.match_length", "name": "match_length", "type": "builtins.int"}}, "match_length-redefinition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.match_length", "kind": "<PERSON><PERSON><PERSON>"}, "start_pos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.start_pos", "name": "start_pos", "type": "builtins.int"}}, "start_pos-redefinition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.start_pos", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": "prompt_toolkit.completion.fuzzy_completer._FuzzyMatch"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "prompt_toolkit.completion.base.Completion"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "prompt_toolkit.completion.base.Completion"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.completion.fuzzy_completer.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.fuzzy_completer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "to_filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.to_filter", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\completion\\fuzzy_completer.py"}