{"start_time": 1753491854.6677527, "saved_at": 1753886261.4605992, "request_count": {"POST /proxy/metrics/reset": 1, "GET /proxy/metrics": 429, "GET /proxy/status": 129, "POST /api/generate": 155, "POST /api/chat": 13, "GET /api/tags": 38, "GET /": 3, "GET /api/version": 3, "POST /proxy/metrics/cleanup": 1}, "request_duration": {"POST /proxy/metrics/reset": [0.008079051971435547], "GET /proxy/metrics": [0.00833582878112793, 0.008246660232543945, 0.007765769958496094, 0.007226228713989258, 0.009305238723754883, 0.007961273193359375, 0.007124900817871094, 0.00761103630065918, 0.007124900817871094, 0.0071485042572021484, 0.0075032711029052734, 0.007625102996826172, 0.007517337799072266, 0.00851130485534668, 0.0076141357421875, 0.00690460205078125, 0.008484363555908203, 0.008684158325195312, 0.007538557052612305, 0.008581399917602539, 0.009335517883300781, 0.008174896240234375, 0.013486146926879883, 0.008265018463134766, 0.009555816650390625, 0.014506340026855469, 0.007960081100463867, 0.008409738540649414, 0.013602018356323242, 0.014334440231323242, 0.019033193588256836, 0.011011838912963867, 0.012443304061889648, 0.010657548904418945, 0.008765220642089844, 0.00939321517944336, 0.013236284255981445, 0.01015782356262207, 0.008229255676269531, 0.008163928985595703, 0.009141683578491211, 0.008946418762207031, 0.009300470352172852, 0.007836103439331055, 0.008150339126586914, 0.010433673858642578, 0.009443521499633789, 0.008045434951782227, 0.007886886596679688, 0.0070476531982421875, 0.007752180099487305, 0.008368253707885742, 0.009185314178466797, 0.007602214813232422, 0.007992267608642578, 0.007399797439575195, 0.008478164672851562, 0.008679389953613281, 0.007705211639404297, 0.008083581924438477, 0.00758051872253418, 0.006877422332763672, 0.007160186767578125, 0.008658885955810547, 0.009223699569702148, 0.008420705795288086, 0.007955312728881836, 0.009169578552246094, 0.007774829864501953, 0.009002447128295898, 0.008591890335083008, 0.011780977249145508, 0.013661861419677734, 0.008112192153930664, 0.008782148361206055, 0.0072422027587890625, 0.011186361312866211, 0.009172677993774414, 0.0073397159576416016, 0.011472225189208984, 0.009166479110717773, 0.008888006210327148, 0.01035761833190918, 0.008949041366577148, 0.01141214370727539, 0.010050773620605469, 0.009212732315063477, 0.009618759155273438, 0.008445024490356445, 0.009115934371948242, 0.007598400115966797, 0.007760763168334961, 0.009383440017700195, 0.007117033004760742, 0.00888371467590332, 0.008780956268310547, 0.00911569595336914, 0.008228540420532227, 0.00799703598022461, 0.008250951766967773], "GET /proxy/status": [0.0003483295440673828, 0.0001819133758544922, 0.0002682209014892578, 0.00022673606872558594, 0.0002913475036621094, 0.00029730796813964844, 0.00018644332885742188, 0.003246307373046875, 0.0015411376953125, 0.0002262592315673828, 0.00042891502380371094, 0.0001766681671142578, 0.00016498565673828125, 0.00030231475830078125, 0.0011594295501708984, 0.0003981590270996094, 0.0004088878631591797, 0.0008294582366943359, 0.0003123283386230469, 0.00030159950256347656, 0.0002052783966064453, 0.00022268295288085938, 0.0003654956817626953, 0.00028705596923828125, 0.0001838207244873047, 0.0011794567108154297, 0.0003266334533691406, 0.0003528594970703125, 0.00025272369384765625, 0.00025010108947753906, 0.00039386749267578125, 0.0003070831298828125, 0.0012230873107910156, 0.00016808509826660156, 0.00016832351684570312, 0.00014925003051757812, 0.00018286705017089844, 0.00017404556274414062, 0.000164031982421875, 0.0002474784851074219, 0.00016736984252929688, 0.00025534629821777344, 0.00019741058349609375, 0.0002090930938720703, 0.00016355514526367188, 0.0005173683166503906, 0.0001857280731201172, 0.0002033710479736328, 0.0008034706115722656, 0.0004088878631591797, 0.00028824806213378906, 0.00017762184143066406, 0.0001842975616455078, 0.00018858909606933594, 0.00017213821411132812, 0.00030732154846191406, 0.0002200603485107422, 0.0008270740509033203, 0.0003063678741455078, 0.0002048015594482422, 0.0002086162567138672, 0.0002429485321044922, 0.0001850128173828125, 0.0001800060272216797, 0.00021219253540039062, 0.00013780593872070312, 0.00014710426330566406, 0.0003409385681152344, 0.0004057884216308594, 0.0005624294281005859, 0.0010769367218017578, 0.004357337951660156, 0.0005192756652832031, 0.0004799365997314453, 0.0002617835998535156, 0.0002868175506591797, 0.0002300739288330078, 0.00020456314086914062, 0.0003211498260498047, 0.00037288665771484375, 0.0002574920654296875, 0.0002980232238769531, 0.0005414485931396484, 0.0003859996795654297, 0.0002925395965576172, 0.0001952648162841797, 0.00020766258239746094, 0.0004367828369140625, 0.0002529621124267578, 0.00017642974853515625, 0.00019073486328125, 0.00023245811462402344, 0.0005865097045898438, 0.0005099773406982422, 0.0003247261047363281, 0.00047135353088378906, 0.0002071857452392578, 0.00023102760314941406, 0.00018644332885742188, 0.0002155303955078125], "POST /api/generate": [0.5644886493682861, 5.51922345161438, 4.4136927127838135, 51.447978496551514, 3.40250301361084, 4.134925603866577, 0.0022373199462890625, 8.091779470443726, 0.9088726043701172, 13.775399208068848, 5.974713087081909, 2.395066499710083, 6.104565382003784, 0.8146324157714844, 32.67900538444519, 6.220295190811157, 24.00834035873413, 0.9904773235321045, 13.16027545928955, 3.0255918502807617, 3.8133952617645264, 14.467668056488037, 2.3554446697235107, 0.37918615341186523, 0.3618583679199219, 0.3296229839324951, 3.8296689987182617, 4.87405252456665, 2.1995954513549805, 8.629430294036865, 35.2440242767334, 10.740895986557007, 7.762277126312256, 4.787411212921143, 36.21790075302124, 0.000438690185546875, 13.757683515548706, 8.067044734954834, 12.760895729064941, 4.750929594039917, 23.960748195648193, 0.0002758502960205078, 10.550098180770874, 1.5877985954284668, 3.898238182067871, 1.1575684547424316, 0.3159515857696533, 0.3804447650909424, 0.7113022804260254, -0.04937386512756348, 0.43117189407348633, 7.1972105503082275, 21.889899969100952, 9.187590599060059, 4.177763223648071, 0.00033855438232421875, 0.0002758502960205078, 6.659513235092163, 0.10174036026000977, 0.0002307891845703125, 0.00020933151245117188, 3.896766424179077, 2.821174383163452, 5.953388690948486, 5.519206285476685, 4.56959867477417, 2.067125082015991, 20.651923179626465, 3.766080379486084, 0.5877985954284668, 0.616471529006958, 0.7179458141326904, 0.6144704818725586, 9.100614070892334, 27.21884775161743, 18.188387632369995, 301.31502866744995, 44.06521201133728, 3.814056396484375, 13.79568076133728, 2.670806407928467, 5.537399530410767, 9.772827625274658, 1.2902545928955078, 5.954418659210205, 14.544468402862549, 13.044536828994751, 23.921113967895508, 20.32419729232788, 9.94236969947815, 11.420101881027222, 62.68362379074097, 319.766321182251, 319.76805901527405, 0.0005795955657958984, 0.0007529258728027344, 0.0003750324249267578, 0.0008020401000976562, 3.3910105228424072, 0], "POST /api/chat": [5.147516489028931, 15.463821172714233, 5.294865846633911, -0.8523826599121094, 7.381824970245361, 48.02816462516785, 7.350108623504639, 4.9162328243255615, -0.3877279758453369, 2.2298800945281982, 19.398632764816284, 4.313049554824829, 4.870074033737183], "GET /api/tags": [0.08531856536865234, 0.09807777404785156, 0.10579085350036621, 0.13324666023254395, 0.07547712326049805, 0.0927591323852539, 0.10317230224609375, 0.07257795333862305, 0.06013846397399902, 0.05866193771362305, 0.06563925743103027, 0.06643319129943848, 0.07505011558532715, 0.07096624374389648, 0.12537813186645508, 0.08399629592895508, 0.07012248039245605, 0.07898163795471191, 0.10027384757995605, 0.10317397117614746, 0.09221887588500977, 0.10700464248657227, 0.24408698081970215, 0.10001111030578613, 0.07370495796203613, 0.7951929569244385, 0.07008981704711914, 0.05976510047912598, 0.0738368034362793, 0.08251380920410156, 0.06316924095153809, 1.0810925960540771, 0.08147621154785156, 0.07912230491638184, 0.07593846321105957, 0.07866549491882324, 0.07825732231140137, 0.07563233375549316], "GET /": [2.1457672119140625e-06, 1.9073486328125e-06, 2.6226043701171875e-06], "GET /api/version": [1.9073486328125e-06, 1.9073486328125e-06, 3.0994415283203125e-06], "POST /proxy/metrics/cleanup": [0.007741451263427734]}, "error_count": {"POST /api/generate": 14}, "status_codes": {"200": 14, "500": 1}, "host_request_count": {"http://************:11434": 38, "http://************:11434": 28, "http://************:11434": 20, "http://************:11434": 47}, "host_error_count": {"http://************:11434": 2}, "host_response_times": {"http://************:11434": [0.4419581890106201, 8.546696186065674, 15.417922258377075, 12.164761304855347, 5.147516489028931, 15.463821172714233, 1.1653144359588623, 7.260696172714233, 0.6072688102722168, 1.2703227996826172, 25.129063844680786, 40.444517612457275, 1.5070714950561523, 0.4231138229370117, 4.638781547546387, 0.3221919536590576, 4.879332780838013, 0.8547573089599609, 6.621678590774536, 0.5162334442138672, 25.15186882019043, 8.465997219085693, 1.114194393157959, 24.00834035873413, 0.9904773235321045, 14.467668056488037, 2.1995954513549805, 0.3159515857696533, 0.43117189407348633, 9.187590599060059, 4.177763223648071, 6.659513235092163, 0.10174036026000977, 27.21884775161743, 18.188387632369995, 301.31502866744995, 44.06521201133728, 5.537399530410767], "http://************:11434": [-0.8523826599121094, 7.381824970245361, -0.3877279758453369, 4.040196657180786, 58.52020192146301, 89.66045188903809, 4.975370407104492, 2.35306978225708, 2.5122764110565186, 48.20158934593201, 2.2298800945281982, 3.8133952617645264, 2.3554446697235107, 0.37918615341186523, 0.3618583679199219, 0.3296229839324951, 3.8296689987182617, 8.629430294036865, 10.740895986557007, 7.762277126312256, 4.787411212921143, 8.067044734954834, 12.760895729064941, 4.750929594039917, 1.5877985954284668, 0.3804447650909424, 9.100614070892334, 3.814056396484375], "http://************:11434": [48.02816462516785, 56.0153546333313, 52.720155477523804, 18.4600567817688, 15.490460634231567, 14.013548374176025, 19.398632764816284, 13.16027545928955, 3.0255918502807617, 35.2440242767334, 36.21790075302124, 13.757683515548706, 23.960748195648193, 10.550098180770874, 3.898238182067871, 0.7113022804260254, 7.1972105503082275, 21.889899969100952, 20.651923179626465, 13.79568076133728], "http://************:11434": [11.191508293151855, 3.579657554626465, 4.0648157596588135, 1.4524481296539307, 0.8863584995269775, 0.5724189281463623, 0.5644886493682861, 5.51922345161438, 4.4136927127838135, 3.40250301361084, 4.134925603866577, 8.091779470443726, 0.9088726043701172, 13.775399208068848, 5.974713087081909, 2.395066499710083, 6.104565382003784, 32.67900538444519, 6.220295190811157, 4.87405252456665, 1.1575684547424316, -0.04937386512756348, 3.896766424179077, 2.821174383163452, 5.953388690948486, 5.519206285476685, 4.56959867477417, 2.067125082015991, 3.766080379486084, 0.5877985954284668, 0.616471529006958, 0.7179458141326904, 0.6144704818725586, 2.670806407928467, 9.772827625274658, 1.2902545928955078, 5.954418659210205, 14.544468402862549, 13.044536828994751, 23.921113967895508, 20.32419729232788, 9.94236969947815, 11.420101881027222, 62.68362379074097, 319.766321182251, 3.3910105228424072, 0]}, "model_request_count": {"llama3.2:3b": 14, "granite3.3:8b": 6, "llama3:latest": 66, "tinyllama:latest": 17, "dolphin-mistral:7b": 2, "deepseek-r1:1.5b": 8, "phi:latest": 23, "smollm2:1.7b": 2, "smollm:135m": 2, "smollm2:latest": 3, "smollm:360m": 2, "llama3.1:latest": 1, "gemma3:latest": 7, "codellama:7b": 1, "codellama:7b-instruct": 1, "Deepcoder:latest": 1, "deepseek-coder:6.7b": 1, "deepseek-r1:8b": 11}, "model_error_count": {"llama3.2:3b": 1, "phi:latest": 2, "smollm2:latest": 2, "smollm:360m": 2, "deepseek-r1:8b": 7}, "model_host_usage": {"llama3.2:3b": {"http://************:11434": 13}, "granite3.3:8b": {"http://************:11434": 6}, "llama3:latest": {"http://************:11434": 11, "http://************:11434": 7, "http://************:11434": 4, "http://************:11434": 24}, "tinyllama:latest": {"http://************:11434": 4, "http://************:11434": 5, "http://************:11434": 4, "http://************:11434": 1}, "dolphin-mistral:7b": {"http://************:11434": 2}, "deepseek-r1:1.5b": {"http://************:11434": 5, "http://************:11434": 3}, "phi:latest": {"http://************:11434": 10, "http://************:11434": 4, "http://************:11434": 4, "http://************:11434": 3}, "smollm2:1.7b": {"http://************:11434": 2}, "smollm:135m": {"http://************:11434": 2}, "smollm2:latest": {"http://************:11434": 1}, "llama3.1:latest": {"http://************:11434": 1}, "gemma3:latest": {"http://************:11434": 7}, "codellama:7b": {"http://************:11434": 1}, "codellama:7b-instruct": {"http://************:11434": 1}, "Deepcoder:latest": {"http://************:11434": 1}, "deepseek-coder:6.7b": {"http://************:11434": 1}, "deepseek-r1:8b": {"http://************:11434": 6}}, "recent_requests": [{"timestamp": "2025-07-30 07:23:13.307175", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:23:41.291461", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:24:09.331540", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:24:37.344306", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.506111145019531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:25:05.343315", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:25:33.363370", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:26:01.372138", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:26:29.385089", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:26:57.443601", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:27:25.473076", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7418136596679688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:27:53.524973", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:28:21.451725", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.8623809814453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:28:49.517177", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:29:17.523446", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.556510925292969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:29:45.513035", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:30:13.546449", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:30:41.522062", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:31:09.447699", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.8650970458984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:31:37.465295", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.3882598876953125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:32:05.457112", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:32:33.406888", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:33:01.377471", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:33:29.321439", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:33:57.185929", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:34:25.188983", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:34:53.146841", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:35:21.168545", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:35:49.162775", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:36:17.116968", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:36:45.130653", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:37:13.068328", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:37:41.021797", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:38:08.986966", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.318092346191406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:38:36.953429", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:39:04.921491", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:39:32.899258", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:40:00.807411", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.100799560546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:40:28.855507", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.462501525878906e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:40:56.780565", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:41:24.725693", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:41:52.714414", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:42:20.667840", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.8160552978515625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:42:48.549401", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:43:16.509481", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:43:44.488567", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:44:12.461091", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:44:40.430175", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:45:08.357916", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:45:36.295773", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:46:04.259946", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:46:32.214393", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.748603820800781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:46:59.872116", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:47:28.040771", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:47:55.921852", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:48:23.788801", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:48:51.672122", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:49:19.610354", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:49:47.474208", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:50:15.399580", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:50:43.318041", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:51:11.229775", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:51:39.082824", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:52:06.821624", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:52:34.812417", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:53:02.724467", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:53:30.615250", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:53:58.505071", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:54:26.403314", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:54:54.315963", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:55:22.179185", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.869171142578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:55:50.140378", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:56:18.054000", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:56:45.889395", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.863739013671875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:57:13.804546", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:57:41.740021", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:58:09.603315", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:58:37.478075", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:59:05.420991", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 07:59:33.296505", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.078315734863281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:00:01.239343", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:00:29.138408", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7418136596679688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:00:57.058427", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:01:24.914520", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:01:52.786864", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:02:20.625432", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:02:48.486291", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:03:16.344240", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:03:44.167870", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:04:11.999051", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.4836273193359375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:04:39.878083", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:05:07.772306", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:05:35.690027", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:06:03.558491", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:06:31.373449", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:06:59.294053", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:07:27.166161", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:07:55.001822", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:08:22.894783", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.574920654296875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:08:50.838590", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:09:18.699255", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:09:46.610237", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:10:14.524478", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:10:42.393245", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:11:10.281003", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:11:38.223093", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:12:06.102877", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:12:33.925410", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:13:01.824199", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:13:29.676687", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:13:57.495915", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:14:25.379874", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:14:53.226584", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:15:21.024865", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:15:48.892084", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:16:16.798009", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:16:44.607810", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:17:12.249826", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.817413330078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:17:40.326788", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:18:08.093048", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:18:36.036210", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:19:03.929315", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:19:31.748636", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:19:59.572062", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:20:27.442142", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:20:55.270301", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:21:23.038711", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.029273986816406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:21:50.836558", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:22:18.506537", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:22:46.265487", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:23:14.050175", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:23:41.840593", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:24:08.744747", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:24:36.526939", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:25:02.859823", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:25:30.632836", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:25:58.387122", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:26:26.164050", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:26:53.932098", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:27:21.717584", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:27:49.532241", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:28:17.307161", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:28:45.126790", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:29:12.926286", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.7697296142578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:29:40.805628", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:30:08.604410", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:30:36.317235", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:31:04.085125", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:31:31.872203", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.510185241699219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:31:59.625582", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:32:27.465453", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.696846008300781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:32:55.270923", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:33:23.069255", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:33:50.869072", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:34:18.631396", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:34:46.399632", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:35:14.239883", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:35:42.014400", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:36:09.789234", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:36:37.669355", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:37:05.468940", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:37:33.303451", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:38:01.067671", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:38:28.964600", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:38:56.744557", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:39:24.480365", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:39:52.282295", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:40:20.054989", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.172325134277344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:40:47.831663", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:41:15.607460", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:41:43.379925", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:42:11.157950", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:42:39.036394", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:43:06.800825", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:43:34.542821", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:44:02.335265", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:44:30.164634", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:44:57.915430", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:45:25.719401", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:45:53.530173", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:46:21.302442", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:46:49.043638", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:47:16.794871", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:47:44.577691", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:48:12.271514", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:48:39.972395", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:49:07.723454", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:49:35.418748", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:50:03.193422", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:50:30.961525", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:50:58.657338", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:51:26.486396", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.316734313964844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:51:54.016720", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:52:21.860213", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.9604644775390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:52:49.551866", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:53:17.248722", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:53:44.958990", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.793571472167969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:54:12.638686", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:54:40.370995", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:55:08.087164", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:55:35.774049", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:56:03.493138", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:56:31.229663", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:56:58.858070", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:57:26.670442", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:57:54.417282", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.8650970458984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:58:22.068950", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:58:49.771843", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:59:17.544524", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.5299530029296875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 08:59:45.233826", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.914138793945312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:00:12.943914", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:00:40.649860", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:01:08.320018", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.890296936035156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:01:36.044789", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.316734313964844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:02:03.822988", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:02:31.514323", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:02:59.231713", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:03:26.996733", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:03:54.682379", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:04:22.403452", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:04:50.131206", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:05:17.903944", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:05:45.690788", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:06:13.387948", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7179718017578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:06:41.122237", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:07:08.827480", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:07:36.555528", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:08:04.310984", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:08:32.014804", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.937980651855469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:08:59.787533", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:09:27.543101", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:09:55.271385", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:10:22.932076", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:10:50.594363", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:11:18.223201", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:11:46.059264", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:12:13.694979", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:12:41.421533", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:13:08.935041", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.555152893066406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:13:36.768200", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:14:04.599681", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:14:32.432996", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:15:00.202573", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:15:27.997873", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.772445678710938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:15:55.750770", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:16:23.518464", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.078315734863281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:16:51.274979", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:17:19.048494", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:17:46.803284", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.322166442871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:18:14.575805", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:18:42.362539", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.316734313964844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:19:10.156936", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:19:37.916153", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:20:05.680797", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:20:33.621929", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.151199340820312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:21:01.541803", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:21:29.514873", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6941299438476562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:21:57.344238", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:22:25.003569", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:22:52.608624", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:23:20.295455", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:23:48.160436", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:24:15.925296", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:24:43.678032", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:25:11.483668", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:25:39.236016", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:26:06.989502", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:26:34.742370", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.818771362304688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:27:02.422964", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:27:30.045898", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:27:57.853333", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:28:25.611320", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:28:53.251252", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:29:20.994525", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:29:48.742337", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:30:16.347214", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:30:44.161531", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:31:11.872729", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:31:39.563715", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.274482727050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:32:07.229598", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:32:34.992635", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:33:02.710078", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:33:30.293728", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:33:58.028001", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6464462280273438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:34:25.729322", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:34:53.383816", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:35:21.127948", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.435943603515625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:35:48.804157", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6941299438476562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:36:16.415719", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:36:44.017109", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.62396240234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:37:11.923768", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:37:39.623898", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:38:07.331242", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:38:35.031146", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:39:02.655363", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:39:30.307856", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:39:58.057591", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:40:25.746507", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:40:53.393387", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:41:20.972859", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:41:48.680443", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:42:16.321663", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:42:43.969185", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:43:11.689287", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.507469177246094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:43:39.340662", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:44:06.967245", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:44:34.531532", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:45:02.322903", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:45:29.898457", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:45:57.552479", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.4836273193359375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:46:25.212899", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:46:52.846527", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:47:20.460566", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:47:48.116754", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:48:15.717322", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:48:43.332867", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:49:11.041563", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:49:38.669157", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:50:06.270384", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:50:33.854686", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:51:01.514943", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:51:29.166193", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:51:56.780282", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:52:24.437997", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:52:52.037695", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.53131103515625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:53:19.571413", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:53:47.259147", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:54:14.844281", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:54:42.398174", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:55:09.967778", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:55:37.618550", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.866455078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:56:05.247640", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:56:32.896195", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:57:00.439581", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:57:28.057638", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:57:55.730477", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:58:23.278677", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:58:50.950263", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:59:18.528804", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 09:59:46.121289", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00017642974853515625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:00:13.761145", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.937980651855469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:00:41.356411", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:01:08.932141", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.555152893066406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:01:36.534083", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.914138793945312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:02:04.125132", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:02:31.761689", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:02:59.322158", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:03:26.944672", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:03:54.484248", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:04:22.057743", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:04:49.627170", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:05:17.223496", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.0558319091796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:05:44.750659", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:06:12.317820", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:06:39.913663", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:07:07.536057", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.984306335449219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:07:35.183291", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:08:02.765135", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.152557373046875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:08:30.315887", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:08:57.916956", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:09:25.481845", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:09:53.058347", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:10:20.650528", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:10:48.236176", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:11:15.832255", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:11:43.431608", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:12:10.976619", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:12:38.534008", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.888938903808594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:13:06.168041", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:13:33.682735", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:14:01.255638", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:14:28.837449", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:14:56.362977", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:15:23.881412", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00013828277587890625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:15:51.556146", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.887580871582031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:16:19.081524", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:16:46.631047", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00014209747314453125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:17:14.219270", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.605552673339844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:17:41.753853", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:18:09.265463", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.790855407714844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:18:36.779164", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:19:04.387004", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:19:31.928999", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:19:59.484619", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:20:27.019446", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:20:54.562278", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:21:22.084568", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:21:49.606523", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:22:17.138039", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:22:44.637083", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7179718017578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:23:12.171942", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:23:39.744715", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.771087646484375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:24:07.266098", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:24:34.762686", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:25:02.331048", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.817413330078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:25:29.872588", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:25:57.376118", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00010633468627929688, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:26:24.862676", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:26:52.447040", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:27:19.997878", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:27:47.492853", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:28:15.023449", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:28:42.533443", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00014138221740722656, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:29:09.997568", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.510185241699219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:29:37.568433", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.553794860839844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:30:05.058990", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:30:32.378205", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.841255187988281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:31:00.077267", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.340576171875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:31:27.594208", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:31:55.078321", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.1484832763671875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:32:22.600256", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:32:50.116510", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:33:17.633418", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:33:45.158564", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:34:12.651578", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:34:40.163587", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.506111145019531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:35:07.659239", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:35:35.178245", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:36:02.685498", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.650520324707031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:36:30.178630", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:36:57.645343", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:37:25.208837", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:37:52.695612", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:38:20.227796", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:38:47.741604", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:39:15.241618", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.82012939453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:39:42.706556", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:40:10.216588", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:40:37.712100", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:41:05.218264", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:41:32.696338", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.678436279296875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:42:00.196562", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:42:27.700821", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:42:55.173321", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:43:22.742744", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:43:50.195414", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:44:17.690822", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:44:45.183748", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:45:11.749526", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00011610984802246094, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:45:39.227018", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:46:05.055849", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:46:32.540472", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.130073547363281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:47:00.026927", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:47:27.497017", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.887580871582031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:47:55.002178", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:48:22.498104", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:48:49.991625", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:49:17.524923", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:49:45.035098", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.699562072753906e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:50:12.582790", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:50:39.973585", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.601478576660156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:51:07.601535", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.8650970458984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:51:35.109274", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:52:02.591106", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.838539123535156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:52:30.082317", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:52:57.582492", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:53:25.113285", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:53:52.575923", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:54:20.070595", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.390975952148438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:54:47.553711", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:55:15.025497", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:55:42.530495", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:56:10.018086", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.322166442871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:56:37.443334", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:57:04.904401", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:57:32.373662", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:57:59.835793", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:58:27.320873", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:58:54.801205", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6941299438476562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:59:22.292714", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.601478576660156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 10:59:49.960534", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.981590270996094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:00:17.695483", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.982948303222656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:00:45.154616", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:01:12.661392", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:01:40.127844", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.935264587402344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:02:07.666958", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:02:35.154924", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:03:02.636689", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.794929504394531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:03:30.110916", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:03:57.616381", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:04:25.218564", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:04:52.784735", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.030632019042969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:05:20.303645", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:05:47.772314", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.647804260253906e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:06:15.222187", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:06:42.724772", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:07:10.169644", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.100799560546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:07:37.687214", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:08:05.195692", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:08:32.662260", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:09:00.096665", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.9591064453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:09:27.630879", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:09:55.093230", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:10:22.610917", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:10:50.097317", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:11:17.592185", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:11:45.069160", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:12:12.543244", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:12:39.979761", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:13:07.553936", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:13:35.025652", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.814697265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:14:02.545923", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:14:30.037649", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.9591064453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:14:57.437738", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:15:24.893107", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:15:52.352012", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:16:19.953949", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.9591064453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:16:47.448647", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:17:14.918424", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:17:42.364380", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:18:09.851631", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:18:37.380390", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:19:04.834723", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:19:32.316717", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:19:59.723688", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.458427429199219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:20:27.288845", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.459785461425781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:20:54.717291", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7179718017578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:21:22.176330", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7179718017578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:21:49.582404", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:22:17.033507", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.363059997558594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:22:44.504990", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:23:12.044466", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:23:39.443366", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:24:06.878370", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:24:34.334384", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.838539123535156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:25:01.844593", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:25:29.301409", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:25:56.754532", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:26:24.318789", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:26:51.734013", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.793571472167969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:27:19.212877", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:27:46.704969", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:28:14.168587", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:28:41.611316", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:29:09.075057", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:29:36.548338", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:30:04.044716", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.863739013671875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:30:31.432592", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.7894973754882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:30:58.858196", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:31:26.342905", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.458427429199219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:31:53.746736", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:32:21.255521", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:32:48.752216", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:33:16.244090", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:33:43.671467", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:34:11.145096", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:34:38.583823", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:35:06.081562", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:35:33.510335", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:36:00.954860", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:36:28.412829", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:36:55.859805", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.482269287109375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:37:23.280051", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:37:50.763350", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:38:18.227759", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:38:45.762589", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:39:13.162424", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:39:40.554173", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.054473876953125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:40:08.009685", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:40:35.472491", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:41:02.913363", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:41:30.406519", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:41:57.920527", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:42:25.416927", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:42:52.887869", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:43:20.329372", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:43:47.787244", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:44:15.228577", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:44:42.587811", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:45:09.976791", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:45:37.380474", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:46:04.846201", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:46:32.246977", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.459785461425781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:46:59.758387", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:47:27.135046", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:47:54.555608", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:48:21.952398", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.0531158447265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:48:49.381566", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:49:16.816835", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.814697265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:49:44.240813", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.915496826171875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 11:50:11.703495", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.7670135498046875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:04.552013", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.9577484130859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:05.562929", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009335517883300781, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:05.576592", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0005624294281005859, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:05.585535", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008174896240234375, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:31.962898", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.367134094238281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:01:59.607720", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.0558319091796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:02:27.049808", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:02:54.461150", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.341934204101562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:03:21.879062", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:03:49.313997", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.6743621826171875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:04:16.889460", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.698204040527344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:04:44.330719", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.58306884765625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:05:11.765722", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:05:39.173431", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.814697265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:06:06.656124", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.246566772460938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:06:34.075353", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.8650970458984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:07:01.729211", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.5789947509765625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:07:29.176512", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.482269287109375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:07:56.690608", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:08:24.142021", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00012230873107910156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:08:51.608967", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.482269287109375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:09:19.004105", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:09:46.416286", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.984306335449219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:10:13.869505", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.821487426757812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:10:41.262160", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:11:08.647433", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:11:36.085037", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.838539123535156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:12:03.634964", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.6716461181640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:12:31.131385", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.8160552978515625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:12:58.572938", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.650520324707031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:13:26.029444", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:13:53.457559", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00011920928955078125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:14:20.644624", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:14:48.252682", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.029273986816406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:15:15.644729", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:15:43.046706", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.316734313964844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:16:10.499673", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:16:37.951938", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00015091896057128906, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:17:05.345069", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.796287536621094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:17:32.771993", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00014495849609375, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:18:00.209437", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:18:27.646671", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.0001437664031982422, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:18:55.048654", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:19:22.459508", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:19:49.858587", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.914138793945312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:20:17.393141", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.673004150390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:20:44.781793", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.270408630371094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:21:12.189246", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.364418029785156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:21:39.584428", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:22:07.008662", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.535385131835938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:22:34.436413", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00019693374633789062, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:23:01.977247", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:23:29.467445", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.940696716308594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:23:56.816806", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.985664367675781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:16.276525", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.013486146926879883, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:16.297401", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008265018463134766, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:16.302221", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0010769367218017578, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:18.238800", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009555816650390625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:21.337867", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.10027384757995605, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:24.231015", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.584426879882812e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:24:51.666974", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.910064697265625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:25:19.065241", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:25:46.498225", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:25:59.419788", "endpoint": "/api/generate", "method": "POST", "status_code": 200, "duration": 62.68362379074097, "host": "http://************:11434", "model": "deepseek-r1:8b", "error": null, "prompt_eval_count": 26, "eval_count": 4220, "prompt_eval_duration": 199145300, "eval_duration": 56777758000, "total_duration": 64316657300}, {"timestamp": "2025-07-30 12:26:13.888372", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.630752563476562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:26:41.328937", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.557868957519531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:27:08.710127", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00013113021850585938, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:27:36.122045", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00013494491577148438, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:28:03.565160", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.0001010894775390625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:28:31.050151", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.870529174804688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:29:08.008941", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:29:35.421050", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.869171142578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:03.104284", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00011992454528808594, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:30.522968", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.510185241699219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:40.118239", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.014506340026855469, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:40.143114", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007960081100463867, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:40.150915", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.004357337951660156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:30:57.918404", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00011992454528808594, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:31:25.340426", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00016808509826660156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:31:52.765821", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.008148193359375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:32:20.222768", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.38690185546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:32:48.143968", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.863739013671875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:33:15.537556", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.632110595703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:33:42.927689", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:12.152835", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:37.796396", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:55.866534", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008409738540649414, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:55.893291", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.013602018356323242, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:55.897115", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0005192756652832031, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:34:58.775826", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.014334440231323242, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:35:00.334977", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.10317397117614746, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:35:05.202171", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:35:32.594151", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.580352783203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:36:00.037631", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.747245788574219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:36:27.432161", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.8623809814453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:36:54.854476", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.267692565917969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:37:22.254852", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:37:49.725257", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.790855407714844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:38:17.095698", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.269050598144531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:38:44.536463", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.743171691894531e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:39:11.899538", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:39:39.299981", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:40:06.686793", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:40:31.286925", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 319.766321182251, "host": "http://************:11434", "model": "deepseek-r1:8b", "error": "RemoteProtocolError"}, {"timestamp": "2025-07-30 12:40:31.287707", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 319.76805901527405, "host": null, "model": "deepseek-r1:8b", "error": "RemoteProtocolError"}, {"timestamp": "2025-07-30 12:40:34.068641", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.702278137207031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:41:01.455758", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.654594421386719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:41:06.008446", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 0.0005795955657958984, "host": null, "model": "deepseek-r1:8b", "error": "HTTPException"}, {"timestamp": "2025-07-30 12:41:23.875745", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 0.0007529258728027344, "host": null, "model": "deepseek-r1:8b", "error": "HTTPException"}, {"timestamp": "2025-07-30 12:41:28.872400", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.128715515136719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:41:34.026577", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.019033193588256836, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:41:41.700085", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.09221887588500977, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:41:54.414397", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 0.0003750324249267578, "host": null, "model": "deepseek-r1:8b", "error": "HTTPException"}, {"timestamp": "2025-07-30 12:41:56.283087", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.250640869140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:03.443554", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.011011838912963867, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:03.473743", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.012443304061889648, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:03.482282", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0004799365997314453, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:05.387421", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.010657548904418945, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:05.398697", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002617835998535156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:05.407873", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008765220642089844, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:08.409165", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00939321517944336, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:10.021808", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.10700464248657227, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:22.206922", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 0.0008020401000976562, "host": null, "model": "deepseek-r1:8b", "error": "HTTPException"}, {"timestamp": "2025-07-30 12:42:23.675930", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.58306884765625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:51.088380", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00016736984252929688, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:42:57.651826", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.013236284255981445, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:43:06.080957", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.24408698081970215, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:43:14.527368", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.01015782356262207, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:43:18.497627", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.220008850097656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:43:18.959471", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.10001111030578613, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:43:35.699629", "endpoint": "/api/generate", "method": "POST", "status_code": 200, "duration": 3.3910105228424072, "host": "http://************:11434", "model": "tinyllama:latest", "error": null, "prompt_eval_count": 35, "eval_count": 376, "prompt_eval_duration": 169818800, "eval_duration": **********, "total_duration": **********}, {"timestamp": "2025-07-30 12:43:45.941243", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.458427429199219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:44:13.336625", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 8.273124694824219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:44:40.755690", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.647804260253906e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:45:08.153224", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.198883056640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:45:35.542926", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:46:02.956062", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:46:30.362281", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:46:57.766681", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00010180473327636719, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:47:25.161991", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.723403930664062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:47:52.607314", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.866455078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:48:20.071486", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.987022399902344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:48:47.461433", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:49:14.844530", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:49:42.268625", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.771087646484375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:50:09.694417", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.793571472167969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:50:37.075323", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.553794860839844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:51:04.469038", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.508827209472656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:51:31.878040", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:51:59.253167", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:52:26.678410", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.270408630371094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:52:54.060444", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:53:21.472534", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:53:48.891721", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.365776062011719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:54:16.313206", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.790855407714844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:54:43.695698", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:55:11.069703", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.029273986816406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:55:38.447557", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:56:05.877497", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:56:33.294262", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:59:39.622006", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:59:40.843229", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008229255676269531, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:59:40.865770", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008163928985595703, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 12:59:40.869754", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002868175506591797, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:00:06.933134", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.00543212890625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:00:34.353917", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:01:01.769265", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.124641418457031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:01:29.143484", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.555152893066406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:01:56.497406", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.6716461181640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:02:23.870720", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:10.249270", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009141683578491211, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:10.270983", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008946418762207031, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:10.273812", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002300739288330078, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:10.716235", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:38.085007", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.838539123535156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:44.037185", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009300470352172852, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:44.055357", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007836103439331055, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:44.058668", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00020456314086914062, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:45.713825", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008150339126586914, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:05:46.883063", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07370495796203613, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:06:05.469321", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:06:32.860211", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:07:00.241384", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:07:27.628923", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:07:55.005861", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:08:22.370132", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:08:49.755820", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:09:17.126678", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.123283386230469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:09:44.622610", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.6716461181640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:09:45.624387", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.7951929569244385, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:20.614201", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.010433673858642578, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:20.642006", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009443521499633789, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:20.645121", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0003211498260498047, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:20.677685", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00010538101196289062, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:47.439462", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008045434951782227, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:47.459031", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007886886596679688, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:47.462151", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00037288665771484375, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:48.058926", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:48.487018", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0070476531982421875, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:13:49.894121", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07008981704711914, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:14:15.367263", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.935264587402344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:14:17.028490", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.05976510047912598, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:14:42.948753", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.2411346435546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:15:10.333989", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:15:37.720608", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:16:05.103698", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.53131103515625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:16:32.468398", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.62396240234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:16:59.829691", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:17:27.211288", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.172325134277344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:17:54.700880", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:18:22.159686", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.765655517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:18:49.531311", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:19:16.991356", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:19:44.377345", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.3603439331054688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:20:11.757641", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:20:39.101746", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.9577484130859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:21:06.472072", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.6702880859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:21:33.839084", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.172325134277344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:22:01.220415", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:22:28.586338", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.790855407714844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:24:51.388416", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:24:52.583860", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007752180099487305, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:24:52.604776", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008368253707885742, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:24:52.608156", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002574920654296875, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:25:18.734816", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:25:46.269875", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.172325134277344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:26:13.648907", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.695487976074219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:26:41.026660", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:27:08.396738", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.719329833984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:27:35.849800", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:28:03.231639", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.291534423828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:28:30.699256", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.842613220214844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:28:58.096662", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:29:25.473623", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:29:52.846115", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:30:20.236624", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:30:47.607982", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.127357482910156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:31:14.978014", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:31:42.340455", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:32:09.716839", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:32:37.122256", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0040740966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:33:04.487257", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:33:31.856899", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:33:59.228116", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.86102294921875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:34:26.629321", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0994415283203125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:34:53.991663", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.866455078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:35:21.432840", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.528594970703125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:35:48.800599", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:36:16.173749", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8133392333984375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:36:43.582724", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:37:10.973196", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:37:38.326435", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:38:05.697221", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9087066650390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:38:33.074667", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.364418029785156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:39:00.429039", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0517578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:39:27.811090", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8848648071289062e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:39:55.176364", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:40:22.540110", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.8371810913085938e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:40:50.032284", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.125999450683594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:41:17.406834", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.291534423828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:41:44.808587", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9802322387695312e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:42:12.171142", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.1484832763671875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:42:39.535564", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:43:06.909312", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:43:34.280976", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:44:01.667781", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.3855438232421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:44:29.024968", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.9325485229492188e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:44:56.382297", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.365776062011719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:45:23.753948", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.00015044212341308594, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:45:51.112330", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 6.341934204101562e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:46:18.519244", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:46:45.893676", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 0.0001552104949951172, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:47:13.284954", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.8623809814453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:47:40.696605", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 2.956390380859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:48:08.071787", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.075599670410156e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:48:35.461057", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.647804260253906e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:49:02.835004", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.337860107421875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:49:30.192235", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.314018249511719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:49:57.579228", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:50:24.938203", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.176399230957031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:50:50.225526", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.870529174804688e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:50:51.482669", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009185314178466797, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:50:51.504188", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007602214813232422, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:50:51.508280", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002980232238769531, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:51:17.624041", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.695487976074219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:51:39.730762", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007992267608642578, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:51:39.748628", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007399797439575195, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:51:39.752101", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0005414485931396484, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:51:45.002102", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.409385681152344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:52:12.392368", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.2901763916015625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:53:53.290887", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.291534423828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:53:54.442283", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008478164672851562, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:53:54.465170", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008679389953613281, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:53:54.469184", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0003859996795654297, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:54:10.029537", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007705211639404297, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:54:10.050857", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008083581924438477, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:54:10.054170", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002925395965576172, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:54:20.658304", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.030632019042969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:54:48.165185", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.9577484130859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:55:15.539839", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.2928924560546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:55:42.944877", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:10.386900", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.6716461181640625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:37.758120", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.457069396972656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:43.425179", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00758051872253418, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:43.441906", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.006877422332763672, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:43.445562", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0001952648162841797, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:48.205476", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007160186767578125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:48.225240", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008658885955810547, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:56:48.228348", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00020766258239746094, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:05.114711", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.340576171875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:30.469603", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.62396240234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:31.171833", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009223699569702148, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:31.194134", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008420705795288086, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:31.197970", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0004367828369140625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:57:57.835055", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.076957702636719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:58:25.262056", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.363059997558594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:58:52.652146", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.9591064453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:59:20.007645", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.935264587402344e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 13:59:47.411002", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:02.120344", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007955312728881836, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:02.169052", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009169578552246094, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:02.172290", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002529621124267578, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:09.121953", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007774829864501953, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:09.197711", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009002447128295898, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:09.201458", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00017642974853515625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:14.767708", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.1948089599609375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:25.074261", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008591890335083008, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:26.463918", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.0738368034362793, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:00:42.121582", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:01:09.471052", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.0279159545898438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:47.067412", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:48.649030", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.011780977249145508, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:48.674488", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.013661861419677734, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:48.677879", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00019073486328125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:59.725492", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008112192153930664, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:59.744555", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008782148361206055, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:09:59.747707", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00023245811462402344, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:10:01.694298", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0072422027587890625, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:10:03.413721", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.08251380920410156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:10:14.444725", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:12:55.688319", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.695487976074219e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:12:56.275998", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.011186361312866211, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:12:56.300546", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009172677993774414, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:12:56.304598", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0005865097045898438, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:13:22.244973", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.0073397159576416016, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:13:23.104435", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.459785461425781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:13:23.836449", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.06316924095153809, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:13:50.720558", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.00543212890625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:14:17.900161", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:14:45.270949", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.24249267578125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:03.202593", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.91278076171875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:03.549599", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.011472225189208984, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:03.575778", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0005099773406982422, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:03.585764", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009166479110717773, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:30.565303", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.2438507080078125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:17:57.933762", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.555152893066406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:18:25.389354", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4809112548828125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:18:52.781773", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:19:20.145723", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.361701965332031e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:19:47.505214", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.886222839355469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:20:14.909012", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.790855407714844e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:20:42.305300", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.4332275390625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:21:09.681138", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 9.679794311523438e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:21:37.045306", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:22:04.414560", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.504753112792969e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:22:31.773305", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.43865966796875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:22:59.167039", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.8623809814453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:23:26.561289", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.147125244140625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:23:53.934907", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:24:14.944359", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008888006210327148, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:26:35.362491", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.9577484130859375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:26:35.777526", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.01035761833190918, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:26:35.801496", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008949041366577148, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:26:35.806062", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0003247261047363281, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:26:58.607782", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.01141214370727539, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:27:01.725901", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 1.0810925960540771, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:27:02.807271", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.8623809814453125e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:27:13.274556", "endpoint": "/api/generate", "method": "POST", "status_code": 500, "duration": 0, "host": "http://************:11434", "model": "deepseek-r1:8b", "error": "UnboundLocalError"}, {"timestamp": "2025-07-30 14:27:30.140354", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.600120544433594e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:27:48.181362", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.010050773620605469, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:29:49.223380", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 7.724761962890625e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:29:49.285361", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009212732315063477, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:29:49.300151", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00047135353088378906, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:29:49.310545", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009618759155273438, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:30:07.650844", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008445024490356445, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:30:09.175845", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.08147621154785156, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:30:16.582216", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:30:36.031732", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07912230491638184, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:30:43.966684", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.552436828613281e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:31:11.335034", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.266334533691406e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:20.532798", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 5.2928924560546875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:20.871445", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009115934371948242, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:20.892812", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007598400115966797, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:20.895900", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002071857452392578, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:38.971564", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007760763168334961, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:40.836342", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07593846321105957, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:32:47.895814", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.933906555175781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:33:15.262797", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.5762786865234375e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:33:42.631316", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.076957702636719e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:34:09.996327", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:24.291246", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.009383440017700195, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:24.307808", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00023102760314941406, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:24.315431", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.007117033004760742, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:24.423644", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.220008850097656e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:51.798632", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 4.649162292480469e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:56.796870", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00888371467590332, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:56.819987", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008780956268310547, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:56.822805", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.00018644332885742188, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:58.143615", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00911569595336914, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:35:59.998496", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07866549491882324, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:19.277796", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.7670135498046875e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:23.466580", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07825732231140137, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:28.717436", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008228540420532227, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:28.736375", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.00799703598022461, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:28.739561", "endpoint": "/proxy/status", "method": "GET", "status_code": 200, "duration": 0.0002155303955078125, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:30.681118", "endpoint": "/proxy/metrics", "method": "GET", "status_code": 200, "duration": 0.008250951766967773, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:32.500661", "endpoint": "/api/tags", "method": "GET", "status_code": 200, "duration": 0.07563233375549316, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:36:46.665962", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.170967102050781e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:37:14.096302", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.218650817871094e-05, "host": null, "model": null, "error": null}, {"timestamp": "2025-07-30 14:37:41.460210", "endpoint": "/proxy/health", "method": "GET", "status_code": 200, "duration": 3.62396240234375e-05, "host": null, "model": null, "error": null}], "health_check_count": 8945, "health_check_errors": 109, "health_checks": {"http://************:11434": [{"timestamp": "2025-07-30 13:52:09.347604", "healthy": true, "response_time": 0.01599597930908203}, {"timestamp": "2025-07-30 13:54:17.127462", "healthy": true, "response_time": 0.017882108688354492}, {"timestamp": "2025-07-30 13:54:44.474747", "healthy": true, "response_time": 0.016339778900146484}, {"timestamp": "2025-07-30 13:55:11.815616", "healthy": true, "response_time": 0.014955282211303711}, {"timestamp": "2025-07-30 13:55:39.171028", "healthy": true, "response_time": 0.027489185333251953}, {"timestamp": "2025-07-30 13:56:06.665044", "healthy": true, "response_time": 0.018435955047607422}, {"timestamp": "2025-07-30 13:56:34.245047", "healthy": true, "response_time": 0.01842021942138672}, {"timestamp": "2025-07-30 13:57:01.604882", "healthy": true, "response_time": 0.02904796600341797}, {"timestamp": "2025-07-30 13:57:54.336263", "healthy": true, "response_time": 0.014944076538085938}, {"timestamp": "2025-07-30 13:58:21.688588", "healthy": true, "response_time": 0.017429828643798828}, {"timestamp": "2025-07-30 13:58:49.056283", "healthy": true, "response_time": 0.01669001579284668}, {"timestamp": "2025-07-30 13:59:16.404558", "healthy": true, "response_time": 0.015813589096069336}, {"timestamp": "2025-07-30 13:59:43.751068", "healthy": true, "response_time": 0.017752408981323242}, {"timestamp": "2025-07-30 14:00:11.150060", "healthy": true, "response_time": 0.01601862907409668}, {"timestamp": "2025-07-30 14:00:38.491307", "healthy": true, "response_time": 0.016958236694335938}, {"timestamp": "2025-07-30 14:01:05.831278", "healthy": true, "response_time": 0.01772284507751465}, {"timestamp": "2025-07-30 14:10:10.739409", "healthy": true, "response_time": 0.01485586166381836}, {"timestamp": "2025-07-30 14:13:19.516343", "healthy": true, "response_time": 0.014250993728637695}, {"timestamp": "2025-07-30 14:13:47.080194", "healthy": true, "response_time": 0.016634225845336914}, {"timestamp": "2025-07-30 14:14:14.239503", "healthy": true, "response_time": 0.01616072654724121}, {"timestamp": "2025-07-30 14:14:41.588951", "healthy": true, "response_time": 0.01625847816467285}, {"timestamp": "2025-07-30 14:17:26.816894", "healthy": true, "response_time": 0.028224706649780273}, {"timestamp": "2025-07-30 14:17:54.178570", "healthy": true, "response_time": 0.01667952537536621}, {"timestamp": "2025-07-30 14:18:21.553642", "healthy": true, "response_time": 0.01626729965209961}, {"timestamp": "2025-07-30 14:18:48.903187", "healthy": true, "response_time": 0.015909194946289062}, {"timestamp": "2025-07-30 14:19:16.255354", "healthy": true, "response_time": 0.01638340950012207}, {"timestamp": "2025-07-30 14:19:43.664324", "healthy": true, "response_time": 0.0151519775390625}, {"timestamp": "2025-07-30 14:20:11.010701", "healthy": true, "response_time": 0.016672849655151367}, {"timestamp": "2025-07-30 14:20:38.933129", "healthy": true, "response_time": 0.01790761947631836}, {"timestamp": "2025-07-30 14:21:06.282970", "healthy": true, "response_time": 0.017885446548461914}, {"timestamp": "2025-07-30 14:21:33.638235", "healthy": true, "response_time": 0.016440391540527344}, {"timestamp": "2025-07-30 14:22:01.231556", "healthy": true, "response_time": 0.016277074813842773}, {"timestamp": "2025-07-30 14:22:28.557897", "healthy": true, "response_time": 0.01741194725036621}, {"timestamp": "2025-07-30 14:22:55.910583", "healthy": true, "response_time": 0.019030094146728516}, {"timestamp": "2025-07-30 14:23:23.259745", "healthy": true, "response_time": 0.014153242111206055}, {"timestamp": "2025-07-30 14:23:50.607547", "healthy": true, "response_time": 0.0175173282623291}, {"timestamp": "2025-07-30 14:26:59.198292", "healthy": true, "response_time": 0.01778244972229004}, {"timestamp": "2025-07-30 14:27:28.037002", "healthy": true, "response_time": 0.0164949893951416}, {"timestamp": "2025-07-30 14:30:12.810492", "healthy": true, "response_time": 0.014188051223754883}, {"timestamp": "2025-07-30 14:30:40.147337", "healthy": true, "response_time": 0.014970779418945312}, {"timestamp": "2025-07-30 14:31:07.482665", "healthy": true, "response_time": 0.018302202224731445}, {"timestamp": "2025-07-30 14:32:46.936969", "healthy": true, "response_time": 0.026218414306640625}, {"timestamp": "2025-07-30 14:33:14.285306", "healthy": true, "response_time": 0.015491962432861328}, {"timestamp": "2025-07-30 14:33:41.626924", "healthy": true, "response_time": 0.014185428619384766}, {"timestamp": "2025-07-30 14:34:08.972381", "healthy": true, "response_time": 0.016410350799560547}, {"timestamp": "2025-07-30 14:35:48.073573", "healthy": true, "response_time": 0.01761007308959961}, {"timestamp": "2025-07-30 14:36:15.440351", "healthy": true, "response_time": 0.016139984130859375}, {"timestamp": "2025-07-30 14:36:42.783420", "healthy": true, "response_time": 0.01619863510131836}, {"timestamp": "2025-07-30 14:37:10.175596", "healthy": true, "response_time": 0.015711307525634766}, {"timestamp": "2025-07-30 14:37:37.516991", "healthy": true, "response_time": 0.015753507614135742}], "http://************:11434": [{"timestamp": "2025-07-30 13:52:09.365354", "healthy": true, "response_time": 0.01770925521850586}, {"timestamp": "2025-07-30 13:54:17.142796", "healthy": true, "response_time": 0.015299558639526367}, {"timestamp": "2025-07-30 13:54:44.491995", "healthy": true, "response_time": 0.01720714569091797}, {"timestamp": "2025-07-30 13:55:11.833586", "healthy": true, "response_time": 0.01793837547302246}, {"timestamp": "2025-07-30 13:55:39.214327", "healthy": true, "response_time": 0.04317617416381836}, {"timestamp": "2025-07-30 13:56:06.708385", "healthy": true, "response_time": 0.043306589126586914}, {"timestamp": "2025-07-30 13:56:34.262747", "healthy": true, "response_time": 0.017613649368286133}, {"timestamp": "2025-07-30 13:57:01.625160", "healthy": true, "response_time": 0.02024698257446289}, {"timestamp": "2025-07-30 13:57:54.353714", "healthy": true, "response_time": 0.017416715621948242}, {"timestamp": "2025-07-30 13:58:21.712419", "healthy": true, "response_time": 0.02377033233642578}, {"timestamp": "2025-07-30 13:58:49.073473", "healthy": true, "response_time": 0.01715826988220215}, {"timestamp": "2025-07-30 13:59:16.421596", "healthy": true, "response_time": 0.01700282096862793}, {"timestamp": "2025-07-30 13:59:43.768097", "healthy": true, "response_time": 0.016995668411254883}, {"timestamp": "2025-07-30 14:00:11.168254", "healthy": true, "response_time": 0.018163681030273438}, {"timestamp": "2025-07-30 14:00:38.509137", "healthy": true, "response_time": 0.017795324325561523}, {"timestamp": "2025-07-30 14:01:05.851054", "healthy": true, "response_time": 0.01973438262939453}, {"timestamp": "2025-07-30 14:10:10.755955", "healthy": true, "response_time": 0.016510725021362305}, {"timestamp": "2025-07-30 14:13:19.547039", "healthy": true, "response_time": 0.030658960342407227}, {"timestamp": "2025-07-30 14:13:47.099259", "healthy": true, "response_time": 0.019030094146728516}, {"timestamp": "2025-07-30 14:14:14.257018", "healthy": true, "response_time": 0.017485380172729492}, {"timestamp": "2025-07-30 14:14:41.607532", "healthy": true, "response_time": 0.018540143966674805}, {"timestamp": "2025-07-30 14:17:26.835033", "healthy": true, "response_time": 0.018099308013916016}, {"timestamp": "2025-07-30 14:17:54.210181", "healthy": true, "response_time": 0.03155827522277832}, {"timestamp": "2025-07-30 14:18:21.572955", "healthy": true, "response_time": 0.019283533096313477}, {"timestamp": "2025-07-30 14:18:48.926326", "healthy": true, "response_time": 0.02310776710510254}, {"timestamp": "2025-07-30 14:19:16.274188", "healthy": true, "response_time": 0.0187985897064209}, {"timestamp": "2025-07-30 14:19:43.681866", "healthy": true, "response_time": 0.01751112937927246}, {"timestamp": "2025-07-30 14:20:11.116618", "healthy": true, "response_time": 0.10587239265441895}, {"timestamp": "2025-07-30 14:20:38.951561", "healthy": true, "response_time": 0.018399953842163086}, {"timestamp": "2025-07-30 14:21:06.301984", "healthy": true, "response_time": 0.018959760665893555}, {"timestamp": "2025-07-30 14:21:33.684642", "healthy": true, "response_time": 0.04637479782104492}, {"timestamp": "2025-07-30 14:22:01.247924", "healthy": true, "response_time": 0.016335725784301758}, {"timestamp": "2025-07-30 14:22:28.575081", "healthy": true, "response_time": 0.017127037048339844}, {"timestamp": "2025-07-30 14:22:55.926207", "healthy": true, "response_time": 0.015595197677612305}, {"timestamp": "2025-07-30 14:23:23.281101", "healthy": true, "response_time": 0.021323204040527344}, {"timestamp": "2025-07-30 14:23:50.625599", "healthy": true, "response_time": 0.01801776885986328}, {"timestamp": "2025-07-30 14:26:59.712867", "healthy": true, "response_time": 0.5145370960235596}, {"timestamp": "2025-07-30 14:27:28.062684", "healthy": true, "response_time": 0.025640249252319336}, {"timestamp": "2025-07-30 14:30:12.826583", "healthy": true, "response_time": 0.01604318618774414}, {"timestamp": "2025-07-30 14:30:40.159513", "healthy": true, "response_time": 0.012140512466430664}, {"timestamp": "2025-07-30 14:31:07.501936", "healthy": true, "response_time": 0.019221782684326172}, {"timestamp": "2025-07-30 14:32:46.953859", "healthy": true, "response_time": 0.01684856414794922}, {"timestamp": "2025-07-30 14:33:14.301793", "healthy": true, "response_time": 0.01645493507385254}, {"timestamp": "2025-07-30 14:33:41.643588", "healthy": true, "response_time": 0.01663041114807129}, {"timestamp": "2025-07-30 14:34:08.989175", "healthy": true, "response_time": 0.016757965087890625}, {"timestamp": "2025-07-30 14:35:48.104479", "healthy": true, "response_time": 0.030865907669067383}, {"timestamp": "2025-07-30 14:36:15.457306", "healthy": true, "response_time": 0.016921520233154297}, {"timestamp": "2025-07-30 14:36:42.826882", "healthy": true, "response_time": 0.04342770576477051}, {"timestamp": "2025-07-30 14:37:10.192285", "healthy": true, "response_time": 0.016652345657348633}, {"timestamp": "2025-07-30 14:37:37.533260", "healthy": true, "response_time": 0.016224384307861328}], "http://************:11434": [{"timestamp": "2025-07-30 13:52:09.382664", "healthy": true, "response_time": 0.01727771759033203}, {"timestamp": "2025-07-30 13:54:17.158864", "healthy": true, "response_time": 0.016031742095947266}, {"timestamp": "2025-07-30 13:54:44.508143", "healthy": true, "response_time": 0.01612091064453125}, {"timestamp": "2025-07-30 13:55:11.852407", "healthy": true, "response_time": 0.018791913986206055}, {"timestamp": "2025-07-30 13:55:39.319411", "healthy": true, "response_time": 0.10503101348876953}, {"timestamp": "2025-07-30 13:56:06.803843", "healthy": true, "response_time": 0.09542274475097656}, {"timestamp": "2025-07-30 13:56:34.281448", "healthy": true, "response_time": 0.018671274185180664}, {"timestamp": "2025-07-30 13:57:01.643587", "healthy": true, "response_time": 0.018390893936157227}, {"timestamp": "2025-07-30 13:57:54.371376", "healthy": true, "response_time": 0.017632722854614258}, {"timestamp": "2025-07-30 13:58:21.731236", "healthy": true, "response_time": 0.018783092498779297}, {"timestamp": "2025-07-30 13:58:49.090824", "healthy": true, "response_time": 0.01732015609741211}, {"timestamp": "2025-07-30 13:59:16.437373", "healthy": true, "response_time": 0.0157468318939209}, {"timestamp": "2025-07-30 13:59:43.786174", "healthy": true, "response_time": 0.018043994903564453}, {"timestamp": "2025-07-30 14:00:11.185013", "healthy": true, "response_time": 0.01673150062561035}, {"timestamp": "2025-07-30 14:00:38.525313", "healthy": true, "response_time": 0.016130447387695312}, {"timestamp": "2025-07-30 14:01:05.880639", "healthy": true, "response_time": 0.029552698135375977}, {"timestamp": "2025-07-30 14:10:10.773175", "healthy": true, "response_time": 0.01714181900024414}, {"timestamp": "2025-07-30 14:13:19.564594", "healthy": true, "response_time": 0.017512083053588867}, {"timestamp": "2025-07-30 14:13:47.116799", "healthy": true, "response_time": 0.017496585845947266}, {"timestamp": "2025-07-30 14:14:14.274653", "healthy": true, "response_time": 0.01760244369506836}, {"timestamp": "2025-07-30 14:14:41.625720", "healthy": true, "response_time": 0.018153667449951172}, {"timestamp": "2025-07-30 14:17:26.850210", "healthy": true, "response_time": 0.015142202377319336}, {"timestamp": "2025-07-30 14:17:54.228385", "healthy": true, "response_time": 0.018153905868530273}, {"timestamp": "2025-07-30 14:18:21.591453", "healthy": true, "response_time": 0.01846599578857422}, {"timestamp": "2025-07-30 14:18:48.947451", "healthy": true, "response_time": 0.021085739135742188}, {"timestamp": "2025-07-30 14:19:16.293861", "healthy": true, "response_time": 0.01963353157043457}, {"timestamp": "2025-07-30 14:19:43.699471", "healthy": true, "response_time": 0.017571449279785156}, {"timestamp": "2025-07-30 14:20:11.134259", "healthy": true, "response_time": 0.017611265182495117}, {"timestamp": "2025-07-30 14:20:38.969847", "healthy": true, "response_time": 0.018198490142822266}, {"timestamp": "2025-07-30 14:21:06.320134", "healthy": true, "response_time": 0.018092870712280273}, {"timestamp": "2025-07-30 14:21:33.728220", "healthy": true, "response_time": 0.04354047775268555}, {"timestamp": "2025-07-30 14:22:01.264168", "healthy": true, "response_time": 0.01621246337890625}, {"timestamp": "2025-07-30 14:22:28.592253", "healthy": true, "response_time": 0.017091751098632812}, {"timestamp": "2025-07-30 14:22:55.945285", "healthy": true, "response_time": 0.01904463768005371}, {"timestamp": "2025-07-30 14:23:23.297783", "healthy": true, "response_time": 0.016652345657348633}, {"timestamp": "2025-07-30 14:23:50.643593", "healthy": true, "response_time": 0.017963171005249023}, {"timestamp": "2025-07-30 14:27:00.183240", "healthy": true, "response_time": 0.4703226089477539}, {"timestamp": "2025-07-30 14:27:28.082147", "healthy": true, "response_time": 0.01942729949951172}, {"timestamp": "2025-07-30 14:30:12.840728", "healthy": true, "response_time": 0.014110803604125977}, {"timestamp": "2025-07-30 14:30:40.172895", "healthy": true, "response_time": 0.013354301452636719}, {"timestamp": "2025-07-30 14:31:07.520931", "healthy": true, "response_time": 0.01894974708557129}, {"timestamp": "2025-07-30 14:32:46.972858", "healthy": true, "response_time": 0.018962860107421875}, {"timestamp": "2025-07-30 14:33:14.314141", "healthy": true, "response_time": 0.012317419052124023}, {"timestamp": "2025-07-30 14:33:41.661989", "healthy": true, "response_time": 0.018367767333984375}, {"timestamp": "2025-07-30 14:34:09.006889", "healthy": true, "response_time": 0.017681360244750977}, {"timestamp": "2025-07-30 14:35:48.121369", "healthy": true, "response_time": 0.01685357093811035}, {"timestamp": "2025-07-30 14:36:15.475625", "healthy": true, "response_time": 0.018290996551513672}, {"timestamp": "2025-07-30 14:36:42.852170", "healthy": true, "response_time": 0.025244951248168945}, {"timestamp": "2025-07-30 14:37:10.209427", "healthy": true, "response_time": 0.0171048641204834}, {"timestamp": "2025-07-30 14:37:37.551360", "healthy": true, "response_time": 0.01804351806640625}], "http://************:11434": [{"timestamp": "2025-07-30 13:52:09.402523", "healthy": true, "response_time": 0.01982712745666504}, {"timestamp": "2025-07-30 13:54:17.177855", "healthy": true, "response_time": 0.018965721130371094}, {"timestamp": "2025-07-30 13:54:44.527666", "healthy": true, "response_time": 0.019498348236083984}, {"timestamp": "2025-07-30 13:55:11.871146", "healthy": true, "response_time": 0.018676042556762695}, {"timestamp": "2025-07-30 13:55:39.371972", "healthy": true, "response_time": 0.052526235580444336}, {"timestamp": "2025-07-30 13:56:06.896561", "healthy": true, "response_time": 0.09268450736999512}, {"timestamp": "2025-07-30 13:56:34.301666", "healthy": true, "response_time": 0.0201871395111084}, {"timestamp": "2025-07-30 13:57:01.666144", "healthy": true, "response_time": 0.02251458168029785}, {"timestamp": "2025-07-30 13:57:54.393099", "healthy": true, "response_time": 0.021691560745239258}, {"timestamp": "2025-07-30 13:58:21.763162", "healthy": true, "response_time": 0.031876564025878906}, {"timestamp": "2025-07-30 13:58:49.111430", "healthy": true, "response_time": 0.020572423934936523}, {"timestamp": "2025-07-30 13:59:16.464420", "healthy": true, "response_time": 0.027021169662475586}, {"timestamp": "2025-07-30 13:59:43.807712", "healthy": true, "response_time": 0.021515846252441406}, {"timestamp": "2025-07-30 14:00:11.204047", "healthy": true, "response_time": 0.01900506019592285}, {"timestamp": "2025-07-30 14:00:38.544484", "healthy": true, "response_time": 0.019145488739013672}, {"timestamp": "2025-07-30 14:01:05.900172", "healthy": true, "response_time": 0.019499540328979492}, {"timestamp": "2025-07-30 14:10:10.794198", "healthy": true, "response_time": 0.020985841751098633}, {"timestamp": "2025-07-30 14:13:19.592046", "healthy": true, "response_time": 0.027416229248046875}, {"timestamp": "2025-07-30 14:13:47.141266", "healthy": true, "response_time": 0.02443408966064453}, {"timestamp": "2025-07-30 14:14:14.297110", "healthy": true, "response_time": 0.022417545318603516}, {"timestamp": "2025-07-30 14:14:41.645497", "healthy": true, "response_time": 0.019741535186767578}, {"timestamp": "2025-07-30 14:17:26.884788", "healthy": true, "response_time": 0.03455233573913574}, {"timestamp": "2025-07-30 14:17:54.259884", "healthy": true, "response_time": 0.03145790100097656}, {"timestamp": "2025-07-30 14:18:21.611385", "healthy": true, "response_time": 0.01989889144897461}, {"timestamp": "2025-07-30 14:18:48.966585", "healthy": true, "response_time": 0.019098997116088867}, {"timestamp": "2025-07-30 14:19:16.317614", "healthy": true, "response_time": 0.023717403411865234}, {"timestamp": "2025-07-30 14:19:43.720566", "healthy": true, "response_time": 0.021059036254882812}, {"timestamp": "2025-07-30 14:20:11.642808", "healthy": true, "response_time": 0.5085122585296631}, {"timestamp": "2025-07-30 14:20:38.987786", "healthy": true, "response_time": 0.017898082733154297}, {"timestamp": "2025-07-30 14:21:06.345803", "healthy": true, "response_time": 0.02563762664794922}, {"timestamp": "2025-07-30 14:21:33.810880", "healthy": true, "response_time": 0.0826265811920166}, {"timestamp": "2025-07-30 14:22:01.283729", "healthy": true, "response_time": 0.01953887939453125}, {"timestamp": "2025-07-30 14:22:28.611316", "healthy": true, "response_time": 0.01903390884399414}, {"timestamp": "2025-07-30 14:22:55.965044", "healthy": true, "response_time": 0.01972055435180664}, {"timestamp": "2025-07-30 14:23:23.316900", "healthy": true, "response_time": 0.019084930419921875}, {"timestamp": "2025-07-30 14:23:50.668726", "healthy": true, "response_time": 0.025098085403442383}, {"timestamp": "2025-07-30 14:27:00.817184", "healthy": true, "response_time": 0.6338982582092285}, {"timestamp": "2025-07-30 14:27:28.102577", "healthy": true, "response_time": 0.020394563674926758}, {"timestamp": "2025-07-30 14:30:12.857236", "healthy": true, "response_time": 0.01646900177001953}, {"timestamp": "2025-07-30 14:30:40.188568", "healthy": true, "response_time": 0.01564931869506836}, {"timestamp": "2025-07-30 14:31:07.561124", "healthy": true, "response_time": 0.040154218673706055}, {"timestamp": "2025-07-30 14:32:46.991993", "healthy": true, "response_time": 0.01909947395324707}, {"timestamp": "2025-07-30 14:33:14.337174", "healthy": true, "response_time": 0.02300119400024414}, {"timestamp": "2025-07-30 14:33:41.681772", "healthy": true, "response_time": 0.01974773406982422}, {"timestamp": "2025-07-30 14:34:09.030629", "healthy": true, "response_time": 0.023712635040283203}, {"timestamp": "2025-07-30 14:35:48.142116", "healthy": true, "response_time": 0.020702838897705078}, {"timestamp": "2025-07-30 14:36:15.494886", "healthy": true, "response_time": 0.01923513412475586}, {"timestamp": "2025-07-30 14:36:42.884244", "healthy": true, "response_time": 0.03203940391540527}, {"timestamp": "2025-07-30 14:37:10.228809", "healthy": true, "response_time": 0.019341707229614258}, {"timestamp": "2025-07-30 14:37:37.577991", "healthy": true, "response_time": 0.026595354080200195}]}, "total_prompt_tokens": 5326, "total_completion_tokens": 44099, "total_tokens": 49425, "total_prompt_eval_duration": 48359163345.0, "total_eval_duration": 1672251692498.0, "model_token_stats": {"llama3:latest": {"prompt_tokens": 880, "completion_tokens": 13772, "total_tokens": 14652, "prompt_eval_duration": 30199793472.0, "eval_duration": 957023646343.0, "prompt_tokens_per_sec": 29.13927212170837, "completion_tokens_per_sec": 14.390449026652448}, "llama3.2:3b": {"prompt_tokens": 229, "completion_tokens": 1022, "total_tokens": 1251, "prompt_eval_duration": 816971059.0, "eval_duration": 28562354546.0, "prompt_tokens_per_sec": 280.30368698776635, "completion_tokens_per_sec": 35.78136383518583}, "granite3.3:8b": {"prompt_tokens": 137, "completion_tokens": 235, "total_tokens": 372, "prompt_eval_duration": **********.0, "eval_duration": 11178358043.0, "prompt_tokens_per_sec": 107.26600273640898, "completion_tokens_per_sec": 21.022765516726256}, "tinyllama:latest": {"prompt_tokens": 2665, "completion_tokens": 6850, "total_tokens": 9515, "prompt_eval_duration": 4193504881.0, "eval_duration": 85181190912.0, "prompt_tokens_per_sec": 635.506593082704, "completion_tokens_per_sec": 80.41681416589584}, "dolphin-mistral:7b": {"prompt_tokens": 62, "completion_tokens": 37, "total_tokens": 99, "prompt_eval_duration": 1072755671.0, "eval_duration": 1210160326.0, "prompt_tokens_per_sec": 57.79508016229355, "completion_tokens_per_sec": 30.574461255309735}, "deepseek-r1:1.5b": {"prompt_tokens": 114, "completion_tokens": 4709, "total_tokens": 4823, "prompt_eval_duration": 1655588100.0, "eval_duration": 118808886900.0, "prompt_tokens_per_sec": 68.8577068172935, "completion_tokens_per_sec": 39.63508221370265}, "phi:latest": {"prompt_tokens": 753, "completion_tokens": 901, "total_tokens": 1654, "prompt_eval_duration": 3762097674.0, "eval_duration": 12586802062.0, "prompt_tokens_per_sec": 200.15429296373978, "completion_tokens_per_sec": 71.5829164200612}, "smollm2:1.7b": {"prompt_tokens": 60, "completion_tokens": 33, "total_tokens": 93, "prompt_eval_duration": 308165237.0, "eval_duration": 465713846.0, "prompt_tokens_per_sec": 194.70074101836477, "completion_tokens_per_sec": 70.85896260855426}, "smollm:135m": {"prompt_tokens": 20, "completion_tokens": 19, "total_tokens": 39, "prompt_eval_duration": 72452995.0, "eval_duration": 124512954.0, "prompt_tokens_per_sec": 276.0410387451892, "completion_tokens_per_sec": 152.5945645783972}, "smollm2:latest": {"prompt_tokens": 30, "completion_tokens": 10, "total_tokens": 40, "prompt_eval_duration": 168421500.0, "eval_duration": 58118500.0, "prompt_tokens_per_sec": 178.12452685672554, "completion_tokens_per_sec": 172.06225212281805}, "llama3.1:latest": {"prompt_tokens": 11, "completion_tokens": 21, "total_tokens": 32, "prompt_eval_duration": 183495400.0, "eval_duration": 225529000.0, "prompt_tokens_per_sec": 59.94700684594818, "completion_tokens_per_sec": 93.11441100701018}, "gemma3:latest": {"prompt_tokens": 147, "completion_tokens": 4245, "total_tokens": 4392, "prompt_eval_duration": 1432854900.0, "eval_duration": 40967382200.0, "prompt_tokens_per_sec": 102.5923839182879, "completion_tokens_per_sec": 103.61902010912476}, "codellama:7b": {"prompt_tokens": 32, "completion_tokens": 268, "total_tokens": 300, "prompt_eval_duration": 737511145.0, "eval_duration": 8617590101.0, "prompt_tokens_per_sec": 43.38917481714802, "completion_tokens_per_sec": 31.099181657398724}, "codellama:7b-instruct": {"prompt_tokens": 32, "completion_tokens": 548, "total_tokens": 580, "prompt_eval_duration": 8119568.0, "eval_duration": 18508394671.0, "prompt_tokens_per_sec": 3941.0963736001713, "completion_tokens_per_sec": 29.608186433296527}, "Deepcoder:latest": {"prompt_tokens": 15, "completion_tokens": 2688, "total_tokens": 2703, "prompt_eval_duration": 927240750.0, "eval_duration": 262208681152.0, "prompt_tokens_per_sec": 16.177028457819613, "completion_tokens_per_sec": 10.251376835390857}, "deepseek-coder:6.7b": {"prompt_tokens": 81, "completion_tokens": 579, "total_tokens": 660, "prompt_eval_duration": 1122318886.0, "eval_duration": 19895651542.0, "prompt_tokens_per_sec": 72.17200121142754, "completion_tokens_per_sec": 29.10183658864968}, "deepseek-r1:8b": {"prompt_tokens": 58, "completion_tokens": 8162, "total_tokens": 8220, "prompt_eval_duration": 420673400.0, "eval_duration": 106628719400.0, "prompt_tokens_per_sec": 137.87417982691562, "completion_tokens_per_sec": 76.54598166354796}}, "host_token_stats": {"http://************:11434": {"prompt_tokens": 1045, "completion_tokens": 6431, "total_tokens": 7476, "prompt_eval_duration": 7345138000.0, "eval_duration": 239199298200.0, "prompt_tokens_per_sec": 142.27098251932094, "completion_tokens_per_sec": 26.885530385724184}, "http://************:11434": {"prompt_tokens": 922, "completion_tokens": 7391, "total_tokens": 8313, "prompt_eval_duration": 8900366745.0, "eval_duration": 418508522698.0, "prompt_tokens_per_sec": 103.59123690245194, "completion_tokens_per_sec": 17.660333300627716}, "http://************:11434": {"prompt_tokens": 891, "completion_tokens": 5292, "total_tokens": 6183, "prompt_eval_duration": 10843531700.0, "eval_duration": 243854355200.0, "prompt_tokens_per_sec": 82.16880114806139, "completion_tokens_per_sec": 21.701478309295336}, "http://************:11434": {"prompt_tokens": 884, "completion_tokens": 18483, "total_tokens": 19367, "prompt_eval_duration": 5235388100.0, "eval_duration": 210327053300.0, "prompt_tokens_per_sec": 168.8509014260089, "completion_tokens_per_sec": 87.87742570441841}}}