{"data_mtime": 1753839573, "dep_lines": [26, 18, 20, 21, 22, 23, 27, 28, 30, 117, 6, 8, 9, 10, 11, 12, 13, 14, 21, 115, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 10, 5, 10, 5, 20, 20, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.pathlib", "_pytest.reports", "_pytest.nodes", "_pytest._io", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.main", "_pytest.warning_types", "__future__", "dataclasses", "errno", "json", "os", "pathlib", "tempfile", "typing", "_pytest", "warnings", "builtins", "re", "traitlets.utils.warnings", "pprint", "functools", "sys", "logging", "_frozen_importlib", "_typeshed", "abc", "<PERSON><PERSON><PERSON><PERSON>", "enum", "pluggy", "pluggy._hooks", "pluggy._manager"], "hash": "5e69c7de0560136ad5d34a26531e654b98053806", "id": "_pytest.cacheprovider", "ignore_all": true, "interface_hash": "508d4aed49d6f45817b57ecba34be101db301ec0", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\cacheprovider.py", "plugin_data": null, "size": 22373, "suppressed": [], "version_id": "1.15.0"}