{".class": "MypyFile", "_fullname": "httpcore._async.http_proxy", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncConnectionInterface": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.interfaces.AsyncConnectionInterface", "kind": "Gdef"}, "AsyncConnectionPool": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.connection_pool.AsyncConnectionPool", "kind": "Gdef"}, "AsyncForwardHTTPConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._async.interfaces.AsyncConnectionInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection", "name": "AsyncForwardHTTPConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._async.http_proxy", "mro": ["httpcore._async.http_proxy.AsyncForwardHTTPConnection", "httpcore._async.interfaces.AsyncConnectionInterface", "httpcore._async.interfaces.AsyncRequestInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_origin", "remote_origin", "proxy_headers", "keepalive_expiry", "network_backend", "socket_options", "proxy_ssl_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_origin", "remote_origin", "proxy_headers", "keepalive_expiry", "network_backend", "socket_options", "proxy_ssl_context"], "arg_types": ["httpcore._async.http_proxy.AsyncForwardHTTPConnection", "httpcore._models.Origin", "httpcore._models.Origin", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpcore._async.http_proxy.HeadersAsMapping"}, {".class": "TypeAliasType", "args": [], "type_ref": "httpcore._async.http_proxy.HeadersAsSequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["httpcore._backends.base.AsyncNetworkBackend", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "httpcore._backends.base.SOCKET_OPTION"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncForwardHTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["httpcore._async.http_proxy.AsyncForwardHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of AsyncForwardHTTPConnection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection._connection", "name": "_connection", "type": "httpcore._async.connection.AsyncHTTPConnection"}}, "_proxy_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection._proxy_headers", "name": "_proxy_headers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_proxy_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection._proxy_origin", "name": "_proxy_origin", "type": "httpcore._models.Origin"}}, "_remote_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection._remote_origin", "name": "_remote_origin", "type": "httpcore._models.Origin"}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncForwardHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aclose of AsyncForwardHTTPConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_handle_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection.can_handle_request", "name": "can_handle_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "arg_types": ["httpcore._async.http_proxy.AsyncForwardHTTPConnection", "httpcore._models.Origin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_handle_request of AsyncForwardHTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_async_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection.handle_async_request", "name": "handle_async_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpcore._async.http_proxy.AsyncForwardHTTPConnection", "httpcore._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_async_request of AsyncForwardHTTPConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "httpcore._models.Response"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_expired": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection.has_expired", "name": "has_expired", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncForwardHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_expired of AsyncForwardHTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncForwardHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of AsyncForwardHTTPConnection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_available": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection.is_available", "name": "is_available", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncForwardHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_available of AsyncForwardHTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection.is_closed", "name": "is_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncForwardHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_closed of AsyncForwardHTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_idle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection.is_idle", "name": "is_idle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncForwardHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_idle of AsyncForwardHTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._async.http_proxy.AsyncForwardHTTPConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._async.http_proxy.AsyncForwardHTTPConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncHTTP11Connection": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.http11.AsyncHTTP11Connection", "kind": "Gdef"}, "AsyncHTTPConnection": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.connection.AsyncHTTPConnection", "kind": "Gdef"}, "AsyncHTTPProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._async.connection_pool.AsyncConnectionPool"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._async.http_proxy.AsyncHTTPProxy", "name": "AsyncHTTPProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncHTTPProxy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._async.http_proxy", "mro": ["httpcore._async.http_proxy.AsyncHTTPProxy", "httpcore._async.connection_pool.AsyncConnectionPool", "httpcore._async.interfaces.AsyncRequestInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_url", "proxy_auth", "proxy_headers", "ssl_context", "proxy_ssl_context", "max_connections", "max_keepalive_connections", "keepalive_expiry", "http1", "http2", "retries", "local_address", "uds", "network_backend", "socket_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncHTTPProxy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_url", "proxy_auth", "proxy_headers", "ssl_context", "proxy_ssl_context", "max_connections", "max_keepalive_connections", "keepalive_expiry", "http1", "http2", "retries", "local_address", "uds", "network_backend", "socket_options"], "arg_types": ["httpcore._async.http_proxy.AsyncHTTPProxy", {".class": "UnionType", "items": ["httpcore._models.URL", "builtins.bytes", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpcore._async.http_proxy.HeadersAsMapping"}, {".class": "TypeAliasType", "args": [], "type_ref": "httpcore._async.http_proxy.HeadersAsSequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["httpcore._backends.base.AsyncNetworkBackend", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "httpcore._backends.base.SOCKET_OPTION"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncHTTPProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_proxy_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncHTTPProxy._proxy_headers", "name": "_proxy_headers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_proxy_ssl_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncHTTPProxy._proxy_ssl_context", "name": "_proxy_ssl_context", "type": {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_proxy_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncHTTPProxy._proxy_url", "name": "_proxy_url", "type": "httpcore._models.URL"}}, "create_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncHTTPProxy.create_connection", "name": "create_connection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "arg_types": ["httpcore._async.http_proxy.AsyncHTTPProxy", "httpcore._models.Origin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connection of AsyncHTTPProxy", "ret_type": "httpcore._async.interfaces.AsyncConnectionInterface", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._async.http_proxy.AsyncHTTPProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._async.http_proxy.AsyncHTTPProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncLock": {".class": "SymbolTableNode", "cross_ref": "httpcore._synchronization.AsyncLock", "kind": "Gdef"}, "AsyncNetworkBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.AsyncNetworkBackend", "kind": "Gdef"}, "AsyncTunnelHTTPConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._async.interfaces.AsyncConnectionInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection", "name": "AsyncTunnelHTTPConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "httpcore._async.http_proxy", "mro": ["httpcore._async.http_proxy.AsyncTunnelHTTPConnection", "httpcore._async.interfaces.AsyncConnectionInterface", "httpcore._async.interfaces.AsyncRequestInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_origin", "remote_origin", "ssl_context", "proxy_ssl_context", "proxy_headers", "keepalive_expiry", "http1", "http2", "network_backend", "socket_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "proxy_origin", "remote_origin", "ssl_context", "proxy_ssl_context", "proxy_headers", "keepalive_expiry", "http1", "http2", "network_backend", "socket_options"], "arg_types": ["httpcore._async.http_proxy.AsyncTunnelHTTPConnection", "httpcore._models.Origin", "httpcore._models.Origin", {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["httpcore._backends.base.AsyncNetworkBackend", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "httpcore._backends.base.SOCKET_OPTION"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncTunnelHTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["httpcore._async.http_proxy.AsyncTunnelHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of AsyncTunnelHTTPConnection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connect_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection._connect_lock", "name": "_connect_lock", "type": "httpcore._synchronization.AsyncLock"}}, "_connected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection._connected", "name": "_connected", "type": "builtins.bool"}}, "_connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection._connection", "name": "_connection", "type": "httpcore._async.interfaces.AsyncConnectionInterface"}}, "_http1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection._http1", "name": "_http1", "type": "builtins.bool"}}, "_http2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection._http2", "name": "_http2", "type": "builtins.bool"}}, "_keepalive_expiry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection._keepalive_expiry", "name": "_keepalive_expiry", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_proxy_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection._proxy_headers", "name": "_proxy_headers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_proxy_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection._proxy_origin", "name": "_proxy_origin", "type": "httpcore._models.Origin"}}, "_proxy_ssl_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection._proxy_ssl_context", "name": "_proxy_ssl_context", "type": {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_remote_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection._remote_origin", "name": "_remote_origin", "type": "httpcore._models.Origin"}}, "_ssl_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection._ssl_context", "name": "_ssl_context", "type": {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncTunnelHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aclose of AsyncTunnelHTTPConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_handle_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection.can_handle_request", "name": "can_handle_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "arg_types": ["httpcore._async.http_proxy.AsyncTunnelHTTPConnection", "httpcore._models.Origin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_handle_request of AsyncTunnelHTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_async_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection.handle_async_request", "name": "handle_async_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["httpcore._async.http_proxy.AsyncTunnelHTTPConnection", "httpcore._models.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_async_request of AsyncTunnelHTTPConnection", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "httpcore._models.Response"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_expired": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection.has_expired", "name": "has_expired", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncTunnelHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_expired of AsyncTunnelHTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncTunnelHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of AsyncTunnelHTTPConnection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_available": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection.is_available", "name": "is_available", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncTunnelHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_available of AsyncTunnelHTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection.is_closed", "name": "is_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncTunnelHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_closed of AsyncTunnelHTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_idle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection.is_idle", "name": "is_idle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["httpcore._async.http_proxy.AsyncTunnelHTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_idle of AsyncTunnelHTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "httpcore._async.http_proxy.AsyncTunnelHTTPConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ByteOrStr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpcore._async.http_proxy.ByteOrStr", "line": 27, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}}}, "HeadersAsMapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpcore._async.http_proxy.HeadersAsMapping", "line": 29, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "httpcore._async.http_proxy.ByteOrStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "httpcore._async.http_proxy.ByteOrStr"}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "HeadersAsSequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "httpcore._async.http_proxy.HeadersAsSequence", "line": 28, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "httpcore._async.http_proxy.ByteOrStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "httpcore._async.http_proxy.ByteOrStr"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "Origin": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Origin", "kind": "Gdef"}, "ProxyError": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ProxyError", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Request", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Response", "kind": "Gdef"}, "SOCKET_OPTION": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.SOCKET_OPTION", "kind": "Gdef"}, "Trace": {".class": "SymbolTableNode", "cross_ref": "httpcore._trace.Trace", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.URL", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.http_proxy.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.http_proxy.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.http_proxy.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.http_proxy.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.http_proxy.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore._async.http_proxy.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef"}, "default_ssl_context": {".class": "SymbolTableNode", "cross_ref": "httpcore._ssl.default_ssl_context", "kind": "Gdef"}, "enforce_bytes": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.enforce_bytes", "kind": "Gdef"}, "enforce_headers": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.enforce_headers", "kind": "Gdef"}, "enforce_url": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.enforce_url", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpcore._async.http_proxy.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "merge_headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["default_headers", "override_headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "httpcore._async.http_proxy.merge_headers", "name": "merge_headers", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["default_headers", "override_headers"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_headers", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\httpcore\\_async\\http_proxy.py"}