{".class": "MypyFile", "_fullname": "numpy.linalg.linalg", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EigResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.linalg.linalg.EigResult", "name": "EigResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "numpy.linalg.linalg.EigResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["eigenvalues", "eigenvectors"]}}, "module_name": "numpy.linalg.linalg", "mro": ["numpy.linalg.linalg.EigResult", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EigResult.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EigResult.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "eigenvalues", "eigenvectors"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.linalg.linalg.EigResult.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "eigenvalues", "eigenvectors"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of EigResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.EigResult._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of EigResult", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EigResult._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EigResult._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EigResult._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.linalg.linalg.EigResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of EigResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "numpy.linalg.linalg.EigResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of EigResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "eigenvalues", "eigenvectors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.EigResult._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "eigenvalues", "eigenvectors"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of EigResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EigResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EigResult._source", "name": "_source", "type": "builtins.str"}}, "eigenvalues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.linalg.linalg.EigResult.eigenvalues", "name": "eigenvalues", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "eigenvalues-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.EigResult.eigenvalues", "kind": "<PERSON><PERSON><PERSON>"}, "eigenvectors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.linalg.linalg.EigResult.eigenvectors", "name": "eigenvectors", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "eigenvectors-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.EigResult.eigenvectors", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EigResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": "numpy.linalg.linalg.EigResult"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "EighResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.linalg.linalg.Eigh<PERSON>esult", "name": "EighResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "numpy.linalg.linalg.Eigh<PERSON>esult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["eigenvalues", "eigenvectors"]}}, "module_name": "numpy.linalg.linalg", "mro": ["numpy.linalg.linalg.Eigh<PERSON>esult", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EighResult.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EighResult.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "eigenvalues", "eigenvectors"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.linalg.linalg.EighResult.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "eigenvalues", "eigenvectors"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of EighResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.EighResult._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of EighResult", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EighResult._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EighResult._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EighResult._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.linalg.linalg.EighResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of EighResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "numpy.linalg.linalg.EighResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of EighResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "eigenvalues", "eigenvectors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.EighResult._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "eigenvalues", "eigenvectors"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.EighResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.EighResult._source", "name": "_source", "type": "builtins.str"}}, "eigenvalues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.linalg.linalg.EighResult.eigenvalues", "name": "eigenvalues", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "eigenvalues-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.EighResult.eigenvalues", "kind": "<PERSON><PERSON><PERSON>"}, "eigenvectors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.linalg.linalg.EighResult.eigenvectors", "name": "eigenvectors", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "eigenvectors-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.EighResult.eigenvectors", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.EighResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": "numpy.linalg.linalg.Eigh<PERSON>esult"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LinAlgError": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.LinAlgError", "kind": "Gdef", "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "QRResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.linalg.linalg.QRResult", "name": "QRResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "numpy.linalg.linalg.QRResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["Q", "R"]}}, "module_name": "numpy.linalg.linalg", "mro": ["numpy.linalg.linalg.QRResult", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "Q": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.linalg.linalg.QRResult.Q", "name": "Q", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "Q-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.QRResult.Q", "kind": "<PERSON><PERSON><PERSON>"}, "R": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.linalg.linalg.QRResult.R", "name": "R", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "R-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.QRResult.R", "kind": "<PERSON><PERSON><PERSON>"}, "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.QRResult.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.QRResult.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "Q", "R"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.linalg.linalg.QRResult.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "Q", "R"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of QRResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.QRResult._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of QRResult", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.QRResult._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.QRResult._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.QRResult._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.linalg.linalg.QRResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of QRResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "numpy.linalg.linalg.QRResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of QRResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "Q", "R"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.QRResult._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "Q", "R"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of QRResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.QRResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.QRResult._source", "name": "_source", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.QRResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": "numpy.linalg.linalg.QRResult"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "SVDResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.linalg.linalg.SVDResult", "name": "SVDResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "numpy.linalg.linalg.SVDResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["U", "S", "Vh"]}}, "module_name": "numpy.linalg.linalg", "mro": ["numpy.linalg.linalg.SVDResult", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "S": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.linalg.linalg.SVDResult.S", "name": "S", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "S-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.SVDResult.S", "kind": "<PERSON><PERSON><PERSON>"}, "U": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.linalg.linalg.SVDResult.U", "name": "U", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "U-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.SVDResult.U", "kind": "<PERSON><PERSON><PERSON>"}, "Vh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.linalg.linalg.SVDResult.Vh", "name": "Vh", "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}}}, "Vh-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.SVDResult.Vh", "kind": "<PERSON><PERSON><PERSON>"}, "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SVDResult.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SVDResult.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "U", "S", "Vh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.linalg.linalg.SVDResult.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "U", "S", "Vh"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of SVDResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.SVDResult._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of SVDResult", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SVDResult._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SVDResult._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SVDResult._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.linalg.linalg.SVDResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of SVDResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "numpy.linalg.linalg.SVDResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of SVDResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "U", "S", "Vh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.SVDResult._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "U", "S", "Vh"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of SVDResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SVDResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SVDResult._source", "name": "_source", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SVDResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": "numpy.linalg.linalg.SVDResult"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "SlogdetResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.linalg.linalg.SlogdetResult", "name": "SlogdetResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "numpy.linalg.linalg.SlogdetResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["sign", "logabsdet"]}}, "module_name": "numpy.linalg.linalg", "mro": ["numpy.linalg.linalg.SlogdetResult", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SlogdetResult.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SlogdetResult.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "sign", "logabsdet"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "numpy.linalg.linalg.SlogdetResult.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "sign", "logabsdet"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of SlogdetResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.SlogdetResult._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of SlogdetResult", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SlogdetResult._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SlogdetResult._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SlogdetResult._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.linalg.linalg.SlogdetResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of SlogdetResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "numpy.linalg.linalg.SlogdetResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of SlogdetResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "sign", "logabsdet"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.SlogdetResult._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "sign", "logabsdet"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of SlogdetResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult._NT", "id": -1, "name": "_NT", "namespace": "numpy.linalg.linalg.SlogdetResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.linalg.linalg.SlogdetResult._source", "name": "_source", "type": "builtins.str"}}, "logabsdet": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.linalg.linalg.SlogdetResult.logabsdet", "name": "logabsdet", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "logabsdet-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.SlogdetResult.logabsdet", "kind": "<PERSON><PERSON><PERSON>"}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "numpy.linalg.linalg.SlogdetResult.sign", "name": "sign", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "sign-redefinition": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.SlogdetResult.sign", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg.SlogdetResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": "numpy.linalg.linalg.SlogdetResult"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsInt": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsInt", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_2Tuple": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg._T", "id": 1, "name": "_T", "namespace": "numpy.linalg.linalg._2Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "numpy.linalg.linalg._2Tuple", "line": 39, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg._T", "id": 1, "name": "_T", "namespace": "numpy.linalg.linalg._2Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg._T", "id": 1, "name": "_T", "namespace": "numpy.linalg.linalg._2Tuple", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_ArrayLikeComplex_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeComplex_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeFloat_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeFloat_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeInt_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeInt_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeObject_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeObject_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeTD64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeTD64_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg._ArrayType", "name": "_ArrayType", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "values": [], "variance": 0}}, "_ModeKind": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.linalg.linalg._ModeKind", "line": 40, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "reduced"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "complete"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "raw"}], "uses_pep604_syntax": false}}}, "_SCT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg._SCT", "name": "_SCT", "upper_bound": "numpy.generic", "values": [], "variance": 1}}, "_SCT2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg._SCT2", "name": "_SCT2", "upper_bound": "numpy.generic", "values": [], "variance": 1}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.linalg._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.linalg.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.linalg.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.linalg.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.linalg.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.linalg.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.linalg.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.linalg.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cholesky": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.cholesky", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.cholesky", "name": "cholesky", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cholesky", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.cholesky", "name": "cholesky", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cholesky", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.cholesky", "name": "cholesky", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cholesky", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.cholesky", "name": "cholesky", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cholesky", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.cholesky", "name": "cholesky", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cholesky", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.cholesky", "name": "cholesky", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cholesky", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cholesky", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cholesky", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cholesky", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "complex128": {".class": "SymbolTableNode", "cross_ref": "numpy.complex128", "kind": "Gdef", "module_hidden": true, "module_public": false}, "complexfloating": {".class": "SymbolTableNode", "cross_ref": "numpy.complexfloating", "kind": "Gdef", "module_hidden": true, "module_public": false}, "cond": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["x", "p"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.cond", "name": "cond", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["x", "p"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "fro"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nuc"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cond", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "det": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.det", "name": "det", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "det", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eig": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.eig", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.eig", "name": "eig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.EigResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.eig", "name": "eig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.EigResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.eig", "name": "eig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.EigResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.eig", "name": "eig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.EigResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.eig", "name": "eig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.EigResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.eig", "name": "eig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.EigResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.EigResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.EigResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.EigResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "eigh": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.eigh", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.eigh", "name": "eigh", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigh", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.Eigh<PERSON>esult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.eigh", "name": "eigh", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigh", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.Eigh<PERSON>esult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.eigh", "name": "eigh", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigh", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.Eigh<PERSON>esult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.eigh", "name": "eigh", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigh", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.Eigh<PERSON>esult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.eigh", "name": "eigh", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigh", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.Eigh<PERSON>esult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.eigh", "name": "eigh", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigh", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.Eigh<PERSON>esult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigh", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.Eigh<PERSON>esult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigh", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.Eigh<PERSON>esult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigh", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.Eigh<PERSON>esult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "eigvals": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.eigvals", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.eigvals", "name": "eigvals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigvals", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.eigvals", "name": "eigvals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigvals", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.eigvals", "name": "eigvals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigvals", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.eigvals", "name": "eigvals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigvals", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.eigvals", "name": "eigvals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigvals", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.eigvals", "name": "eigvals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigvals", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigvals", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.complex128"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigvals", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eigvals", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "eigvalsh": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.eig<PERSON>sh", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.eig<PERSON>sh", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.eig<PERSON>sh", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.eig<PERSON>sh", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.eig<PERSON>sh", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "UPLO"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "U"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "l"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "u"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "float64": {".class": "SymbolTableNode", "cross_ref": "numpy.float64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "floating": {".class": "SymbolTableNode", "cross_ref": "numpy.floating", "kind": "Gdef", "module_hidden": true, "module_public": false}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int32": {".class": "SymbolTableNode", "cross_ref": "numpy.int32", "kind": "Gdef", "module_hidden": true, "module_public": false}, "inv": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.inv", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.inv", "name": "inv", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.inv", "name": "inv", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.inv", "name": "inv", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.inv", "name": "inv", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.inv", "name": "inv", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.inv", "name": "inv", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "lstsq": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.lstsq", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.lstsq", "name": "lstsq", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstsq", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.lstsq", "name": "lstsq", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstsq", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.lstsq", "name": "lstsq", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstsq", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.lstsq", "name": "lstsq", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstsq", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.lstsq", "name": "lstsq", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstsq", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.lstsq", "name": "lstsq", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstsq", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstsq", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstsq", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "rcond"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lstsq", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.int32"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "matrix_power": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.matrix_power", "name": "matrix_power", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "n"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, "typing.SupportsIndex"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matrix_power", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "matrix_rank": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["A", "tol", "hermitian"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.matrix_rank", "name": "matrix_rank", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["A", "tol", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matrix_rank", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "multi_dot": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["arrays", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.multi_dot", "name": "multi_dot", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["arrays", "out"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multi_dot", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "norm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.norm", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["x", "ord", "axis", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.norm", "name": "norm", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["x", "ord", "axis", "keepdims"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "fro"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nuc"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "norm", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.norm", "name": "norm", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["x", "ord", "axis", "keepdims"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "fro"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nuc"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "norm", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["x", "ord", "axis", "keepdims"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.norm", "name": "norm", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["x", "ord", "axis", "keepdims"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "fro"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nuc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsInt", "typing.SupportsIndex", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.norm", "name": "norm", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["x", "ord", "axis", "keepdims"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "fro"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nuc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsInt", "typing.SupportsIndex", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["x", "ord", "axis", "keepdims"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "fro"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nuc"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "norm", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["x", "ord", "axis", "keepdims"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "fro"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nuc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["typing.SupportsInt", "typing.SupportsIndex", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "norm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "pinv": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.pinv", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.pinv", "name": "pinv", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.pinv", "name": "pinv", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.pinv", "name": "pinv", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.pinv", "name": "pinv", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.pinv", "name": "pinv", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.pinv", "name": "pinv", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["a", "rcond", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "qr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.qr", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.qr", "name": "qr", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg._ModeKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qr", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.QRResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.qr", "name": "qr", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg._ModeKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qr", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.QRResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.qr", "name": "qr", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg._ModeKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qr", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.QRResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.qr", "name": "qr", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg._ModeKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qr", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.QRResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.qr", "name": "qr", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg._ModeKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qr", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.QRResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.qr", "name": "qr", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg._ModeKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qr", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.QRResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg._ModeKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qr", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.QRResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg._ModeKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qr", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.QRResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "mode"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg._ModeKind"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "qr", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.QRResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "slogdet": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.slogdet", "name": "slogdet", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "slogdet", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.SlogdetResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "solve": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.solve", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.solve", "name": "solve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "solve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.solve", "name": "solve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "solve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.solve", "name": "solve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "solve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.solve", "name": "solve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "solve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.solve", "name": "solve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "solve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.solve", "name": "solve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "solve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "solve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "solve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "solve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "svd": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.svd", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.svd", "name": "svd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.SVDResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.svd", "name": "svd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.SVDResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.svd", "name": "svd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.SVDResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.svd", "name": "svd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.SVDResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.svd", "name": "svd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.SVDResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.svd", "name": "svd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.SVDResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.svd", "name": "svd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.svd", "name": "svd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.svd", "name": "svd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.svd", "name": "svd", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.SVDResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.SVDResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "numpy.linalg.linalg.SVDResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["a", "full_matrices", "compute_uv", "hermitian"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "svd", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "tensorinv": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.tensorinv", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.tensorinv", "name": "tensorinv", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.tensorinv", "name": "tensorinv", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.tensorinv", "name": "tensorinv", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.tensorinv", "name": "tensorinv", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.tensorinv", "name": "tensorinv", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.tensorinv", "name": "tensorinv", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "ind"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorinv", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "tensorsolve": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.linalg.linalg.tensorsolve", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.tensorsolve", "name": "tensorsolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorsolve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.tensorsolve", "name": "tensorsolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorsolve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.tensorsolve", "name": "tensorsolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorsolve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.tensorsolve", "name": "tensorsolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorsolve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.linalg.linalg.tensorsolve", "name": "tensorsolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorsolve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.linalg.linalg.tensorsolve", "name": "tensorsolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorsolve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeInt_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorsolve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeFloat_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorsolve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["a", "b", "axes"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeComplex_co"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tensorsolve", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\numpy\\linalg\\linalg.pyi"}