{".class": "MypyFile", "_fullname": "rich.console", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Align": {".class": "SymbolTableNode", "cross_ref": "rich.align.<PERSON>gn", "kind": "Gdef"}, "AlignMethod": {".class": "SymbolTableNode", "cross_ref": "rich.align.AlignMethod", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "COLOR_SYSTEMS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console.COLOR_SYSTEMS", "name": "COLOR_SYSTEMS", "type": {".class": "Instance", "args": ["builtins.str", "rich.color.ColorSystem"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "CONSOLE_HTML_FORMAT": {".class": "SymbolTableNode", "cross_ref": "rich._export_format.CONSOLE_HTML_FORMAT", "kind": "Gdef"}, "CONSOLE_SVG_FORMAT": {".class": "SymbolTableNode", "cross_ref": "rich._export_format.CONSOLE_SVG_FORMAT", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Capture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.Capture", "name": "Capture", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.Capture", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.Capture", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Capture.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.console.Capture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of Capture", "ret_type": "rich.console.Capture", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Capture.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["rich.console.Capture", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of Capture", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "console"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Capture.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "console"], "arg_types": ["rich.console.Capture", "rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Capture", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_console": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Capture._console", "name": "_console", "type": "rich.console.Console"}}, "_result": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.console.Capture._result", "name": "_result", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Capture.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Capture"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Capture", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.Capture.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.Capture", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CaptureError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.CaptureError", "name": "CaptureError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.CaptureError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.CaptureError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.CaptureError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.CaptureError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColorSystem": {".class": "SymbolTableNode", "cross_ref": "rich.color.ColorSystem", "kind": "Gdef"}, "Console": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.Console", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.Console", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.Console", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of <PERSON>sole", "ret_type": "rich.console.Console", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["rich.console.Console", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "color_system", "force_terminal", "force_jupyter", "force_interactive", "soft_wrap", "theme", "stderr", "file", "quiet", "width", "height", "style", "no_color", "tab_size", "record", "markup", "emoji", "emoji_variant", "highlight", "log_time", "log_path", "log_time_format", "highlighter", "legacy_windows", "safe_box", "get_datetime", "get_time", "_environ"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "color_system", "force_terminal", "force_jupyter", "force_interactive", "soft_wrap", "theme", "stderr", "file", "quiet", "width", "height", "style", "no_color", "tab_size", "record", "markup", "emoji", "emoji_variant", "highlight", "log_time", "log_path", "log_time_format", "highlighter", "legacy_windows", "safe_box", "get_datetime", "get_time", "_environ"], "arg_types": ["rich.console.Console", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "standard"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "256"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "truecolor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "windows"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["rich.theme.Theme", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.emoji.EmojiV<PERSON>t"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "rich._log_render.FormatTimeCallable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.HighlighterType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of <PERSON><PERSON>e", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.console.Console._buffer", "name": "_buffer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_buffer of Console", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console._buffer", "name": "_buffer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_buffer of Console", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_buffer_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "rich.console.Console._buffer_index", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "rich.console.Console._buffer_index", "name": "_buffer_index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_buffer_index of Console", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console._buffer_index", "name": "_buffer_index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_buffer_index of Console", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "rich.console.Console._buffer_index", "name": "_buffer_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["rich.console.Console", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_buffer_index of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "_buffer_index", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_buffer_index of Console", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_caller_frame_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["offset", "currentframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "rich.console.Console._caller_frame_info", "name": "_caller_frame_info", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["offset", "currentframe"], "arg_types": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["types.FrameType", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_caller_frame_info of Console", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "rich.console.Console._caller_frame_info", "name": "_caller_frame_info", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["offset", "currentframe"], "arg_types": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["types.FrameType", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_caller_frame_info of Console", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_check_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console._check_buffer", "name": "_check_buffer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_buffer of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_collect_renderables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5], "arg_names": ["self", "objects", "sep", "end", "justify", "emoji", "markup", "highlight"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console._collect_renderables", "name": "_collect_renderables", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5], "arg_names": ["self", "objects", "sep", "end", "justify", "emoji", "markup", "highlight"], "arg_types": ["rich.console.Console", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_collect_renderables of Console", "ret_type": {".class": "Instance", "args": ["rich.console.ConsoleRenderable"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_color_system": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.console.Console._color_system", "name": "_color_system", "type": {".class": "UnionType", "items": ["rich.color.ColorSystem", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_detect_color_system": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console._detect_color_system", "name": "_detect_color_system", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detect_color_system of Console", "ret_type": {".class": "UnionType", "items": ["rich.color.ColorSystem", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_emoji": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._emoji", "name": "_emoji", "type": "builtins.bool"}}, "_emoji_variant": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.console.Console._emoji_variant", "name": "_emoji_variant", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.emoji.EmojiV<PERSON>t"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_enter_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console._enter_buffer", "name": "_enter_buffer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_enter_buffer of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_environ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich.console.Console._environ", "name": "_environ", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_exit_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console._exit_buffer", "name": "_exit_buffer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_exit_buffer of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._file", "name": "_file", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_force_terminal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._force_terminal", "name": "_force_terminal", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._height", "name": "_height", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_highlight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._highlight", "name": "_highlight", "type": "builtins.bool"}}, "_is_alt_screen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._is_alt_screen", "name": "_is_alt_screen", "type": "builtins.bool"}}, "_live": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.console.Console._live", "name": "_live", "type": {".class": "UnionType", "items": ["rich.live.Live", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._lock", "name": "_lock", "type": "_thread.RLock"}}, "_log_render": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._log_render", "name": "_log_render", "type": "rich._log_render.LogRender"}}, "_markup": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._markup", "name": "_markup", "type": "builtins.bool"}}, "_record_buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.console.Console._record_buffer", "name": "_record_buffer", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_record_buffer_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._record_buffer_lock", "name": "_record_buffer_lock", "type": "_thread.RLock"}}, "_render_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console._render_buffer", "name": "_render_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "arg_types": ["rich.console.Console", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_buffer of Console", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_hooks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.console.Console._render_hooks", "name": "_render_hooks", "type": {".class": "Instance", "args": ["rich.console.RenderHook"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_theme_stack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.console.Console._theme_stack", "name": "_theme_stack", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_theme_stack of Console", "ret_type": "rich.theme.ThemeStack", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console._theme_stack", "name": "_theme_stack", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_theme_stack of Console", "ret_type": "rich.theme.ThemeStack", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_thread_locals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._thread_locals", "name": "_thread_locals", "type": "rich.console.ConsoleThreadLocals"}}, "_width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console._width", "name": "_width", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "begin_capture": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.begin_capture", "name": "begin_capture", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "begin_capture of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.bell", "name": "bell", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bell of <PERSON>sole", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "capture": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.capture", "name": "capture", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capture of Console", "ret_type": "rich.console.Capture", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "home"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "home"], "arg_types": ["rich.console.Console", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_live": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.clear_live", "name": "clear_live", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_live of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "color_system": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.console.Console.color_system", "name": "color_system", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "color_system of Console", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console.color_system", "name": "color_system", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "color_system of Console", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "control": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "control"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.control", "name": "control", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "control"], "arg_types": ["rich.console.Console", "rich.control.Control"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "control of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.console.Console.encoding", "name": "encoding", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encoding of Console", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console.encoding", "name": "encoding", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encoding of Console", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "end_capture": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.end_capture", "name": "end_capture", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_capture of <PERSON>sole", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "export_html": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "theme", "clear", "code_format", "inline_styles"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.export_html", "name": "export_html", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "theme", "clear", "code_format", "inline_styles"], "arg_types": ["rich.console.Console", {".class": "UnionType", "items": ["rich.terminal_theme.TerminalTheme", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export_html of Console", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "export_svg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "title", "theme", "clear", "code_format", "font_aspect_ratio", "unique_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.export_svg", "name": "export_svg", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "title", "theme", "clear", "code_format", "font_aspect_ratio", "unique_id"], "arg_types": ["rich.console.Console", "builtins.str", {".class": "UnionType", "items": ["rich.terminal_theme.TerminalTheme", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export_svg of Console", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "export_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "clear", "styles"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.export_text", "name": "export_text", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "clear", "styles"], "arg_types": ["rich.console.Console", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "export_text of Console", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "rich.console.Console.file", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "rich.console.Console.file", "name": "file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "file of <PERSON><PERSON>e", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.IO"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console.file", "name": "file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "file of <PERSON><PERSON>e", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.IO"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "rich.console.Console.file", "name": "file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_file"], "arg_types": ["rich.console.Console", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.IO"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "file of <PERSON><PERSON>e", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "file", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "file of <PERSON><PERSON>e", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.IO"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.get_datetime", "name": "get_datetime", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "name", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.get_style", "name": "get_style", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "name", "default"], "arg_types": ["rich.console.Console", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["rich.style.Style", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_style of Console", "ret_type": "rich.style.Style", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.get_time", "name": "get_time", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "rich.console.Console.height", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "rich.console.Console.height", "name": "height", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "height of Console", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console.height", "name": "height", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "height of Console", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "rich.console.Console.height", "name": "height", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "height"], "arg_types": ["rich.console.Console", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "height of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "height", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "height of Console", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "highlighter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.console.Console.highlighter", "name": "highlighter", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.HighlighterType"}}}, "input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "prompt", "markup", "emoji", "password", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.input", "name": "input", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5], "arg_names": ["self", "prompt", "markup", "emoji", "password", "stream"], "arg_types": ["rich.console.Console", {".class": "TypeAliasType", "args": [], "type_ref": "rich.text.TextType"}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["typing.TextIO", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "input of Console", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_alt_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.console.Console.is_alt_screen", "name": "is_alt_screen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_alt_screen of Console", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console.is_alt_screen", "name": "is_alt_screen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_alt_screen of Console", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_dumb_terminal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.console.Console.is_dumb_terminal", "name": "is_dumb_terminal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_dumb_terminal of Console", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console.is_dumb_terminal", "name": "is_dumb_terminal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_dumb_terminal of Console", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_interactive": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.is_interactive", "name": "is_interactive", "type": "builtins.bool"}}, "is_jupyter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.is_jupyter", "name": "is_jupyter", "type": "builtins.bool"}}, "is_terminal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.console.Console.is_terminal", "name": "is_terminal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_terminal of Console", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console.is_terminal", "name": "is_terminal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_terminal of Console", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "legacy_windows": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.console.Console.legacy_windows", "name": "legacy_windows", "type": "builtins.bool"}}, "line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.line", "name": "line", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "count"], "arg_types": ["rich.console.Console", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "line of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "objects", "sep", "end", "style", "justify", "emoji", "markup", "highlight", "log_locals", "_stack_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.log", "name": "log", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "objects", "sep", "end", "style", "justify", "emoji", "markup", "highlight", "log_locals", "_stack_offset"], "arg_types": ["rich.console.Console", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "measure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "renderable", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.measure", "name": "measure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "renderable", "options"], "arg_types": ["rich.console.Console", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}, {".class": "UnionType", "items": ["rich.console.ConsoleOptions", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "measure of Console", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.measure.Measurement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "no_color": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.no_color", "name": "no_color", "type": "builtins.bool"}}, "options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.console.Console.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "options of Console", "ret_type": "rich.console.ConsoleOptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "options of Console", "ret_type": "rich.console.ConsoleOptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "out": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5], "arg_names": ["self", "objects", "sep", "end", "style", "highlight"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.out", "name": "out", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5], "arg_names": ["self", "objects", "sep", "end", "style", "highlight"], "arg_types": ["rich.console.Console", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "out of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "pager", "styles", "links"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.pager", "name": "pager", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "pager", "styles", "links"], "arg_types": ["rich.console.Console", {".class": "UnionType", "items": ["rich.pager.Pager", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pager of Console", "ret_type": "rich.console.PagerContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pop_render_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.pop_render_hook", "name": "pop_render_hook", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_render_hook of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pop_theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.pop_theme", "name": "pop_theme", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_theme of <PERSON><PERSON>e", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "print": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "objects", "sep", "end", "style", "justify", "overflow", "no_wrap", "emoji", "markup", "highlight", "width", "height", "crop", "soft_wrap", "new_line_start"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.print", "name": "print", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "objects", "sep", "end", "style", "justify", "overflow", "no_wrap", "emoji", "markup", "highlight", "width", "height", "crop", "soft_wrap", "new_line_start"], "arg_types": ["rich.console.Console", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "print_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "width", "extra_lines", "theme", "word_wrap", "show_locals", "suppress", "max_frames"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.print_exception", "name": "print_exception", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "width", "extra_lines", "theme", "word_wrap", "show_locals", "suppress", "max_frames"], "arg_types": ["rich.console.Console", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "types.ModuleType"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_exception of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "print_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "json", "data", "indent", "highlight", "skip_keys", "ensure_ascii", "check_circular", "allow_nan", "default", "sort_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.print_json", "name": "print_json", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "json", "data", "indent", "highlight", "skip_keys", "ensure_ascii", "check_circular", "allow_nan", "default", "sort_keys"], "arg_types": ["rich.console.Console", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_json of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push_render_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hook"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.push_render_hook", "name": "push_render_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hook"], "arg_types": ["rich.console.Console", "rich.console.RenderHook"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_render_hook of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push_theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "theme", "inherit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.push_theme", "name": "push_theme", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "theme", "inherit"], "arg_types": ["rich.console.Console", "rich.theme.Theme", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_theme of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "quiet": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.quiet", "name": "quiet", "type": "builtins.bool"}}, "record": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.record", "name": "record", "type": "builtins.bool"}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "renderable", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "renderable", "options"], "arg_types": ["rich.console.Console", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}, {".class": "UnionType", "items": ["rich.console.ConsoleOptions", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Console", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "renderable", "options", "style", "pad", "new_lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.render_lines", "name": "render_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "renderable", "options", "style", "pad", "new_lines"], "arg_types": ["rich.console.Console", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}, {".class": "UnionType", "items": ["rich.console.ConsoleOptions", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["rich.style.Style", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_lines of Console", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "text", "style", "justify", "overflow", "emoji", "markup", "highlight", "highlighter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.render_str", "name": "render_str", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "text", "style", "justify", "overflow", "emoji", "markup", "highlight", "highlighter"], "arg_types": ["rich.console.Console", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.HighlighterType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_str of Console", "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "title", "characters", "style", "align"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.rule", "name": "rule", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["self", "title", "characters", "style", "align"], "arg_types": ["rich.console.Console", {".class": "TypeAliasType", "args": [], "type_ref": "rich.text.TextType"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", "rich.style.Style"], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.align.AlignMethod"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rule of <PERSON><PERSON>e", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "safe_box": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.safe_box", "name": "safe_box", "type": "builtins.bool"}}, "save_html": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "path", "theme", "clear", "code_format", "inline_styles"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.save_html", "name": "save_html", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "path", "theme", "clear", "code_format", "inline_styles"], "arg_types": ["rich.console.Console", "builtins.str", {".class": "UnionType", "items": ["rich.terminal_theme.TerminalTheme", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_html of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_svg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "path", "title", "theme", "clear", "code_format", "font_aspect_ratio", "unique_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.save_svg", "name": "save_svg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "path", "title", "theme", "clear", "code_format", "font_aspect_ratio", "unique_id"], "arg_types": ["rich.console.Console", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["rich.terminal_theme.TerminalTheme", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_svg of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "path", "clear", "styles"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.save_text", "name": "save_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "path", "clear", "styles"], "arg_types": ["rich.console.Console", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_text of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "hide_cursor", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.screen", "name": "screen", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "hide_cursor", "style"], "arg_types": ["rich.console.Console", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "screen of <PERSON><PERSON><PERSON>", "ret_type": "rich.console.ScreenContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_alt_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "enable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.set_alt_screen", "name": "set_alt_screen", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "enable"], "arg_types": ["rich.console.Console", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_alt_screen of Console", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_live": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "live"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.set_live", "name": "set_live", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "live"], "arg_types": ["rich.console.Console", "rich.live.Live"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_live of <PERSON>sole", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_window_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "title"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.set_window_title", "name": "set_window_title", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "title"], "arg_types": ["rich.console.Console", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_window_title of <PERSON><PERSON>e", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "show_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "show"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.show_cursor", "name": "show_cursor", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "show"], "arg_types": ["rich.console.Console", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show_cursor of <PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "rich.console.Console.size", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "rich.console.Console.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of Console", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.ConsoleDimensions"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of Console", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.ConsoleDimensions"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "rich.console.Console.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_size"], "arg_types": ["rich.console.Console", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "size", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of Console", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.ConsoleDimensions"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "soft_wrap": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.soft_wrap", "name": "soft_wrap", "type": "builtins.bool"}}, "status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "status", "spinner", "spinner_style", "speed", "refresh_per_second"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.status", "name": "status", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "status", "spinner", "spinner_style", "speed", "refresh_per_second"], "arg_types": ["rich.console.Console", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "status of Console", "ret_type": "rich.status.Status", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stderr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.stderr", "name": "stderr", "type": "builtins.bool"}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.style", "name": "style", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tab_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Console.tab_size", "name": "tab_size", "type": "builtins.int"}}, "update_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "renderable", "region", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.update_screen", "name": "update_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "renderable", "region", "options"], "arg_types": ["rich.console.Console", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.region.Region"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["rich.console.ConsoleOptions", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_screen of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_screen_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "lines", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.update_screen_lines", "name": "update_screen_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "lines", "x", "y"], "arg_types": ["rich.console.Console", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_screen_lines of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "use_theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "theme", "inherit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Console.use_theme", "name": "use_theme", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "theme", "inherit"], "arg_types": ["rich.console.Console", "rich.theme.Theme", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "use_theme of Console", "ret_type": "rich.console.ThemeContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "rich.console.Console.width", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "rich.console.Console.width", "name": "width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "width of Console", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "rich.console.Console.width", "name": "width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "width of Console", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "rich.console.Console.width", "name": "width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["rich.console.Console", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "width of Console", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "width", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Console"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "width of Console", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.Console.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.Console", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConsoleDimensions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.ConsoleDimensions", "name": "ConsoleDimensions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "rich.console.ConsoleDimensions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["width", "height"]}}, "module_name": "rich.console", "mro": ["rich.console.ConsoleDimensions", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleDimensions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleDimensions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "rich.console.ConsoleDimensions.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "width", "height"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ConsoleDimensions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleDimensions._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of ConsoleDimensions", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleDimensions._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleDimensions._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleDimensions._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "rich.console.ConsoleDimensions._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of ConsoleDimensions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "rich.console.ConsoleDimensions._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of ConsoleDimensions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleDimensions._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "width", "height"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of ConsoleDimensions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions._NT", "id": -1, "name": "_NT", "namespace": "rich.console.ConsoleDimensions._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleDimensions._source", "name": "_source", "type": "builtins.str"}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "rich.console.ConsoleDimensions.height", "name": "height", "type": "builtins.int"}}, "height-redefinition": {".class": "SymbolTableNode", "cross_ref": "rich.console.ConsoleDimensions.height", "kind": "<PERSON><PERSON><PERSON>"}, "width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "rich.console.ConsoleDimensions.width", "name": "width", "type": "builtins.int"}}, "width-redefinition": {".class": "SymbolTableNode", "cross_ref": "rich.console.ConsoleDimensions.width", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleDimensions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": "rich.console.ConsoleDimensions"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "ConsoleOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.ConsoleOptions", "name": "ConsoleOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 129, "name": "size", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.ConsoleDimensions"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 131, "name": "legacy_windows", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 133, "name": "min_width", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 135, "name": "max_width", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 137, "name": "is_terminal", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 139, "name": "encoding", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 141, "name": "max_height", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 143, "name": "justify", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 145, "name": "overflow", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 147, "name": "no_wrap", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 149, "name": "highlight", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 151, "name": "markup", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 153, "name": "height", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "rich.console", "mro": ["rich.console.ConsoleOptions", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "rich.console.ConsoleOptions.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "size", "legacy_windows", "min_width", "max_width", "is_terminal", "encoding", "max_height", "justify", "overflow", "no_wrap", "highlight", "markup", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleOptions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "size", "legacy_windows", "min_width", "max_width", "is_terminal", "encoding", "max_height", "justify", "overflow", "no_wrap", "highlight", "markup", "height"], "arg_types": ["rich.console.ConsoleOptions", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.ConsoleDimensions"}, "builtins.bool", "builtins.int", "builtins.int", "builtins.bool", "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "full"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fold"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "crop"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ellipsis"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConsoleOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["size", "legacy_windows", "min_width", "max_width", "is_terminal", "encoding", "max_height", "justify", "overflow", "no_wrap", "highlight", "markup", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "rich.console.ConsoleOptions.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["size", "legacy_windows", "min_width", "max_width", "is_terminal", "encoding", "max_height", "justify", "overflow", "no_wrap", "highlight", "markup", "height"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.ConsoleDimensions"}, "builtins.bool", "builtins.int", "builtins.int", "builtins.bool", "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "full"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fold"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "crop"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ellipsis"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ConsoleOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "rich.console.ConsoleOptions.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["size", "legacy_windows", "min_width", "max_width", "is_terminal", "encoding", "max_height", "justify", "overflow", "no_wrap", "highlight", "markup", "height"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.ConsoleDimensions"}, "builtins.bool", "builtins.int", "builtins.int", "builtins.bool", "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "full"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fold"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "crop"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ellipsis"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ConsoleOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "ascii_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.console.ConsoleOptions.ascii_only", "name": "ascii_only", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ascii_only of ConsoleOptions", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.console.ConsoleOptions.ascii_only", "name": "ascii_only", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ascii_only of ConsoleOptions", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleOptions.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of ConsoleOptions", "ret_type": "rich.console.ConsoleOptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleOptions.encoding", "name": "encoding", "type": "builtins.str"}}, "height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich.console.ConsoleOptions.height", "name": "height", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "highlight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich.console.ConsoleOptions.highlight", "name": "highlight", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "is_terminal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleOptions.is_terminal", "name": "is_terminal", "type": "builtins.bool"}}, "justify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich.console.ConsoleOptions.justify", "name": "justify", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "legacy_windows": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleOptions.legacy_windows", "name": "legacy_windows", "type": "builtins.bool"}}, "markup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich.console.ConsoleOptions.markup", "name": "markup", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleOptions.max_height", "name": "max_height", "type": "builtins.int"}}, "max_width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleOptions.max_width", "name": "max_width", "type": "builtins.int"}}, "min_width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleOptions.min_width", "name": "min_width", "type": "builtins.int"}}, "no_wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich.console.ConsoleOptions.no_wrap", "name": "no_wrap", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "overflow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich.console.ConsoleOptions.overflow", "name": "overflow", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "reset_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleOptions.reset_height", "name": "reset_height", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_height of ConsoleOptions", "ret_type": "rich.console.ConsoleOptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleOptions.size", "name": "size", "type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.ConsoleDimensions"}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "width", "min_width", "max_width", "justify", "overflow", "no_wrap", "highlight", "markup", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleOptions.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "width", "min_width", "max_width", "justify", "overflow", "no_wrap", "highlight", "markup", "height"], "arg_types": ["rich.console.ConsoleOptions", {".class": "UnionType", "items": ["builtins.int", "rich.console.NoChange"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "rich.console.NoChange"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "rich.console.NoChange"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.JustifyMethod"}, {".class": "NoneType"}, "rich.console.NoChange"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.OverflowMethod"}, {".class": "NoneType"}, "rich.console.NoChange"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}, "rich.console.NoChange"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}, "rich.console.NoChange"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}, "rich.console.NoChange"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}, "rich.console.NoChange"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of ConsoleOptions", "ret_type": "rich.console.ConsoleOptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_dimensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleOptions.update_dimensions", "name": "update_dimensions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "arg_types": ["rich.console.ConsoleOptions", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_dimensions of ConsoleOptions", "ret_type": "rich.console.ConsoleOptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleOptions.update_height", "name": "update_height", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "height"], "arg_types": ["rich.console.ConsoleOptions", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_height of ConsoleOptions", "ret_type": "rich.console.ConsoleOptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleOptions.update_width", "name": "update_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["rich.console.ConsoleOptions", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_width of ConsoleOptions", "ret_type": "rich.console.ConsoleOptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.ConsoleOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConsoleRenderable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__rich_console__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.ConsoleRenderable", "name": "ConsoleRenderable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "rich.console.ConsoleRenderable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.ConsoleRenderable", "builtins.object"], "names": {".class": "SymbolTable", "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "rich.console.ConsoleRenderable.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.console.ConsoleRenderable", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of ConsoleRenderable", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleRenderable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.ConsoleRenderable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConsoleThreadLocals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_thread._local"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.ConsoleThreadLocals", "name": "ConsoleThreadLocals", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleThreadLocals", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 552, "name": "theme_stack", "type": "rich.theme.ThemeStack"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 553, "name": "buffer", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 554, "name": "buffer_index", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "rich.console", "mro": ["rich.console.ConsoleThreadLocals", "_thread._local", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "rich.console.ConsoleThreadLocals.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "theme_stack", "buffer", "buffer_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ConsoleThreadLocals.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "theme_stack", "buffer", "buffer_index"], "arg_types": ["rich.console.ConsoleThreadLocals", "rich.theme.ThemeStack", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConsoleThreadLocals", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["theme_stack", "buffer", "buffer_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "rich.console.ConsoleThreadLocals.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["theme_stack", "buffer", "buffer_index"], "arg_types": ["rich.theme.ThemeStack", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ConsoleThreadLocals", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "rich.console.ConsoleThreadLocals.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["theme_stack", "buffer", "buffer_index"], "arg_types": ["rich.theme.ThemeStack", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ConsoleThreadLocals", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich.console.ConsoleThreadLocals.buffer", "name": "buffer", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "buffer_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich.console.ConsoleThreadLocals.buffer_index", "name": "buffer_index", "type": "builtins.int"}}, "theme_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "rich.console.ConsoleThreadLocals.theme_stack", "name": "theme_stack", "type": "rich.theme.ThemeStack"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ConsoleThreadLocals.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.ConsoleThreadLocals", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Control": {".class": "SymbolTableNode", "cross_ref": "rich.control.Control", "kind": "Gdef"}, "DEFAULT_TERMINAL_THEME": {".class": "SymbolTableNode", "cross_ref": "rich.terminal_theme.DEFAULT_TERMINAL_THEME", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EmojiVariant": {".class": "SymbolTableNode", "cross_ref": "rich.emoji.EmojiV<PERSON>t", "kind": "Gdef"}, "FormatTimeCallable": {".class": "SymbolTableNode", "cross_ref": "rich._log_render.FormatTimeCallable", "kind": "Gdef"}, "FrameType": {".class": "SymbolTableNode", "cross_ref": "types.FrameType", "kind": "Gdef"}, "Group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.Group", "name": "Group", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.Group", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.Group", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["self", "renderables", "fit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Group.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["self", "renderables", "fit"], "arg_types": ["rich.console.Group", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Group", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Group.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.console.Group", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of Group", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_measure__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.Group.__rich_measure__", "name": "__rich_measure__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.console.Group", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_measure__ of Group", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.measure.Measurement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "rich.console.Group._render", "name": "_render", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_renderables": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Group._renderables", "name": "_renderables", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "fit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.Group.fit", "name": "fit", "type": "builtins.bool"}}, "renderables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "rich.console.Group.renderables", "name": "renderables", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Group"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "renderables of Group", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "rich.console.Group.renderables", "name": "renderables", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.Group"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "renderables of Group", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.Group.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.Group", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HighlighterType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "rich.console.HighlighterType", "line": 81, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "rich.text.Text"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "rich.text.Text", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "JUPYTER_DEFAULT_COLUMNS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "rich.console.JUPYTER_DEFAULT_COLUMNS", "name": "JUPYTER_DEFAULT_COLUMNS", "type": "builtins.int"}}, "JUPYTER_DEFAULT_LINES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "rich.console.JUPYTER_DEFAULT_LINES", "name": "JUPYTER_DEFAULT_LINES", "type": "builtins.int"}}, "JustifyMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "rich.console.JustifyMethod", "line": 82, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "full"}], "uses_pep604_syntax": false}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Live": {".class": "SymbolTableNode", "cross_ref": "rich.live.Live", "kind": "Gdef"}, "LogRender": {".class": "SymbolTableNode", "cross_ref": "rich._log_render.LogRender", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Measurement": {".class": "SymbolTableNode", "cross_ref": "rich.measure.Measurement", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef"}, "NO_CHANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console.NO_CHANGE", "name": "NO_CHANGE", "type": "rich.console.NoChange"}}, "NULL_FILE": {".class": "SymbolTableNode", "cross_ref": "rich._null_file.NULL_FILE", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "NewLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.NewLine", "name": "NewLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.NewLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.NewLine", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.NewLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "count"], "arg_types": ["rich.console.NewLine", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NewLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.NewLine.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.console.NewLine", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of NewLine", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.NewLine.count", "name": "count", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.NewLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.NewLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoChange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.NoChange", "name": "NoChange", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.NoChange", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.NoChange", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.NoChange.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.NoChange", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NullHighlighter": {".class": "SymbolTableNode", "cross_ref": "rich.highlighter.<PERSON><PERSON><PERSON><PERSON><PERSON>er", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OverflowMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "rich.console.OverflowMethod", "line": 83, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fold"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "crop"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ellipsis"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore"}], "uses_pep604_syntax": false}}}, "Pager": {".class": "SymbolTableNode", "cross_ref": "rich.pager.Pager", "kind": "Gdef"}, "PagerContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.PagerContext", "name": "PagerContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.PagerContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.PagerContext", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.PagerContext.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.console.PagerContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of PagerContext", "ret_type": "rich.console.PagerContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.PagerContext.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["rich.console.PagerContext", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of PagerContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "console", "pager", "styles", "links"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.PagerContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "console", "pager", "styles", "links"], "arg_types": ["rich.console.PagerContext", "rich.console.Console", {".class": "UnionType", "items": ["rich.pager.Pager", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PagerContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_console": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.PagerContext._console", "name": "_console", "type": "rich.console.Console"}}, "links": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.PagerContext.links", "name": "links", "type": "builtins.bool"}}, "pager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.PagerContext.pager", "name": "pager", "type": "rich.pager.Pager"}}, "styles": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.PagerContext.styles", "name": "styles", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.PagerContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.PagerContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pretty": {".class": "SymbolTableNode", "cross_ref": "rich.pretty.Pretty", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "Region": {".class": "SymbolTableNode", "cross_ref": "rich.region.Region", "kind": "Gdef"}, "RenderHook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["process_renderables", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.RenderHook", "name": "RenderHook", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "rich.console.RenderHook", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.RenderHook", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "process_renderables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "renderables"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "rich.console.RenderHook.process_renderables", "name": "process_renderables", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "renderables"], "arg_types": ["rich.console.RenderHook", {".class": "Instance", "args": ["rich.console.ConsoleRenderable"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_renderables of RenderHook", "ret_type": {".class": "Instance", "args": ["rich.console.ConsoleRenderable"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "rich.console.RenderHook.process_renderables", "name": "process_renderables", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "renderables"], "arg_types": ["rich.console.RenderHook", {".class": "Instance", "args": ["rich.console.ConsoleRenderable"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_renderables of RenderHook", "ret_type": {".class": "Instance", "args": ["rich.console.ConsoleRenderable"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.RenderHook.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.RenderHook", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RenderResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "rich.console.RenderResult", "line": 284, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}, {".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "RenderableType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "rich.console.RenderableType", "line": 280, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["rich.console.ConsoleRenderable", "rich.console.RichCast", "builtins.str"], "uses_pep604_syntax": false}}}, "ReprHighlighter": {".class": "SymbolTableNode", "cross_ref": "rich.highlighter.Repr<PERSON><PERSON>lighter", "kind": "Gdef"}, "RichCast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__rich__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.RichCast", "name": "RichCast", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "rich.console.RichCast", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.RichCast", "builtins.object"], "names": {".class": "SymbolTable", "__rich__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "rich.console.RichCast.__rich__", "name": "__rich__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["rich.console.RichCast"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich__ of RichCast", "ret_type": {".class": "UnionType", "items": ["rich.console.ConsoleRenderable", "rich.console.RichCast", "builtins.str"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.RichCast.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.RichCast", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SVG_EXPORT_THEME": {".class": "SymbolTableNode", "cross_ref": "rich.terminal_theme.SVG_EXPORT_THEME", "kind": "Gdef"}, "Screen": {".class": "SymbolTableNode", "cross_ref": "rich.screen.Screen", "kind": "Gdef"}, "ScreenContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.ScreenContext", "name": "ScreenContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.ScreenContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.ScreenContext", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ScreenContext.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.console.ScreenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of ScreenContext", "ret_type": "rich.console.ScreenContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ScreenContext.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["rich.console.ScreenContext", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of ScreenContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "console", "hide_cursor", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ScreenContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "console", "hide_cursor", "style"], "arg_types": ["rich.console.ScreenContext", "rich.console.Console", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScreenContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_changed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.ScreenContext._changed", "name": "_changed", "type": "builtins.bool"}}, "console": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.ScreenContext.console", "name": "console", "type": "rich.console.Console"}}, "hide_cursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.ScreenContext.hide_cursor", "name": "hide_cursor", "type": "builtins.bool"}}, "screen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.ScreenContext.screen", "name": "screen", "type": "rich.screen.Screen"}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["self", "renderables", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ScreenContext.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["self", "renderables", "style"], "arg_types": ["rich.console.ScreenContext", {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderableType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.style.StyleType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of ScreenContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ScreenContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.ScreenContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ScreenUpdate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.ScreenUpdate", "name": "ScreenUpdate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.ScreenUpdate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.ScreenUpdate", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "lines", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ScreenUpdate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "lines", "x", "y"], "arg_types": ["rich.console.ScreenUpdate", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScreenUpdate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_console__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ScreenUpdate.__rich_console__", "name": "__rich_console__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "console", "options"], "arg_types": ["rich.console.ScreenUpdate", "rich.console.Console", "rich.console.ConsoleOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rich_console__ of ScreenUpdate", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "rich.console.RenderResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.ScreenUpdate._lines", "name": "_lines", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "rich.segment.Segment"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "x": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.ScreenUpdate.x", "name": "x", "type": "builtins.int"}}, "y": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.ScreenUpdate.y", "name": "y", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ScreenUpdate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.ScreenUpdate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Segment": {".class": "SymbolTableNode", "cross_ref": "rich.segment.Segment", "kind": "Gdef"}, "Status": {".class": "SymbolTableNode", "cross_ref": "rich.status.Status", "kind": "Gdef"}, "Style": {".class": "SymbolTableNode", "cross_ref": "rich.style.Style", "kind": "Gdef"}, "StyleType": {".class": "SymbolTableNode", "cross_ref": "rich.style.StyleType", "kind": "Gdef"}, "Styled": {".class": "SymbolTableNode", "cross_ref": "rich.styled.Styled", "kind": "Gdef"}, "SystemPager": {".class": "SymbolTableNode", "cross_ref": "rich.pager.SystemPager", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TerminalTheme": {".class": "SymbolTableNode", "cross_ref": "rich.terminal_theme.TerminalTheme", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "cross_ref": "rich.text.Text", "kind": "Gdef"}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef"}, "TextType": {".class": "SymbolTableNode", "cross_ref": "rich.text.TextType", "kind": "Gdef"}, "Theme": {".class": "SymbolTableNode", "cross_ref": "rich.theme.Theme", "kind": "Gdef"}, "ThemeContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich.console.ThemeContext", "name": "ThemeContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich.console.ThemeContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rich.console", "mro": ["rich.console.ThemeContext", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ThemeContext.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["rich.console.ThemeContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of ThemeContext", "ret_type": "rich.console.ThemeContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ThemeContext.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["rich.console.ThemeContext", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of ThemeContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "console", "theme", "inherit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.ThemeContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "console", "theme", "inherit"], "arg_types": ["rich.console.ThemeContext", "rich.console.Console", "rich.theme.Theme", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ThemeContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "console": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.ThemeContext.console", "name": "console", "type": "rich.console.Console"}}, "inherit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.ThemeContext.inherit", "name": "inherit", "type": "builtins.bool"}}, "theme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "rich.console.ThemeContext.theme", "name": "theme", "type": "rich.theme.Theme"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich.console.ThemeContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich.console.ThemeContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ThemeStack": {".class": "SymbolTableNode", "cross_ref": "rich.theme.ThemeStack", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WINDOWS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console.WINDOWS", "name": "WINDOWS", "type": "builtins.bool"}}, "WindowsConsoleFeatures": {".class": "SymbolTableNode", "cross_ref": "rich._windows.WindowsConsoleFeatures", "kind": "Gdef"}, "_COLOR_SYSTEMS_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console._COLOR_SYSTEMS_NAMES", "name": "_COLOR_SYSTEMS_NAMES", "type": {".class": "Instance", "args": ["rich.color.ColorSystem", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_STDERR_FILENO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console._STDERR_FILENO", "name": "_STDERR_FILENO", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}], "uses_pep604_syntax": false}}}, "_STDIN_FILENO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console._STDIN_FILENO", "name": "_STDIN_FILENO", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}], "uses_pep604_syntax": false}}}, "_STDOUT_FILENO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console._STDOUT_FILENO", "name": "_STDOUT_FILENO", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}], "uses_pep604_syntax": false}}}, "_STD_STREAMS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console._STD_STREAMS", "name": "_STD_STREAMS", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_STD_STREAMS_OUTPUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console._STD_STREAMS_OUTPUT", "name": "_STD_STREAMS_OUTPUT", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_TERM_COLORS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console._TERM_COLORS", "name": "_TERM_COLORS", "type": {".class": "Instance", "args": ["builtins.str", "rich.color.ColorSystem"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.console.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.console.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.console.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.console.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.console.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich.console.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_emoji_replace": {".class": "SymbolTableNode", "cross_ref": "rich._emoji_replace._emoji_replace", "kind": "Gdef"}, "_is_jupyter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console._is_jupyter", "name": "_is_jupyter", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_jupyter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_null_highlighter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console._null_highlighter", "name": "_null_highlighter", "type": "rich.highlighter.<PERSON><PERSON><PERSON><PERSON><PERSON>er"}}, "_svg_hash": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["svg_main_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console._svg_hash", "name": "_svg_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["svg_main_code"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_svg_hash", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_windows_console_features": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "rich.console._windows_console_features", "name": "_windows_console_features", "type": {".class": "UnionType", "items": ["rich._windows.WindowsConsoleFeatures", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "blend_rgb": {".class": "SymbolTableNode", "cross_ref": "rich.color.blend_rgb", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "ceil": {".class": "SymbolTableNode", "cross_ref": "math.ceil", "kind": "Gdef"}, "console": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich.console.console", "name": "console", "type": "rich.console.Console"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "detect_legacy_windows": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.detect_legacy_windows", "name": "detect_legacy_windows", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_legacy_windows", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "errors": {".class": "SymbolTableNode", "cross_ref": "rich.errors", "kind": "Gdef"}, "escape": {".class": "SymbolTableNode", "cross_ref": "html.escape", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}, "get_fileno": {".class": "SymbolTableNode", "cross_ref": "rich._fileno.get_fileno", "kind": "Gdef"}, "get_windows_console_features": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.get_windows_console_features", "name": "get_windows_console_features", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_windows_console_features", "ret_type": "rich._windows.WindowsConsoleFeatures", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getpass": {".class": "SymbolTableNode", "cross_ref": "getpass.getpass", "kind": "Gdef"}, "group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["fit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich.console.group", "name": "group", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["fit"], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "group", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "_render_stack of Traceback", "ret_type": "rich.console.Group", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_expandable": {".class": "SymbolTableNode", "cross_ref": "rich.pretty.is_expandable", "kind": "Gdef"}, "isclass": {".class": "SymbolTableNode", "cross_ref": "inspect.isclass", "kind": "Gdef"}, "islice": {".class": "SymbolTableNode", "cross_ref": "itertools.islice", "kind": "Gdef"}, "measure_renderables": {".class": "SymbolTableNode", "cross_ref": "rich.measure.measure_renderables", "kind": "Gdef"}, "monotonic": {".class": "SymbolTableNode", "cross_ref": "time.monotonic", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "platform": {".class": "SymbolTableNode", "cross_ref": "platform", "kind": "Gdef"}, "render_markup": {".class": "SymbolTableNode", "cross_ref": "rich.markup.render", "kind": "Gdef"}, "render_scope": {".class": "SymbolTableNode", "cross_ref": "rich.scope.render_scope", "kind": "Gdef"}, "rich_cast": {".class": "SymbolTableNode", "cross_ref": "rich.protocol.rich_cast", "kind": "Gdef"}, "runtime_checkable": {".class": "SymbolTableNode", "cross_ref": "typing.runtime_checkable", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "themes": {".class": "SymbolTableNode", "cross_ref": "rich.themes", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef"}, "zlib": {".class": "SymbolTableNode", "cross_ref": "zlib", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\rich\\console.py"}