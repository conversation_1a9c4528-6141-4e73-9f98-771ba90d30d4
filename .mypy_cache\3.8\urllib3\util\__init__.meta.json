{"data_mtime": 1753876885, "dep_lines": [4, 5, 6, 7, 8, 18, 19, 20, 2, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["urllib3.util.connection", "urllib3.util.request", "urllib3.util.response", "urllib3.util.retry", "urllib3.util.ssl_", "urllib3.util.timeout", "urllib3.util.url", "urllib3.util.wait", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "52460d96b307e3cb7da56a187fc2c4e597de3c87", "id": "urllib3.util", "ignore_all": true, "interface_hash": "fc4222d06f6008b960bd6400372068c52b38c17a", "mtime": 1750470649, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\urllib3\\util\\__init__.py", "plugin_data": null, "size": 1001, "suppressed": [], "version_id": "1.15.0"}