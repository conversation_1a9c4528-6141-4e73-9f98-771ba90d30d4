{".class": "MypyFile", "_fullname": "fastapi.openapi.docs", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Doc", "kind": "Gdef"}, "HTMLResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.HTMLResponse", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.docs.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.docs.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.docs.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.docs.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.docs.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.openapi.docs.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "get_redoc_html": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 5, 5, 5], "arg_names": ["openapi_url", "title", "redoc_js_url", "redoc_favicon_url", "with_google_fonts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi.openapi.docs.get_redoc_html", "name": "get_redoc_html", "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5], "arg_names": ["openapi_url", "title", "redoc_js_url", "redoc_favicon_url", "with_google_fonts"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_redoc_html", "ret_type": "starlette.responses.HTMLResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_swagger_ui_html": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["openapi_url", "title", "swagger_js_url", "swagger_css_url", "swagger_favicon_url", "oauth2_redirect_url", "init_oauth", "swagger_ui_parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi.openapi.docs.get_swagger_ui_html", "name": "get_swagger_ui_html", "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["openapi_url", "title", "swagger_js_url", "swagger_css_url", "swagger_favicon_url", "oauth2_redirect_url", "init_oauth", "swagger_ui_parameters"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_swagger_ui_html", "ret_type": "starlette.responses.HTMLResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_swagger_ui_oauth2_redirect_html": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi.openapi.docs.get_swagger_ui_oauth2_redirect_html", "name": "get_swagger_ui_oauth2_redirect_html", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_swagger_ui_oauth2_redirect_html", "ret_type": "starlette.responses.HTMLResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "jsonable_encoder": {".class": "SymbolTableNode", "cross_ref": "fastapi.encoders.jsonable_encoder", "kind": "Gdef"}, "swagger_ui_default_parameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "fastapi.openapi.docs.swagger_ui_default_parameters", "name": "swagger_ui_default_parameters", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\fastapi\\openapi\\docs.py"}