{"data_mtime": 1753839571, "dep_lines": [4, 12, 16, 19, 21, 22, 23, 24, 25, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 5, 5, 5, 5, 5, 10, 20, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["http.cookies", "email.utils", "urllib.parse", "anyio.to_thread", "starlette.background", "starlette.concurrency", "starlette.datastructures", "starlette.requests", "starlette.types", "__future__", "<PERSON><PERSON><PERSON>", "http", "json", "os", "re", "stat", "typing", "warnings", "datetime", "functools", "mimetypes", "secrets", "anyio", "builtins", "traitlets.utils.warnings", "pprint", "sys", "logging", "_frozen_importlib", "_typeshed", "abc", "enum", "typing_extensions", "urllib"], "hash": "087c6d450e0b382205f7e7926f452f3ed7c4a7fb", "id": "starlette.responses", "ignore_all": true, "interface_hash": "64bf7292af7c6db23cce2b8698b75487ab428d92", "mtime": 1750258710, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\starlette\\responses.py", "plugin_data": null, "size": 20172, "suppressed": [], "version_id": "1.15.0"}