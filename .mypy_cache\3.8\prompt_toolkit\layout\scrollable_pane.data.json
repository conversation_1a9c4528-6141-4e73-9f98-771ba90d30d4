{".class": "MypyFile", "_fullname": "prompt_toolkit.layout.scrollable_pane", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnyDimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.AnyDimension", "kind": "Gdef", "module_public": false}, "Char": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.screen.Char", "kind": "Gdef", "module_public": false}, "Container": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.Container", "kind": "Gdef", "module_public": false}, "Dimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.Dimension", "kind": "Gdef", "module_public": false}, "FilterOrBool": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.FilterOrBool", "kind": "Gdef", "module_public": false}, "KeyBindingsBase": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "kind": "Gdef", "module_public": false}, "MAX_AVAILABLE_HEIGHT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.scrollable_pane.MAX_AVAILABLE_HEIGHT", "name": "MAX_AVAILABLE_HEIGHT", "type": "builtins.int"}}, "MouseEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.mouse_events.MouseEvent", "kind": "Gdef", "module_public": false}, "MouseHandler": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.mouse_handlers.MouseHandler", "kind": "Gdef", "module_public": false}, "MouseHandlers": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "kind": "Gdef", "module_public": false}, "Point": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.data_structures.Point", "kind": "Gdef", "module_public": false}, "Screen": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.screen.Screen", "kind": "Gdef", "module_public": false}, "ScrollOffsets": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.ScrollOffsets", "kind": "Gdef", "module_public": false}, "ScrollablePane": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.containers.Container"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane", "name": "ScrollablePane", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.scrollable_pane", "mro": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane", "prompt_toolkit.layout.containers.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "content", "scroll_offsets", "keep_cursor_visible", "keep_focused_window_visible", "max_available_height", "width", "height", "show_scrollbar", "display_arrows", "up_arrow_symbol", "down_arrow_symbol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "content", "scroll_offsets", "keep_cursor_visible", "keep_focused_window_visible", "max_available_height", "width", "height", "show_scrollbar", "display_arrows", "up_arrow_symbol", "down_arrow_symbol"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane", "prompt_toolkit.layout.containers.Container", {".class": "UnionType", "items": ["prompt_toolkit.layout.containers.ScrollOffsets", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScrollablePane", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ScrollablePane", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_clip_point_to_visible_area": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "point", "write_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane._clip_point_to_visible_area", "name": "_clip_point_to_visible_area", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "point", "write_position"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, "prompt_toolkit.layout.screen.WritePosition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clip_point_to_visible_area of ScrollablePane", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy_over_mouse_handlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "mouse_handlers", "temp_mouse_handlers", "write_position", "virtual_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane._copy_over_mouse_handlers", "name": "_copy_over_mouse_handlers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "mouse_handlers", "temp_mouse_handlers", "write_position", "virtual_width"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_over_mouse_handlers of ScrollablePane", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy_over_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "screen", "temp_screen", "write_position", "virtual_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane._copy_over_screen", "name": "_copy_over_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "screen", "temp_screen", "write_position", "virtual_width"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.screen.WritePosition", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_over_screen of ScrollablePane", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy_over_write_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "screen", "temp_screen", "write_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane._copy_over_write_positions", "name": "_copy_over_write_positions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "screen", "temp_screen", "write_position"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.screen.WritePosition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_over_write_positions of ScrollablePane", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_draw_scrollbar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "write_position", "content_height", "screen"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane._draw_scrollbar", "name": "_draw_scrollbar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "write_position", "content_height", "screen"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane", "prompt_toolkit.layout.screen.WritePosition", "builtins.int", "prompt_toolkit.layout.screen.Screen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_draw_scrollbar of ScrollablePane", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_window_visible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "visible_height", "virtual_height", "visible_win_write_pos", "cursor_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane._make_window_visible", "name": "_make_window_visible", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "visible_height", "virtual_height", "visible_win_write_pos", "cursor_position"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane", "builtins.int", "builtins.int", "prompt_toolkit.layout.screen.WritePosition", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_window_visible of ScrollablePane", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.content", "name": "content", "type": "prompt_toolkit.layout.containers.Container"}}, "display_arrows": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.display_arrows", "name": "display_arrows", "type": "prompt_toolkit.filters.base.Filter"}}, "down_arrow_symbol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.down_arrow_symbol", "name": "down_arrow_symbol", "type": "builtins.str"}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of ScrollablePane", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_key_bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.get_key_bindings", "name": "get_key_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_key_bindings of ScrollablePane", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.height", "name": "height", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}}}, "is_modal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.is_modal", "name": "is_modal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_modal of ScrollablePane", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keep_cursor_visible": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.keep_cursor_visible", "name": "keep_cursor_visible", "type": "prompt_toolkit.filters.base.Filter"}}, "keep_focused_window_visible": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.keep_focused_window_visible", "name": "keep_focused_window_visible", "type": "prompt_toolkit.filters.base.Filter"}}, "max_available_height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.max_available_height", "name": "max_available_height", "type": "builtins.int"}}, "preferred_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of ScrollablePane", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of ScrollablePane", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of ScrollablePane", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scroll_offsets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.scroll_offsets", "name": "scroll_offsets", "type": "prompt_toolkit.layout.containers.ScrollOffsets"}}, "show_scrollbar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.show_scrollbar", "name": "show_scrollbar", "type": "prompt_toolkit.filters.base.Filter"}}, "up_arrow_symbol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.up_arrow_symbol", "name": "up_arrow_symbol", "type": "builtins.str"}}, "vertical_scroll": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.vertical_scroll", "name": "vertical_scroll", "type": "builtins.int"}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.width", "name": "width", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}}}, "write_to_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.write_to_screen", "name": "write_to_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "arg_types": ["prompt_toolkit.layout.scrollable_pane.ScrollablePane", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_to_screen of ScrollablePane", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.scrollable_pane.ScrollablePane.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.scrollable_pane.ScrollablePane", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WritePosition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.screen.WritePosition", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.scrollable_pane.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.scrollable_pane.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.scrollable_pane.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.scrollable_pane.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.scrollable_pane.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.scrollable_pane.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.scrollable_pane.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "sum_layout_dimensions": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.sum_layout_dimensions", "kind": "Gdef", "module_public": false}, "to_dimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.to_dimension", "kind": "Gdef", "module_public": false}, "to_filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.to_filter", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\layout\\scrollable_pane.py"}