{"data_mtime": 1753839570, "dep_lines": [15, 130, 157, 217, 217, 221, 227, 271, 275, 280, 292, 304, 309, 362, 392, 406, 429, 433, 444, 448, 491, 497, 512, 529, 544, 557, 576, 582, 600, 615, 621, 14, 17, 172, 202, 202, 202, 202, 202, 202, 202, 202, 202, 202, 202, 217, 637, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 181, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 10, 10, 10, 10, 10, 10, 10, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["numpy.core._internal", "numpy._typing._callable", "numpy._typing._extended_precision", "numpy.core.def<PERSON><PERSON><PERSON>", "numpy.core.records", "numpy.core.function_base", "numpy.core.fromnumeric", "numpy.core._asarray", "numpy.core._type_aliases", "numpy.core._ufunc_config", "numpy.core.arrayprint", "numpy.core.einsumfunc", "numpy.core.multiarray", "numpy.core.numeric", "numpy.core.numerictypes", "numpy.core.shape_base", "numpy.lib.arraypad", "numpy.lib.arraysetops", "numpy.lib.arrayterator", "numpy.lib.function_base", "numpy.lib.histograms", "numpy.lib.index_tricks", "numpy.lib.nanfunctions", "numpy.lib.npyio", "numpy.lib.polynomial", "numpy.lib.shape_base", "numpy.lib.stride_tricks", "numpy.lib.twodim_base", "numpy.lib.type_check", "numpy.lib.ufunclike", "numpy.lib.utils", "numpy._pytesttester", "numpy._typing", "collections.abc", "numpy.ctypeslib", "numpy.exceptions", "numpy.fft", "numpy.lib", "numpy.linalg", "numpy.ma", "numpy.polynomial", "numpy.random", "numpy.testing", "numpy.version", "numpy.dtypes", "numpy.core", "numpy.matrixlib", "builtins", "sys", "os", "mmap", "ctypes", "array", "datetime", "enum", "abc", "types", "contextlib", "typing", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "logging", "_ctypes", "_frozen_importlib", "_typeshed", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "numpy._typing._ufunc"], "hash": "f9c321b8b76b369ff8fe66f199eb2380dfb108b7", "id": "numpy", "ignore_all": true, "interface_hash": "fea4d7af5da241426e0650c512bbcde03f8c4ff9", "mtime": 1750470725, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\numpy\\__init__.pyi", "plugin_data": null, "size": 158502, "suppressed": [], "version_id": "1.15.0"}