{".class": "MypyFile", "_fullname": "prompt_toolkit.key_binding.bindings.mouse", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ALT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.ALT", "name": "ALT", "type": {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "ALT_CONTROL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.ALT_CONTROL", "name": "ALT_CONTROL", "type": {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "CONTROL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.CONTROL", "name": "CONTROL", "type": {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "E": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "prompt_toolkit.key_binding.bindings.mouse.E", "line": 25, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "prompt_toolkit.key_binding.key_processor.KeyPressEvent"}}, "KeyBindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "kind": "Gdef", "module_public": false}, "KeyPress": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_processor.KeyPress", "kind": "Gdef", "module_public": false}, "KeyPressEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_processor.KeyPressEvent", "kind": "Gdef", "module_public": false}, "Keys": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.keys.Keys", "kind": "Gdef", "module_public": false}, "LEFT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.LEFT", "name": "LEFT", "type": "prompt_toolkit.mouse_events.MouseButton"}}, "MIDDLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.MIDDLE", "name": "MIDDLE", "type": "prompt_toolkit.mouse_events.MouseButton"}}, "MOUSE_DOWN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.MOUSE_DOWN", "name": "MOUSE_DOWN", "type": "prompt_toolkit.mouse_events.MouseEventType"}}, "MOUSE_MOVE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.MOUSE_MOVE", "name": "MOUSE_MOVE", "type": "prompt_toolkit.mouse_events.MouseEventType"}}, "MOUSE_UP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.MOUSE_UP", "name": "MOUSE_UP", "type": "prompt_toolkit.mouse_events.MouseEventType"}}, "MouseButton": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.mouse_events.MouseButton", "kind": "Gdef", "module_public": false}, "MouseEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.mouse_events.MouseEvent", "kind": "Gdef", "module_public": false}, "MouseEventType": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.mouse_events.MouseEventType", "kind": "Gdef", "module_public": false}, "MouseModifier": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.mouse_events.MouseModifier", "kind": "Gdef", "module_public": false}, "NO_BUTTON": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.NO_BUTTON", "name": "NO_BUTTON", "type": "prompt_toolkit.mouse_events.MouseButton"}}, "NO_MODIFIER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.NO_MODIFIER", "name": "NO_MODIFIER", "type": {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "NotImplementedOrNone": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.NotImplementedOrNone", "kind": "Gdef", "module_public": false}, "Point": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.data_structures.Point", "kind": "Gdef", "module_public": false}, "RIGHT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.RIGHT", "name": "RIGHT", "type": "prompt_toolkit.mouse_events.MouseButton"}}, "SCROLL_DOWN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.SCROLL_DOWN", "name": "SCROLL_DOWN", "type": "prompt_toolkit.mouse_events.MouseEventType"}}, "SCROLL_UP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.SCROLL_UP", "name": "SCROLL_UP", "type": "prompt_toolkit.mouse_events.MouseEventType"}}, "SHIFT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.SHIFT", "name": "SHIFT", "type": {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "SHIFT_ALT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.SHIFT_ALT", "name": "SHIFT_ALT", "type": {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "SHIFT_ALT_CONTROL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.SHIFT_ALT_CONTROL", "name": "SHIFT_ALT_CONTROL", "type": {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "SHIFT_CONTROL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.SHIFT_CONTROL", "name": "SHIFT_CONTROL", "type": {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "UNKNOWN_BUTTON": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.UNKNOWN_BUTTON", "name": "UNKNOWN_BUTTON", "type": "prompt_toolkit.mouse_events.MouseButton"}}, "UNKNOWN_MODIFIER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.UNKNOWN_MODIFIER", "name": "UNKNOWN_MODIFIER", "type": {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "load_mouse_bindings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.bindings.mouse.load_mouse_bindings", "name": "load_mouse_bindings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_mouse_bindings", "ret_type": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "typical_mouse_events": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.typical_mouse_events", "name": "typical_mouse_events", "type": {".class": "Instance", "args": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["prompt_toolkit.mouse_events.MouseButton", "prompt_toolkit.mouse_events.MouseEventType", {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["prompt_toolkit.mouse_events.MouseButton", "prompt_toolkit.mouse_events.MouseEventType", {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "urxvt_mouse_events": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.urxvt_mouse_events", "name": "urxvt_mouse_events", "type": {".class": "Instance", "args": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["prompt_toolkit.mouse_events.MouseButton", "prompt_toolkit.mouse_events.MouseEventType", {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["prompt_toolkit.mouse_events.MouseButton", "prompt_toolkit.mouse_events.MouseEventType", {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "xterm_sgr_mouse_events": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.mouse.xterm_sgr_mouse_events", "name": "xterm_sgr_mouse_events", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["prompt_toolkit.mouse_events.MouseButton", "prompt_toolkit.mouse_events.MouseEventType", {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["prompt_toolkit.mouse_events.MouseButton", "prompt_toolkit.mouse_events.MouseEventType", {".class": "Instance", "args": ["prompt_toolkit.mouse_events.MouseModifier"], "extra_attrs": null, "type_ref": "builtins.frozenset"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\key_binding\\bindings\\mouse.py"}