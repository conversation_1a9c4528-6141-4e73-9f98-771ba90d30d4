{"data_mtime": 1753839580, "dep_lines": [28, 31, 30, 7, 8, 9, 10, 11, 12, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["traitlets.config.configurable", "IPython.utils.decorators", "IPython.paths", "atexit", "datetime", "re", "sqlite3", "threading", "pathlib", "traitlets", "builtins", "json", "traitlets.utils.warnings", "pprint", "typing", "functools", "os", "sys", "logging", "IPython.utils", "_frozen_importlib", "_sqlite3", "_thread", "_typeshed", "abc", "enum", "traitlets.config", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel", "typing_extensions"], "hash": "2d30f2a146763ff828cf4d0e57fb52d8b72a2faa", "id": "IPython.core.history", "ignore_all": true, "interface_hash": "a06613c4a99d6a04c5f8b83b97bff85f262c0eea", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\history.py", "plugin_data": null, "size": 34821, "suppressed": ["decorator"], "version_id": "1.15.0"}