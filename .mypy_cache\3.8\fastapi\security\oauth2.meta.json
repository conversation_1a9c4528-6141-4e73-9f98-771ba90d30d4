{"data_mtime": 1753839581, "dep_lines": [4, 7, 8, 3, 6, 9, 10, 1, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.openapi.models", "fastapi.security.base", "fastapi.security.utils", "fastapi.exceptions", "fastapi.param_functions", "starlette.requests", "starlette.status", "typing", "typing_extensions", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "sys", "logging", "_frozen_importlib", "abc", "enum", "fastapi.openapi", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "starlette"], "hash": "c540283ff1849237df847f59abef697504254319", "id": "fastapi.security.oauth2", "ignore_all": true, "interface_hash": "3de08aebca1126ad90de62dce1e420d9b7972cb8", "mtime": 1750471027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\fastapi\\security\\oauth2.py", "plugin_data": null, "size": 21585, "suppressed": [], "version_id": "1.15.0"}