{".class": "MypyFile", "_fullname": "prompt_toolkit.styles.style_transformation", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_public": false}, "ANSI_COLOR_NAMES": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.ANSI_COLOR_NAMES", "kind": "Gdef", "module_public": false}, "AdjustBrightnessStyleTransformation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation", "name": "AdjustBrightnessStyleTransformation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.styles.style_transformation", "mro": ["prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation", "prompt_toolkit.styles.style_transformation.StyleTransformation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "min_brightness", "max_brightness"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "min_brightness", "max_brightness"], "arg_types": ["prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.utils.AnyFloat"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.utils.AnyFloat"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AdjustBrightnessStyleTransformation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_color_to_rgb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "color"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation._color_to_rgb", "name": "_color_to_rgb", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "color"], "arg_types": ["prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_color_to_rgb of AdjustBrightnessStyleTransformation", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_interpolate_brightness": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "value", "min_brightness", "max_brightness"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation._interpolate_brightness", "name": "_interpolate_brightness", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "value", "min_brightness", "max_brightness"], "arg_types": ["prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation", "builtins.float", "builtins.float", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_interpolate_brightness of AdjustBrightnessStyleTransformation", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invalidation_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation.invalidation_hash", "name": "invalidation_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidation_hash of AdjustBrightnessStyleTransformation", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_brightness": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation.max_brightness", "name": "max_brightness", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.utils.AnyFloat"}}}, "min_brightness": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation.min_brightness", "name": "min_brightness", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.utils.AnyFloat"}}}, "transform_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation.transform_attrs", "name": "transform_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_attrs of AdjustBrightnessStyleTransformation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AnyFloat": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.AnyFloat", "kind": "Gdef", "module_public": false}, "Attrs": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.Attrs", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ConditionalStyleTransformation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation", "name": "ConditionalStyleTransformation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.styles.style_transformation", "mro": ["prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation", "prompt_toolkit.styles.style_transformation.StyleTransformation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "style_transformation", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "style_transformation", "filter"], "arg_types": ["prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation", "prompt_toolkit.styles.style_transformation.StyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConditionalStyleTransformation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation.filter", "name": "filter", "type": "prompt_toolkit.filters.base.Filter"}}, "invalidation_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation.invalidation_hash", "name": "invalidation_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidation_hash of ConditionalStyleTransformation", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style_transformation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation.style_transformation", "name": "style_transformation", "type": "prompt_toolkit.styles.style_transformation.StyleTransformation"}}, "transform_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation.transform_attrs", "name": "transform_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_attrs of ConditionalStyleTransformation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DummyStyleTransformation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.styles.style_transformation.DummyStyleTransformation", "name": "DummyStyleTransformation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.DummyStyleTransformation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.styles.style_transformation", "mro": ["prompt_toolkit.styles.style_transformation.DummyStyleTransformation", "prompt_toolkit.styles.style_transformation.StyleTransformation", "builtins.object"], "names": {".class": "SymbolTable", "invalidation_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.DummyStyleTransformation.invalidation_hash", "name": "invalidation_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.styles.style_transformation.DummyStyleTransformation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidation_hash of DummyStyleTransformation", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transform_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.DummyStyleTransformation.transform_attrs", "name": "transform_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["prompt_toolkit.styles.style_transformation.DummyStyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_attrs of DummyStyleTransformation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.styles.style_transformation.DummyStyleTransformation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.styles.style_transformation.DummyStyleTransformation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DynamicStyleTransformation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.styles.style_transformation.DynamicStyleTransformation", "name": "DynamicStyleTransformation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.DynamicStyleTransformation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.styles.style_transformation", "mro": ["prompt_toolkit.styles.style_transformation.DynamicStyleTransformation", "prompt_toolkit.styles.style_transformation.StyleTransformation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "get_style_transformation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.DynamicStyleTransformation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "get_style_transformation"], "arg_types": ["prompt_toolkit.styles.style_transformation.DynamicStyleTransformation", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.styles.style_transformation.StyleTransformation", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DynamicStyleTransformation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_style_transformation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.styles.style_transformation.DynamicStyleTransformation.get_style_transformation", "name": "get_style_transformation", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.styles.style_transformation.StyleTransformation", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invalidation_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.DynamicStyleTransformation.invalidation_hash", "name": "invalidation_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.styles.style_transformation.DynamicStyleTransformation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidation_hash of DynamicStyleTransformation", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transform_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.DynamicStyleTransformation.transform_attrs", "name": "transform_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["prompt_toolkit.styles.style_transformation.DynamicStyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_attrs of DynamicStyleTransformation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.styles.style_transformation.DynamicStyleTransformation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.styles.style_transformation.DynamicStyleTransformation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FilterOrBool": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.FilterOrBool", "kind": "Gdef", "module_public": false}, "Hashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef", "module_public": false}, "OPPOSITE_ANSI_COLOR_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.styles.style_transformation.OPPOSITE_ANSI_COLOR_NAMES", "name": "OPPOSITE_ANSI_COLOR_NAMES", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ReverseStyleTransformation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.styles.style_transformation.ReverseStyleTransformation", "name": "ReverseStyleTransformation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.ReverseStyleTransformation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.styles.style_transformation", "mro": ["prompt_toolkit.styles.style_transformation.ReverseStyleTransformation", "prompt_toolkit.styles.style_transformation.StyleTransformation", "builtins.object"], "names": {".class": "SymbolTable", "transform_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.ReverseStyleTransformation.transform_attrs", "name": "transform_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["prompt_toolkit.styles.style_transformation.ReverseStyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_attrs of ReverseStyleTransformation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.styles.style_transformation.ReverseStyleTransformation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.styles.style_transformation.ReverseStyleTransformation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "SetDefaultColorStyleTransformation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation", "name": "SetDefaultColorStyleTransformation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.styles.style_transformation", "mro": ["prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation", "prompt_toolkit.styles.style_transformation.StyleTransformation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fg", "bg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fg", "bg"], "arg_types": ["prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation", {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SetDefaultColorStyleTransformation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation.bg", "name": "bg", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "fg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation.fg", "name": "fg", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "invalidation_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation.invalidation_hash", "name": "invalidation_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidation_hash of SetDefaultColorStyleTransformation", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transform_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation.transform_attrs", "name": "transform_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_attrs of SetDefaultColorStyleTransformation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StyleTransformation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["transform_attrs", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.styles.style_transformation.StyleTransformation", "name": "StyleTransformation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "prompt_toolkit.styles.style_transformation.StyleTransformation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.styles.style_transformation", "mro": ["prompt_toolkit.styles.style_transformation.StyleTransformation", "builtins.object"], "names": {".class": "SymbolTable", "invalidation_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.StyleTransformation.invalidation_hash", "name": "invalidation_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidation_hash of StyleTransformation", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transform_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "prompt_toolkit.styles.style_transformation.StyleTransformation.transform_attrs", "name": "transform_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["prompt_toolkit.styles.style_transformation.StyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_attrs of StyleTransformation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.styles.style_transformation.StyleTransformation.transform_attrs", "name": "transform_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["prompt_toolkit.styles.style_transformation.StyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_attrs of StyleTransformation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.styles.style_transformation.StyleTransformation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.styles.style_transformation.StyleTransformation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SwapLightAndDarkStyleTransformation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.styles.style_transformation.SwapLightAndDarkStyleTransformation", "name": "SwapLightAndDarkStyleTransformation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.SwapLightAndDarkStyleTransformation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.styles.style_transformation", "mro": ["prompt_toolkit.styles.style_transformation.SwapLightAndDarkStyleTransformation", "prompt_toolkit.styles.style_transformation.StyleTransformation", "builtins.object"], "names": {".class": "SymbolTable", "transform_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.SwapLightAndDarkStyleTransformation.transform_attrs", "name": "transform_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["prompt_toolkit.styles.style_transformation.SwapLightAndDarkStyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_attrs of SwapLightAndDarkStyleTransformation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.styles.style_transformation.SwapLightAndDarkStyleTransformation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.styles.style_transformation.SwapLightAndDarkStyleTransformation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MergedStyleTransformation": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.styles.style_transformation._MergedStyleTransformation", "name": "_MergedStyleTransformation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation._MergedStyleTransformation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.styles.style_transformation", "mro": ["prompt_toolkit.styles.style_transformation._MergedStyleTransformation", "prompt_toolkit.styles.style_transformation.StyleTransformation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "style_transformations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation._MergedStyleTransformation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "style_transformations"], "arg_types": ["prompt_toolkit.styles.style_transformation._MergedStyleTransformation", {".class": "Instance", "args": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _MergedStyleTransformation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invalidation_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation._MergedStyleTransformation.invalidation_hash", "name": "invalidation_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.styles.style_transformation._MergedStyleTransformation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidation_hash of _MergedStyleTransformation", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style_transformations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.styles.style_transformation._MergedStyleTransformation.style_transformations", "name": "style_transformations", "type": {".class": "Instance", "args": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "transform_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation._MergedStyleTransformation.transform_attrs", "name": "transform_attrs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["prompt_toolkit.styles.style_transformation._MergedStyleTransformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transform_attrs of _MergedStyleTransformation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.styles.style_transformation._MergedStyleTransformation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.styles.style_transformation._MergedStyleTransformation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.styles.style_transformation.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.style_transformation.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.style_transformation.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.style_transformation.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.style_transformation.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.style_transformation.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.style_transformation.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "get_opposite_color": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["colorname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "prompt_toolkit.styles.style_transformation.get_opposite_color", "name": "get_opposite_color", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["colorname"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_opposite_color", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "prompt_toolkit.styles.style_transformation.get_opposite_color", "name": "get_opposite_color", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["colorname"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_opposite_color", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hls_to_rgb": {".class": "SymbolTableNode", "cross_ref": "colorsys.hls_to_rgb", "kind": "Gdef", "module_public": false}, "memoized": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.cache.memoized", "kind": "Gdef", "module_public": false}, "merge_style_transformations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["style_transformations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.style_transformation.merge_style_transformations", "name": "merge_style_transformations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["style_transformations"], "arg_types": [{".class": "Instance", "args": ["prompt_toolkit.styles.style_transformation.StyleTransformation"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_style_transformations", "ret_type": "prompt_toolkit.styles.style_transformation.StyleTransformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_color": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style.parse_color", "kind": "Gdef", "module_public": false}, "rgb_to_hls": {".class": "SymbolTableNode", "cross_ref": "colorsys.rgb_to_hls", "kind": "Gdef", "module_public": false}, "to_filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.to_filter", "kind": "Gdef", "module_public": false}, "to_float": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.to_float", "kind": "Gdef", "module_public": false}, "to_str": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.to_str", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\styles\\style_transformation.py"}