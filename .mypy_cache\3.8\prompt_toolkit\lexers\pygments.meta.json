{"data_mtime": 1753839575, "dep_lines": [15, 16, 17, 19, 13, 14, 7, 9, 10, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22, 206, 207], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 25, 20, 20], "dependencies": ["prompt_toolkit.formatted_text.base", "prompt_toolkit.formatted_text.utils", "prompt_toolkit.styles.pygments", "prompt_toolkit.lexers.base", "prompt_toolkit.document", "prompt_toolkit.filters", "__future__", "re", "abc", "typing", "builtins", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "sys", "logging", "_frozen_importlib", "_typeshed", "enum", "prompt_toolkit.filters.base", "prompt_toolkit.filters.utils", "prompt_toolkit.mouse_events"], "hash": "cc713d35cbda5147cc80d7021f108b62efb7d2d5", "id": "prompt_toolkit.lexers.pygments", "ignore_all": true, "interface_hash": "1da563f6bb091eed8c3d095b407bea9397434ee8", "mtime": 1708667605, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\lexers\\pygments.py", "plugin_data": null, "size": 11921, "suppressed": ["pygments.lexer", "pygments.lexers", "pygments.util"], "version_id": "1.15.0"}