{"data_mtime": 1753839578, "dep_lines": [7, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 1, 3, 4, 6, 16, 706, 786, 787, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 10, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["markdown_it.token", "rich.table", "rich.box", "rich._loop", "rich._stack", "rich.console", "rich.containers", "rich.jupyter", "rich.panel", "rich.rule", "rich.segment", "rich.style", "rich.syntax", "rich.text", "__future__", "sys", "typing", "markdown_it", "rich", "<PERSON><PERSON><PERSON><PERSON>", "io", "pydoc", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "logging", "_frozen_importlib", "_io", "_typeshed", "abc", "datetime", "enum", "markdown_it.main", "markdown_it.renderer", "markdown_it.utils", "rich.theme", "typing_extensions"], "hash": "bb35d9783028a23f5550c10d0e21b19e3faf9b60", "id": "rich.markdown", "ignore_all": true, "interface_hash": "354ae439ce6ebc7d13d4995ee19eb15eee8a3010", "mtime": 1723156628, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\rich\\markdown.py", "plugin_data": null, "size": 26167, "suppressed": [], "version_id": "1.15.0"}