{"data_mtime": 1753839573, "dep_lines": [26, 33, 35, 24, 30, 31, 34, 38, 39, 41, 42, 49, 324, 413, 2, 4, 5, 6, 7, 8, 10, 20, 22, 24, 46, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 5, 10, 5, 5, 10, 5, 5, 10, 10, 20, 25, 5, 20, 20, 20, 20, 20, 20, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.compat", "_pytest.mark.structures", "_pytest._code", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.outcomes", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "_pytest.main", "_pytest.mark", "_pytest.fixtures", "__future__", "abc", "functools", "inspect", "os", "pathlib", "typing", "warnings", "pluggy", "_pytest", "typing_extensions", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "sys", "logging", "_frozen_importlib", "pluggy._hooks"], "hash": "38cad0e4553207028c655ac98855eb93f4aad79e", "id": "_pytest.nodes", "ignore_all": true, "interface_hash": "1980d5769793e722060b5dc5e2e290c205ce6bcc", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\nodes.py", "plugin_data": null, "size": 26483, "suppressed": [], "version_id": "1.15.0"}