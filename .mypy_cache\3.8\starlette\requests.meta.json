{"data_mtime": **********, "dep_lines": [5, 9, 10, 11, 12, 13, 16, 18, 19, 1, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 25, 25, 25, 5, 10, 10, 20, 10, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["http.cookies", "starlette._utils", "starlette.datastructures", "starlette.exceptions", "starlette.formparsers", "starlette.types", "python_multipart.multipart", "starlette.applications", "starlette.routing", "__future__", "json", "typing", "http", "anyio", "builtins", "re", "traitlets.utils.warnings", "pprint", "functools", "os", "sys", "logging", "_frozen_importlib", "_typeshed", "abc", "anyio._core", "anyio._core._tasks", "contextlib", "json.decoder", "python_multipart", "typing_extensions"], "hash": "c0022897a46eec53b3af9bc6eb374b62b64bce04", "id": "starlette.requests", "ignore_all": true, "interface_hash": "143890b89a0dca4addd3e7959e3c86413ff09c9c", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\starlette\\requests.py", "plugin_data": null, "size": 11693, "suppressed": [], "version_id": "1.15.0"}