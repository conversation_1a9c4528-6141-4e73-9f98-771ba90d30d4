{"data_mtime": 1753839573, "dep_lines": [36, 37, 10, 28, 29, 30, 38, 39, 44, 46, 48, 54, 3, 5, 6, 7, 8, 9, 11, 12, 13, 14, 24, 26, 28, 52, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 5, 10, 5, 10, 10, 20, 25, 5, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.config.compat", "importlib.util", "_pytest.nodes", "_pytest._code", "_pytest.config", "_pytest.outcomes", "_pytest.pathlib", "_pytest.reports", "_pytest.runner", "_pytest.warning_types", "_pytest.fixtures", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "fnmatch", "functools", "importlib", "os", "pathlib", "sys", "typing", "warnings", "pluggy", "_pytest", "typing_extensions", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "logging", "_frozen_importlib", "_pytest.config.exceptions", "abc", "enum", "pluggy._hooks", "pluggy._manager", "pluggy._tracing"], "hash": "e9d0270c196214afe2c5d8e98a1b82efc2de89a9", "id": "_pytest.main", "ignore_all": true, "interface_hash": "6cb4dfb8aac27697b2128c69b41d231435ec1880", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\main.py", "plugin_data": null, "size": 37416, "suppressed": [], "version_id": "1.15.0"}