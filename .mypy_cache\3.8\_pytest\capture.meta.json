{"data_mtime": 1753839573, "dep_lines": [35, 33, 36, 37, 39, 42, 4, 6, 7, 8, 9, 11, 12, 13, 14, 15, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 10, 5, 5, 5, 25, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.nodes", "_pytest.reports", "__future__", "abc", "collections", "contextlib", "io", "os", "sys", "tempfile", "types", "typing", "typing_extensions", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "logging", "_frozen_importlib", "_io", "_typeshed", "pluggy", "pluggy._hooks"], "hash": "ac762c38ba5c57546325d10c81623362391a22fc", "id": "_pytest.capture", "ignore_all": true, "interface_hash": "b1f1740d00bc06669426a88c8c79a87bdd4857a0", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\capture.py", "plugin_data": null, "size": 35330, "suppressed": [], "version_id": "1.15.0"}