<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM Proxy Server - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .nav-tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
        }
        
        .nav-tab:hover:not(.active) {
            background: #f0f0f0;
        }
        
        .tab-content {
            display: none;
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .tab-content.active {
            display: block;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 60px;
            font-family: inherit;
        }

        /* Settings Section Styling */
        .settings-container h3 {
            color: #667eea;
            margin: 2rem 0 1rem 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e0e0e0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .settings-container h3:first-child {
            margin-top: 0;
        }

        .settings-note {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 2rem;
        }

        .settings-note p {
            margin: 0 0 0.5rem 0;
            font-weight: 600;
            color: #495057;
        }

        .settings-note ul {
            margin: 0;
            padding-left: 1.5rem;
        }

        .settings-note li {
            margin-bottom: 0.25rem;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .settings-note code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: #dc3545;
            color: white;
            border: 1px solid #dc3545;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn-danger:hover {
            background: #c82333;
            border-color: #bd2130;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 0.9rem;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            table-layout: auto;
        }

        /* Keep table styles for other tables that might still use them */
        .table-container {
            overflow-x: auto;
            margin: 1rem 0;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-enabled {
            background: #d4edda;
            color: #155724;
        }
        
        .status-disabled {
            background: #f8d7da;
            color: #721c24;
        }

        .status-orphaned {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-admin {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-user {
            background: #fff3cd;
            color: #856404;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 1rem;
            border: 1px solid transparent;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .modal-header h2 {
            color: #667eea;
            margin: 0;
        }
        
        .close {
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            color: #999;
        }
        
        .close:hover {
            color: #333;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .models-info {
            min-width: 400px;
            max-width: 600px;
        }

        .models-list {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-top: 10px;
            background: #fff;
            width: 100%;
            min-width: 500px;
        }

        .models-header {
            padding: 12px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .models-search {
            flex: 1;
            min-width: 200px;
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .models-sort {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
            background: white;
        }

        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 16px;
            padding: 16px;
        }

        .model-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px;
            background: #fff;
            transition: all 0.2s ease;
            position: relative;
            word-wrap: break-word;
            overflow-wrap: break-word;
            min-width: 0; /* Allow flex items to shrink */
        }

        .model-card:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }

        .model-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .model-name {
            font-weight: bold;
            color: #333;
            font-size: 0.95em;
            margin: 0;
            word-break: break-word;
        }

        .model-actions {
            display: flex;
            gap: 4px;
            flex-shrink: 0;
        }

        .model-details {
            color: #666;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .model-detail-row {
            display: flex;
            justify-content: space-between;
            margin: 4px 0;
        }

        .model-size {
            font-weight: 500;
            color: #555;
        }

        .model-tag {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 0.75em;
            margin: 2px 2px 0 0;
        }

        .model-date {
            color: #888;
            font-size: 0.8em;
        }

        /* Host Cards Layout */
        .hosts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .host-card {
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
            position: relative;
        }

        .host-card:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
            transform: translateY(-2px);
        }

        .host-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .host-card-title {
            flex: 1;
        }

        .host-card-id {
            font-size: 0.85em;
            color: #666;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .host-card-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
            margin: 0;
            word-break: break-word;
        }

        .host-card-actions {
            display: flex;
            gap: 8px;
            flex-shrink: 0;
            margin-left: 12px;
        }

        .host-card-body {
            display: grid;
            gap: 12px;
        }

        .host-info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
        }

        .host-info-label {
            font-weight: 500;
            color: #555;
            font-size: 0.9em;
            min-width: 100px;
        }

        .host-info-value {
            color: #333;
            font-size: 0.9em;
            text-align: right;
            word-break: break-all;
            max-width: 200px;
        }

        .host-url {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
        }

        .host-models-section {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #f0f0f0;
        }

        .host-models-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .host-models-title {
            font-weight: 600;
            color: #333;
            font-size: 0.95em;
        }

        /* Metrics Dashboard Styles */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }

        .metric-card:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }

        .metric-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .metric-card-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .metric-card-icon {
            font-size: 1.5em;
            opacity: 0.7;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin: 8px 0;
        }

        .metric-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 4px;
        }

        .metric-change {
            font-size: 0.85em;
            font-weight: 500;
        }

        .metric-change.positive {
            color: #28a745;
        }

        .metric-change.negative {
            color: #dc3545;
        }

        .metric-change.neutral {
            color: #6c757d;
        }

        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .metrics-table th,
        .metrics-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .metrics-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }

        .metrics-table tr:hover {
            background: #f8f9fa;
        }

        /* Model Tree Table Styles */
        .model-tree-table .model-row {
            cursor: pointer;
            background: #f8f9fa;
            font-weight: 600;
        }

        .model-tree-table .model-row:hover {
            background: #e9ecef;
        }

        .model-tree-table .model-row td:first-child {
            position: relative;
            padding-left: 20px;
        }

        .model-tree-table .model-row td:first-child::before {
            content: '▶';
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
            color: #667eea;
            font-size: 0.8em;
        }

        .model-tree-table .model-row.expanded td:first-child::before {
            transform: translateY(-50%) rotate(90deg);
        }

        .model-tree-table .host-row {
            background: #fff;
            display: none;
        }

        .model-tree-table .host-row.visible {
            display: table-row;
        }

        .model-tree-table .host-row td:first-child {
            padding-left: 40px;
            position: relative;
            color: #666;
        }

        .model-tree-table .host-row td:first-child::before {
            content: '└─';
            position: absolute;
            left: 24px;
            top: 50%;
            transform: translateY(-50%);
            color: #ccc;
            font-size: 0.9em;
        }

        .model-tree-table .host-row:hover {
            background: #f8f9fa;
        }

        .model-tree-table .model-summary {
            font-weight: 600;
            color: #333;
        }

        .model-tree-table .host-name {
            font-weight: normal;
            color: #666;
        }

        .metric-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }

        .metric-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .metric-timestamp {
            font-size: 0.8em;
            color: #888;
            text-align: right;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #f0f0f0;
        }

        /* Request Breakdown Styles */
        .request-categories {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .request-category {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            background: #fff;
        }

        .category-header {
            display: flex;
            align-items: center;
            padding: 16px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .category-icon {
            font-size: 1.5em;
            margin-right: 12px;
        }

        .category-info {
            flex: 1;
        }

        .category-name {
            margin: 0 0 4px 0;
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
        }

        .category-description {
            margin: 0;
            font-size: 0.9em;
            color: #666;
        }

        .category-stats {
            text-align: right;
        }

        .category-count {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }

        .category-percentage {
            font-size: 0.9em;
            color: #666;
        }

        .category-endpoints {
            padding: 0;
        }

        .endpoint-row {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.9em;
        }

        .endpoint-row:last-child {
            border-bottom: none;
        }

        .endpoint-row:hover {
            background: #f8f9fa;
        }

        .endpoint-name {
            flex: 1;
            font-family: 'Courier New', monospace;
            color: #495057;
        }

        .endpoint-count {
            margin-right: 16px;
            font-weight: 600;
            color: #333;
        }

        .endpoint-time {
            color: #666;
            font-size: 0.85em;
        }

        .no-data {
            text-align: center;
            color: #666;
            padding: 20px;
            font-style: italic;
        }

        /* Enhanced Health Display */
        .health-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .health-indicator {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            transition: all 0.2s ease;
        }

        .health-indicator:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }

        .health-indicator.healthy {
            border-color: #28a745;
            background: #f8fff9;
        }

        .health-indicator.degraded {
            border-color: #ffc107;
            background: #fffdf5;
        }

        .health-indicator.unhealthy {
            border-color: #dc3545;
            background: #fff5f5;
        }

        .health-icon {
            font-size: 2em;
            margin-bottom: 8px;
        }

        .health-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .health-value {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .health-description {
            font-size: 0.85em;
            color: #666;
        }

        .health-details {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .health-detail-row {
            display: flex;
            flex-direction: column;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .health-detail-row:last-child {
            border-bottom: none;
        }

        .health-detail-main {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .health-detail-label {
            font-weight: 500;
            color: #555;
        }

        .health-detail-value {
            color: #333;
            font-weight: 500;
        }

        .health-detail-description {
            font-size: 12px;
            color: #777;
            font-style: italic;
            margin-top: 2px;
            line-height: 1.3;
        }

        .models-summary {
            font-size: 0.9em;
            color: #555;
            margin-top: 5px;
        }

        .btn-success {
            background: #28a745;
            color: white;
            border: 1px solid #28a745;
        }

        .btn-success:hover {
            background: #218838;
            border-color: #1e7e34;
        }

        .model-item .btn {
            font-size: 0.8em;
            padding: 2px 6px;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .hosts-grid {
                grid-template-columns: 1fr;
                gap: 16px;
                margin-top: 16px;
            }

            .host-card {
                padding: 16px;
            }

            .host-card-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .host-card-actions {
                align-self: flex-end;
                margin-left: 0;
            }

            .host-info-value {
                max-width: 150px;
                font-size: 0.85em;
            }

            .models-grid {
                grid-template-columns: 1fr;
                gap: 8px;
                padding: 8px;
            }

            .models-header {
                flex-direction: column;
                align-items: stretch;
            }

            .models-search {
                min-width: auto;
                margin-bottom: 8px;
            }

            .model-card {
                padding: 10px;
            }

            .model-card-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .model-actions {
                align-self: flex-end;
            }
        }

        /* Empty state styling */
        .models-empty {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .models-empty-icon {
            font-size: 3em;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .actions {
            display: flex;
            gap: 10px;
            margin-top: 1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }

        /* Performance Profiling Styles */
        .profiling-controls {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e9ecef;
        }

        .profiling-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .profiling-form-full {
            grid-column: 1 / -1;
        }

        .profiling-selection {
            background: white;
            border-radius: 6px;
            padding: 1rem;
            border: 1px solid #ddd;
        }

        .profiling-selection h4 {
            margin-bottom: 0.5rem;
            color: #667eea;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .selection-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .selection-controls {
            display: flex;
            gap: 0.5rem;
        }

        .selection-controls .btn {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .selection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 0.5rem;
            max-height: 200px;
            overflow-y: auto;
        }

        .selection-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 0.85rem;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
        }

        .selection-item:hover:not(.disabled) {
            background: #e9ecef;
            border-color: #007bff;
        }

        .selection-item.disabled {
            background: #f1f3f4;
            border-color: #dee2e6;
            opacity: 0.6;
        }

        .selection-item.disabled input {
            cursor: not-allowed;
        }

        .selection-item.disabled label {
            cursor: not-allowed;
            color: #6c757d !important;
        }

        .selection-item input[type="checkbox"] {
            margin-right: 0.5rem;
        }

        .prompt-selector-container {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .prompt-selector-header {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .prompt-selector-header label {
            font-weight: 500;
            color: #333;
            margin: 0;
        }

        #prompt-selector {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: white;
            font-size: 0.9rem;
        }

        #prompt-selector:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .profiling-prompt {
            width: 100%;
            min-height: 100px;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-family: inherit;
            resize: vertical;
        }

        .profiling-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .profiling-progress {
            flex: 1;
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            display: none;
        }

        .profiling-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .profiling-status {
            font-size: 0.9rem;
            color: #666;
            display: none;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .result-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }

        .result-title {
            font-weight: 600;
            color: #333;
        }

        .result-status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .result-status.success {
            background: #d4edda;
            color: #155724;
        }

        .result-status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .result-status.running {
            background: #fff3cd;
            color: #856404;
        }

        .result-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .result-metric {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 4px;
            text-align: center;
        }

        .result-metric-value {
            font-weight: 600;
            color: #667eea;
            font-size: 1.1rem;
        }

        .result-metric-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .result-response {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 1rem;
            font-size: 0.9rem;
            line-height: 1.5;
            border-left: 3px solid #667eea;
        }

        .response-preview, .response-full {
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 0.75rem;
        }

        .response-full {
            max-height: 400px;
        }

        .response-content {
            background: #ffffff;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            padding: 1rem;
            margin: 0.5rem 0;
        }

        .response-content pre {
            background: #f6f8fa;
            border: 1px solid #d0d7de;
            border-radius: 6px;
            padding: 1rem;
            margin: 0.5rem 0;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.85rem;
            line-height: 1.45;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .response-content pre code {
            background: transparent;
            border: none;
            padding: 0;
            font-size: inherit;
        }

        /* Syntax highlighting for common languages */
        .language-python .keyword { color: #d73a49; font-weight: bold; }
        .language-python .string { color: #032f62; }
        .language-python .comment { color: #6a737d; font-style: italic; }
        .language-python .function { color: #6f42c1; }

        .language-javascript .keyword { color: #d73a49; font-weight: bold; }
        .language-javascript .string { color: #032f62; }
        .language-javascript .comment { color: #6a737d; font-style: italic; }
        .language-javascript .function { color: #6f42c1; }

        .response-content code {
            background: #f6f8fa;
            border: 1px solid #d0d7de;
            border-radius: 3px;
            padding: 0.2em 0.4em;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.85em;
        }

        .response-content h1, .response-content h2, .response-content h3 {
            color: #24292f;
            margin-top: 1.5rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .response-content h1 { font-size: 1.25rem; }
        .response-content h2 { font-size: 1.1rem; }
        .response-content h3 { font-size: 1rem; }

        .response-content ul, .response-content ol {
            padding-left: 1.5rem;
            margin: 0.5rem 0;
        }

        .response-content li {
            margin: 0.25rem 0;
        }

        .response-content p {
            margin: 0.75rem 0;
            line-height: 1.6;
        }

        .response-content blockquote {
            border-left: 4px solid #d0d7de;
            padding-left: 1rem;
            margin: 1rem 0;
            color: #656d76;
            font-style: italic;
        }

        .expand-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .expand-btn:hover {
            background: #5a67d8;
        }

        .expand-btn:active {
            background: #4c51bf;
        }

        /* Chat Interface Formatting */
        .chat-message {
            margin-bottom: 15px;
        }

        .chat-message-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chat-message-content {
            padding: 12px;
            border-radius: 8px;
            line-height: 1.6;
        }

        .chat-message-user .chat-message-content {
            background: #e3f2fd;
            border-left: 3px solid #2196f3;
        }

        .chat-message-assistant .chat-message-content {
            background: #f1f8e9;
            border-left: 3px solid #4caf50;
        }

        .chat-message-error .chat-message-content {
            background: #ffebee;
            border-left: 3px solid #f44336;
            color: #d32f2f;
        }

        .chat-message-metrics {
            font-size: 0.75em;
            color: #666;
            margin-top: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .chat-message-content pre {
            background: rgba(0,0,0,0.05);
            border: 1px solid rgba(0,0,0,0.1);
            border-radius: 4px;
            padding: 0.75rem;
            margin: 0.5rem 0;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .chat-message-content code {
            background: rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.1);
            border-radius: 3px;
            padding: 0.2em 0.4em;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
        }

        .chat-message-content h1, .chat-message-content h2, .chat-message-content h3 {
            color: #24292f;
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .chat-message-content ul, .chat-message-content ol {
            padding-left: 1.5rem;
            margin: 0.5rem 0;
        }

        .chat-message-content li {
            margin: 0.25rem 0;
        }

        /* Thinking/Reasoning Display */
        .thinking-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin: 0.75rem 0;
            overflow: hidden;
        }

        .thinking-header {
            background: #e9ecef;
            padding: 0.5rem 0.75rem;
            font-weight: 600;
            color: #495057;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            user-select: none;
        }

        .thinking-header:hover {
            background: #dee2e6;
        }

        .thinking-toggle {
            font-size: 0.8em;
            transition: transform 0.2s ease;
        }

        .thinking-toggle.expanded {
            transform: rotate(90deg);
        }

        .thinking-content {
            padding: 0.75rem;
            border-top: 1px solid #e9ecef;
            background: #fafbfc;
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            font-size: 0.9rem;
            line-height: 1.6;
            color: #2c3e50;
            max-height: 400px;
            overflow-y: auto;
        }

        .thinking-paragraph {
            margin: 0 0 0.75rem 0;
            padding: 0;
            text-align: justify;
        }

        .thinking-paragraph:last-child {
            margin-bottom: 0;
        }

        .thinking-step {
            margin: 0 0 0.75rem 0;
            padding: 0.5rem;
            background: #e8f4fd;
            border-left: 3px solid #007bff;
            border-radius: 3px;
        }

        .thinking-calculation {
            margin: 0 0 0.5rem 0;
            padding: 0.4rem 0.6rem;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .thinking-conclusion {
            margin: 0 0 0.75rem 0;
            padding: 0.5rem;
            background: #d4edda;
            border-left: 3px solid #28a745;
            border-radius: 3px;
        }

        .streaming-indicator {
            color: #6c757d;
            font-style: italic;
            animation: pulse 1.5s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            from { opacity: 0.6; }
            to { opacity: 1.0; }
        }

        .thinking-container {
            margin-bottom: 0.5rem;
        }

        .thinking-metrics {
            font-size: 0.75em;
            color: #6c757d;
            padding: 0.5rem 0.75rem;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 1rem;
        }

        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .summary-table th,
        .summary-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .summary-table th {
            background: #667eea;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .summary-table tr:hover {
            background: #f8f9fa;
        }

        .summary-table .metric-cell {
            text-align: center;
            font-weight: 500;
        }

        .summary-table .best-metric {
            background: #d4edda;
            color: #155724;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .profiling-form {
                grid-template-columns: 1fr;
            }

            .results-grid {
                grid-template-columns: 1fr;
            }

            .result-metrics {
                grid-template-columns: 1fr;
            }

            .profiling-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .summary-table {
                font-size: 0.8rem;
            }

            .summary-table th,
            .summary-table td {
                padding: 0.5rem;
            }
            
            .table {
                font-size: 0.9rem;
            }
            
            .modal-content {
                margin: 10% auto;
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 LLM Proxy Server Admin Dashboard</h1>
            <p>Monitor performance, test models, manage hosts, and configure your multi-LLM infrastructure</p>
        </div>
        
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">📊 Overview</button>
            <button class="nav-tab" onclick="showTab('hosts')">🖥️ Hosts</button>
            <button class="nav-tab" onclick="showTab('users')">👥 Users</button>
            <button class="nav-tab" onclick="showTab('metrics')">📈 Metrics</button>
            <button class="nav-tab" onclick="showTab('profiling')">🚀 Performance</button>
            <button class="nav-tab" onclick="showTab('backups')">💾 Backups</button>
            <button class="nav-tab" onclick="showTab('settings')">⚙️ Settings</button>
        </div>
        
        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-hosts">-</div>
                    <div class="stat-label">Total Hosts</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="enabled-hosts">-</div>
                    <div class="stat-label">Enabled Hosts</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-users">-</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="admin-users">-</div>
                    <div class="stat-label">Admin Users</div>
                </div>
            </div>

            <!-- Token Statistics Overview -->
            <div class="card">
                <h3>🎯 Token Usage Overview</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="total-tokens">-</div>
                        <div class="stat-label">Total Tokens</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="prompt-tokens">-</div>
                        <div class="stat-label">Prompt Tokens</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="completion-tokens">-</div>
                        <div class="stat-label">Completion Tokens</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="token-efficiency">-</div>
                        <div class="stat-label">Output Ratio</div>
                    </div>
                </div>
            </div>

            <!-- Real-time Performance -->
            <div class="card">
                <h3>⚡ Real-time Performance</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="prompt-tokens-per-sec">-</div>
                        <div class="stat-label">Input Processing (tok/s)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="completion-tokens-per-sec">-</div>
                        <div class="stat-label">Output Generation (tok/s)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="active-connections">-</div>
                        <div class="stat-label">Active Connections</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="success-rate">-</div>
                        <div class="stat-label">Success Rate (%)</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h3>🏥 System Health</h3>
                    <button class="btn btn-secondary" onclick="refreshSystemHealth()">🔄 Refresh</button>
                </div>
                <div id="health-status">Loading...</div>
            </div>
            
            <div class="card">
                <h3>📈 Quick Actions</h3>
                <div class="actions">
                    <button class="btn btn-primary" onclick="showTab('hosts')">Manage Hosts</button>
                    <button class="btn btn-primary" onclick="showTab('users')">Manage Users</button>
                    <button class="btn btn-secondary" onclick="exportConfig()">Export Config</button>
                    <button class="btn btn-secondary" onclick="refreshData()">Refresh Data</button>
                </div>
            </div>
        </div>
        
        <!-- Hosts Tab -->
        <div id="hosts" class="tab-content">
            <div class="card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h3>🖥️ Host Configuration</h3>
                    <div>
                        <button class="btn btn-secondary" onclick="toggleChatInterface()" style="margin-right: 8px;">💬 Chat Interface</button>
                        <button class="btn btn-primary" onclick="showAddHostModal()">Add Host</button>
                    </div>
                </div>
                <div id="hosts-table-container">
                    <div class="loading"></div> Loading hosts...
                </div>
            </div>
        </div>
        
        <!-- Users Tab -->
        <div id="users" class="tab-content">
            <div class="card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h3>👥 User Management</h3>
                    <button class="btn btn-primary" onclick="showAddUserModal()">Add User</button>
                </div>
                <div id="users-table-container">
                    <div class="loading"></div> Loading users...
                </div>
            </div>
        </div>

        <!-- Metrics Tab -->
        <div id="metrics" class="tab-content">
            <div class="card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h3>📈 System Metrics</h3>
                    <div>
                        <button class="btn btn-secondary" onclick="saveMetrics()" style="margin-right: 8px;" title="Save metrics to disk">💾 Save</button>
                        <button class="btn btn-danger" onclick="resetMetrics()" style="margin-right: 8px;" title="Reset all metrics data">🗑️ Reset</button>
                        <button class="btn btn-secondary" onclick="refreshMetrics()" title="Refresh metrics display">🔄 Refresh</button>
                    </div>
                </div>
                <div id="metrics-container">
                    <div class="loading"></div> Loading metrics...
                </div>
            </div>
        </div>

        <!-- Performance Profiling Tab -->
        <div id="profiling" class="tab-content">
            <div class="card">
                <h3>🚀 Performance Profiling</h3>
                <p>Test prompts across multiple models and hosts to analyze performance characteristics and identify optimal configurations.</p>

                <div class="profiling-controls">
                    <div class="profiling-form">
                        <div class="profiling-selection">
                            <div class="selection-header">
                                <h4 id="host-selection-title">🖥️ Select Hosts</h4>
                                <div class="selection-controls">
                                    <button class="btn btn-sm btn-outline-secondary" onclick="selectAllHosts()">Select All</button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="clearAllHosts()">Clear All</button>
                                </div>
                            </div>
                            <div class="selection-grid" id="host-selection">
                                <div class="loading"></div> Loading hosts...
                            </div>
                        </div>

                        <div class="profiling-selection">
                            <div class="selection-header">
                                <h4 id="model-selection-title">🤖 Select Models</h4>
                                <div class="selection-controls">
                                    <button class="btn btn-sm btn-outline-secondary" onclick="selectAllModels()">Select All</button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="clearAllModels()">Clear All</button>
                                </div>
                            </div>
                            <div class="selection-grid" id="model-selection">
                                <div class="loading"></div> Loading models...
                            </div>
                        </div>

                        <div class="profiling-form-full">
                            <h4>💬 Test Prompt</h4>
                            <div class="prompt-selector-container">
                                <div class="prompt-selector-header">
                                    <label for="prompt-selector">Choose a predefined prompt:</label>
                                    <select id="prompt-selector" class="form-select" onchange="selectPredefinedPrompt()">
                                        <option value="">-- Select a prompt or write your own --</option>
                                        <optgroup label="🖥️ Programming & Development">
                                            <option value="Write a Python function to sort a list using merge sort algorithm">Write a Python function to sort a list using merge sort algorithm</option>
                                            <option value="Create a REST API endpoint in Node.js for user authentication">Create a REST API endpoint in Node.js for user authentication</option>
                                            <option value="Explain the difference between SQL and NoSQL databases with examples">Explain the difference between SQL and NoSQL databases with examples</option>
                                            <option value="Write a JavaScript function to validate email addresses using regex">Write a JavaScript function to validate email addresses using regex</option>
                                            <option value="How do you implement a binary search tree in Java?">How do you implement a binary search tree in Java?</option>
                                        </optgroup>
                                        <optgroup label="⚖️ Legal & Compliance">
                                            <option value="Explain the key principles of GDPR data protection regulation">Explain the key principles of GDPR data protection regulation</option>
                                            <option value="What are the main differences between copyright and trademark law?">What are the main differences between copyright and trademark law?</option>
                                            <option value="Draft a basic non-disclosure agreement template">Draft a basic non-disclosure agreement template</option>
                                            <option value="Explain the concept of intellectual property in software development">Explain the concept of intellectual property in software development</option>
                                        </optgroup>
                                        <optgroup label="🏥 Medical & Healthcare">
                                            <option value="Explain the process of photosynthesis in plants at the cellular level">Explain the process of photosynthesis in plants at the cellular level</option>
                                            <option value="What are the symptoms and treatment options for Type 2 diabetes?">What are the symptoms and treatment options for Type 2 diabetes?</option>
                                            <option value="Describe the human immune system and how vaccines work">Describe the human immune system and how vaccines work</option>
                                            <option value="Explain the difference between bacteria and viruses">Explain the difference between bacteria and viruses</option>
                                        </optgroup>
                                        <optgroup label="🔬 Science & Technology">
                                            <option value="Explain quantum computing in simple terms">Explain quantum computing in simple terms</option>
                                            <option value="What are the benefits of renewable energy sources?">What are the benefits of renewable energy sources?</option>
                                            <option value="Describe how machine learning algorithms work">Describe how machine learning algorithms work</option>
                                            <option value="Explain the theory of relativity and its practical applications">Explain the theory of relativity and its practical applications</option>
                                            <option value="What is CRISPR gene editing and how does it work?">What is CRISPR gene editing and how does it work?</option>
                                        </optgroup>
                                        <optgroup label="✍️ Writing & Communication">
                                            <option value="Write a professional email requesting a meeting with a client">Write a professional email requesting a meeting with a client</option>
                                            <option value="Create a compelling product description for an eco-friendly water bottle">Create a compelling product description for an eco-friendly water bottle</option>
                                            <option value="Translate 'Hello, how are you?' to Spanish, French, and German">Translate 'Hello, how are you?' to Spanish, French, and German</option>
                                        </optgroup>
                                        <optgroup label="📚 Story Telling">
                                            <option value="Write a short story about a time traveler who gets stuck in the past">Write a short story about a time traveler who gets stuck in the past</option>
                                            <option value="Create a fantasy tale about a young wizard discovering their first magical power">Create a fantasy tale about a young wizard discovering their first magical power</option>
                                            <option value="Tell a mystery story set in a small town where everyone has a secret">Tell a mystery story set in a small town where everyone has a secret</option>
                                            <option value="Write a science fiction story about the last human on Earth meeting an alien">Write a science fiction story about the last human on Earth meeting an alien</option>
                                            <option value="Create a heartwarming story about an elderly person and a stray animal">Create a heartwarming story about an elderly person and a stray animal</option>
                                            <option value="Tell a horror story that takes place in an abandoned amusement park">Tell a horror story that takes place in an abandoned amusement park</option>
                                            <option value="Write a coming-of-age story about a teenager's first day at a new school">Write a coming-of-age story about a teenager's first day at a new school</option>
                                        </optgroup>
                                        <optgroup label="🧩 Logic & Reasoning">
                                            <option value="Solve this riddle: I have cities, but no houses. I have mountains, but no trees. I have water, but no fish. What am I?">Solve this riddle: I have cities, but no houses. I have mountains, but no trees. I have water, but no fish. What am I?</option>
                                            <option value="Three friends check into a hotel room that costs $30. They each pay $10. Later, the manager realizes the room only costs $25 and gives the bellhop $5 to return. The bellhop keeps $2 and gives each friend $1 back. Now each friend has paid $9 (totaling $27) and the bellhop has $2. Where is the missing dollar?">Three friends check into a hotel room that costs $30. They each pay $10. Later, the manager realizes the room only costs $25 and gives the bellhop $5 to return. The bellhop keeps $2 and gives each friend $1 back. Now each friend has paid $9 (totaling $27) and the bellhop has $2. Where is the missing dollar?</option>
                                            <option value="You have 12 balls that look identical. 11 weigh the same, but one is either heavier or lighter. Using a balance scale only 3 times, how can you identify the different ball and determine if it's heavier or lighter?">You have 12 balls that look identical. 11 weigh the same, but one is either heavier or lighter. Using a balance scale only 3 times, how can you identify the different ball and determine if it's heavier or lighter?</option>
                                            <option value="A farmer has 17 sheep. All but 9 die. How many sheep are left alive?">A farmer has 17 sheep. All but 9 die. How many sheep are left alive?</option>
                                            <option value="You're in a room with three light switches. Each switch controls a light bulb in another room. You can only visit the other room once. How can you determine which switch controls which bulb?">You're in a room with three light switches. Each switch controls a light bulb in another room. You can only visit the other room once. How can you determine which switch controls which bulb?</option>
                                            <option value="Two trains are traveling toward each other on the same track. Train A is going 60 mph, Train B is going 40 mph. They start 200 miles apart. A bird flies back and forth between them at 80 mph until they meet. How far does the bird travel?">Two trains are traveling toward each other on the same track. Train A is going 60 mph, Train B is going 40 mph. They start 200 miles apart. A bird flies back and forth between them at 80 mph until they meet. How far does the bird travel?</option>
                                            <option value="You have a 3-gallon jug and a 5-gallon jug. How can you measure exactly 4 gallons of water?">You have a 3-gallon jug and a 5-gallon jug. How can you measure exactly 4 gallons of water?</option>
                                            <option value="In a family of 6 children, each child has at least one brother and one sister. What is the minimum number of boys and girls in the family?">In a family of 6 children, each child has at least one brother and one sister. What is the minimum number of boys and girls in the family?</option>
                                        </optgroup>
                                        <optgroup label="🔢 Mathematics">
                                            <option value="Solve for x: 3x + 7 = 22">Solve for x: 3x + 7 = 22</option>
                                            <option value="Calculate the area of a circle with radius 8 units. Show your work and round to 2 decimal places.">Calculate the area of a circle with radius 8 units. Show your work and round to 2 decimal places.</option>
                                            <option value="Find the derivative of f(x) = 3x² + 5x - 2">Find the derivative of f(x) = 3x² + 5x - 2</option>
                                            <option value="A rectangle has a length of 12 cm and a width of 8 cm. If you increase the length by 25% and decrease the width by 10%, what is the new area?">A rectangle has a length of 12 cm and a width of 8 cm. If you increase the length by 25% and decrease the width by 10%, what is the new area?</option>
                                            <option value="Solve the system of equations: 2x + 3y = 12 and x - y = 1">Solve the system of equations: 2x + 3y = 12 and x - y = 1</option>
                                            <option value="What is the sum of the first 50 positive integers? Show the formula and calculation.">What is the sum of the first 50 positive integers? Show the formula and calculation.</option>
                                            <option value="A ball is thrown upward with an initial velocity of 20 m/s. Using the equation h(t) = -4.9t² + 20t, find when the ball hits the ground.">A ball is thrown upward with an initial velocity of 20 m/s. Using the equation h(t) = -4.9t² + 20t, find when the ball hits the ground.</option>
                                            <option value="Calculate the compound interest on $1000 invested at 5% annual interest rate for 3 years, compounded quarterly.">Calculate the compound interest on $1000 invested at 5% annual interest rate for 3 years, compounded quarterly.</option>
                                            <option value="Find all prime numbers between 20 and 40. Explain how you determined each number is prime.">Find all prime numbers between 20 and 40. Explain how you determined each number is prime.</option>
                                            <option value="If sin(θ) = 3/5 and θ is in the first quadrant, find cos(θ) and tan(θ).">If sin(θ) = 3/5 and θ is in the first quadrant, find cos(θ) and tan(θ).</option>
                                        </optgroup>
                                        <optgroup label="🔐 Cryptography">
                                            <option value="Decrypt this Caesar cipher with a shift of 3: 'KHOOR ZRUOG'">Decrypt this Caesar cipher with a shift of 3: 'KHOOR ZRUOG'</option>
                                            <option value="Explain the difference between symmetric and asymmetric encryption. Give examples of each.">Explain the difference between symmetric and asymmetric encryption. Give examples of each.</option>
                                            <option value="Encode the message 'MEET AT DAWN' using a simple substitution cipher where A=D, B=E, C=F, etc.">Encode the message 'MEET AT DAWN' using a simple substitution cipher where A=D, B=E, C=F, etc.</option>
                                            <option value="What is a hash function? Explain how SHA-256 works and why it's considered secure.">What is a hash function? Explain how SHA-256 works and why it's considered secure.</option>
                                            <option value="Decrypt this Vigenère cipher using the key 'KEY': 'RIJVS UYVJN'">Decrypt this Vigenère cipher using the key 'KEY': 'RIJVS UYVJN'</option>
                                            <option value="Explain how RSA public-key cryptography works. Include the mathematical principles behind key generation.">Explain how RSA public-key cryptography works. Include the mathematical principles behind key generation.</option>
                                            <option value="What is a digital signature? How does it provide authentication and non-repudiation?">What is a digital signature? How does it provide authentication and non-repudiation?</option>
                                            <option value="Solve this Rail Fence cipher with 3 rails: 'HOREL LOWLD'">Solve this Rail Fence cipher with 3 rails: 'HOREL LOWLD'</option>
                                            <option value="Explain the concept of perfect forward secrecy in cryptographic protocols like TLS.">Explain the concept of perfect forward secrecy in cryptographic protocols like TLS.</option>
                                            <option value="What are the main differences between AES and DES encryption algorithms? Why was AES chosen to replace DES?">What are the main differences between AES and DES encryption algorithms? Why was AES chosen to replace DES?</option>
                                        </optgroup>
                                        <optgroup label="🌍 Geography & History">
                                            <option value="Explain the causes and consequences of World War I. How did it reshape the global political landscape?">Explain the causes and consequences of World War I. How did it reshape the global political landscape?</option>
                                            <option value="Describe the formation of the Himalayan mountain range. What geological processes were involved?">Describe the formation of the Himalayan mountain range. What geological processes were involved?</option>
                                            <option value="Compare and contrast the Roman Empire and the Byzantine Empire. What led to their rise and fall?">Compare and contrast the Roman Empire and the Byzantine Empire. What led to their rise and fall?</option>
                                            <option value="Explain why the Amazon rainforest is called 'the lungs of the Earth' and discuss current threats to its ecosystem.">Explain why the Amazon rainforest is called 'the lungs of the Earth' and discuss current threats to its ecosystem.</option>
                                            <option value="What were the main factors that led to the Industrial Revolution? How did it change society?">What were the main factors that led to the Industrial Revolution? How did it change society?</option>
                                            <option value="Describe the water cycle and explain how climate change is affecting global precipitation patterns.">Describe the water cycle and explain how climate change is affecting global precipitation patterns.</option>
                                            <option value="Analyze the impact of the Silk Road on ancient civilizations. What goods, ideas, and technologies were exchanged?">Analyze the impact of the Silk Road on ancient civilizations. What goods, ideas, and technologies were exchanged?</option>
                                            <option value="Explain the concept of plate tectonics. How do earthquakes and volcanic activity relate to tectonic plate movement?">Explain the concept of plate tectonics. How do earthquakes and volcanic activity relate to tectonic plate movement?</option>
                                            <option value="Discuss the causes and effects of the Great Depression of the 1930s. How did different countries respond?">Discuss the causes and effects of the Great Depression of the 1930s. How did different countries respond?</option>
                                            <option value="What factors determine a region's climate? Compare the climate patterns of tropical, temperate, and polar regions.">What factors determine a region's climate? Compare the climate patterns of tropical, temperate, and polar regions.</option>
                                        </optgroup>
                                        <optgroup label="🧬 Science & Biology">
                                            <option value="Explain the process of photosynthesis. Include the chemical equation and describe what happens in the light and dark reactions.">Explain the process of photosynthesis. Include the chemical equation and describe what happens in the light and dark reactions.</option>
                                            <option value="Describe the structure and function of DNA. How does DNA replication work?">Describe the structure and function of DNA. How does DNA replication work?</option>
                                            <option value="What is evolution by natural selection? Explain Darwin's theory with examples from nature.">What is evolution by natural selection? Explain Darwin's theory with examples from nature.</option>
                                            <option value="Explain how vaccines work to provide immunity. What is the difference between active and passive immunity?">Explain how vaccines work to provide immunity. What is the difference between active and passive immunity?</option>
                                            <option value="Describe the human circulatory system. How does blood flow through the heart, lungs, and body?">Describe the human circulatory system. How does blood flow through the heart, lungs, and body?</option>
                                            <option value="What are stem cells and why are they important in medicine? Discuss different types of stem cells.">What are stem cells and why are they important in medicine? Discuss different types of stem cells.</option>
                                            <option value="Explain the greenhouse effect and its role in climate change. Which gases contribute most to global warming?">Explain the greenhouse effect and its role in climate change. Which gases contribute most to global warming?</option>
                                            <option value="Describe the process of cellular respiration. How do cells convert glucose into ATP energy?">Describe the process of cellular respiration. How do cells convert glucose into ATP energy?</option>
                                            <option value="What is CRISPR gene editing technology? How does it work and what are its potential applications?">What is CRISPR gene editing technology? How does it work and what are its potential applications?</option>
                                            <option value="Explain the concept of biodiversity. Why is it important and what are the main threats to biodiversity today?">Explain the concept of biodiversity. Why is it important and what are the main threats to biodiversity today?</option>
                                        </optgroup>
                                        <optgroup label="📊 Business & Finance">
                                            <option value="Explain the basics of cryptocurrency and blockchain technology">Explain the basics of cryptocurrency and blockchain technology</option>
                                            <option value="What are the key components of a business plan?">What are the key components of a business plan?</option>
                                            <option value="Describe different investment strategies for retirement planning">Describe different investment strategies for retirement planning</option>
                                            <option value="How do you calculate return on investment (ROI)?">How do you calculate return on investment (ROI)?</option>
                                        </optgroup>
                                    </select>
                                </div>
                                <textarea class="profiling-prompt" id="profiling-prompt" placeholder="Enter your test prompt here or select one from the dropdown above..."></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="profiling-actions">
                        <button class="btn btn-info" onclick="refreshModelRegistry()" id="refresh-models-btn">
                            🔄 Refresh Models
                        </button>
                        <button class="btn btn-warning" onclick="cleanupOrphanedHosts()" id="cleanup-hosts-btn">
                            🧹 Cleanup Old Hosts
                        </button>
                        <button class="btn btn-primary" onclick="startProfiling()" id="start-profiling-btn">
                            🚀 Start Performance Test
                        </button>
                        <button class="btn btn-secondary" onclick="clearProfilingResults()" id="clear-results-btn">
                            🗑️ Clear Results
                        </button>
                        <div class="profiling-progress" id="profiling-progress">
                            <div class="profiling-progress-bar" id="profiling-progress-bar"></div>
                        </div>
                        <div class="profiling-status" id="profiling-status">Ready to start profiling...</div>
                    </div>
                </div>

                <div id="profiling-results">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </div>

        <!-- Backups Tab -->
        <div id="backups" class="tab-content">
            <div class="card">
                <h3>💾 Configuration Backups</h3>
                <p>Automatic backups are created when configuration changes are made.</p>
                <div id="backups-table-container">
                    <div class="loading"></div> Loading backups...
                </div>
            </div>
        </div>
        
        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <div class="card">
                <h3>⚙️ System Settings</h3>
                <div id="settings-container">
                    <div class="loading"></div> Loading settings...
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Interface Modal -->
    <div id="chatModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px; height: 80vh;">
            <div class="modal-header">
                <h2>💬 Model Chat Interface</h2>
                <span class="close" onclick="closeChatInterface()">&times;</span>
            </div>
            <div class="modal-body" style="height: calc(100% - 120px); display: flex; flex-direction: column;">
                <div style="display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;">
                    <select id="chatModel" style="flex: 1; min-width: 200px;">
                        <option value="">Select Model...</option>
                    </select>
                    <select id="chatHost" style="flex: 1; min-width: 200px;">
                        <option value="">Auto (Load Balanced)</option>
                    </select>
                    <button class="btn btn-secondary" onclick="clearChatHistory()">Clear</button>
                </div>
                <div id="chatMessages" style="flex: 1; border: 1px solid #ddd; border-radius: 6px; padding: 15px; overflow-y: auto; background: #f9f9f9; margin-bottom: 15px;">
                    <div style="text-align: center; color: #666; font-style: italic;">
                        Select a model and start chatting to test performance...
                    </div>
                </div>
                <div style="display: flex; gap: 10px;">
                    <input type="text" id="chatInput" placeholder="Type your message..." style="flex: 1;" onkeypress="if(event.key==='Enter') sendChatMessage()">
                    <button class="btn btn-primary" onclick="sendChatMessage()" id="sendButton">Send</button>
                    <button class="btn btn-danger" onclick="cancelChatMessage()" id="cancelButton" style="display: none;">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Host Modal -->
    <div id="hostModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="hostModalTitle">Add Host</h2>
                <span class="close" onclick="closeModal('hostModal')">&times;</span>
            </div>
            <form id="hostForm">
                <div class="form-group">
                    <label for="hostId">Host ID *</label>
                    <input type="text" id="hostId" name="id" required placeholder="e.g., gpu-server-1">
                </div>
                <div class="form-group">
                    <label for="hostName">Host Name</label>
                    <input type="text" id="hostName" name="name" placeholder="e.g., Main GPU Server">
                </div>
                <div class="form-group">
                    <label for="hostDescription">Description</label>
                    <textarea id="hostDescription" name="description" rows="2" placeholder="e.g., Primary server with RTX 4090, handles large models"></textarea>
                </div>
                <div class="form-group">
                    <label for="hostUrl">Host URL *</label>
                    <input type="url" id="hostUrl" name="host" required placeholder="http://*************:11434">
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="hostWeight">Weight</label>
                        <input type="number" id="hostWeight" name="weight" step="0.1" min="0.1" value="1.0">
                    </div>
                    <div class="form-group">
                        <label for="hostMaxConnections">Max Connections</label>
                        <input type="number" id="hostMaxConnections" name="max_connections" min="1" value="10">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="hostTimeout">Timeout (seconds)</label>
                        <input type="number" id="hostTimeout" name="timeout" min="1" value="300">
                    </div>
                    <div class="form-group">
                        <label for="hostEnabled">Status</label>
                        <select id="hostEnabled" name="enabled">
                            <option value="true">Enabled</option>
                            <option value="false">Disabled</option>
                        </select>
                    </div>
                </div>
                <div class="actions">
                    <button type="button" class="btn btn-secondary" onclick="testConnection()">Test Connection</button>
                    <button type="submit" class="btn btn-primary">Save Host</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('hostModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Add/Edit User Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="userModalTitle">Add User</h2>
                <span class="close" onclick="closeModal('userModal')">&times;</span>
            </div>
            <form id="userForm">
                <div class="form-group">
                    <label for="username">Username *</label>
                    <input type="text" id="username" name="username" required placeholder="Enter username">
                </div>
                <div class="form-group">
                    <label for="password">Password *</label>
                    <input type="password" id="password" name="password" required placeholder="Enter password">
                </div>
                <div class="form-group">
                    <label for="isAdmin">Role</label>
                    <select id="isAdmin" name="is_admin">
                        <option value="false">Regular User</option>
                        <option value="true">Administrator</option>
                    </select>
                </div>
                <div class="actions">
                    <button type="submit" class="btn btn-primary">Save User</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('userModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Install Model Modal -->
    <div id="installModelModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Install Model</h2>
                <span class="close" onclick="closeModal('installModelModal')">&times;</span>
            </div>
            <form id="installModelForm">
                <div class="form-group">
                    <label for="modelName">Model Name *</label>
                    <input type="text" id="modelName" name="model_name" required placeholder="e.g., llama3:8b, mistral:7b, codellama:13b">
                    <small style="color: #666; margin-top: 5px; display: block;">
                        Enter the model name from <a href="https://ollama.com/library" target="_blank">Ollama Library</a>
                    </small>
                </div>
                <div class="form-group">
                    <label>Target Host</label>
                    <input type="text" id="targetHost" readonly style="background: #f5f5f5;">
                </div>
                <div class="actions">
                    <button type="submit" class="btn btn-success">Install Model</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('installModelModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Alert Container -->
    <div id="alertContainer" style="position: fixed; top: 20px; right: 20px; z-index: 1001;"></div>
    
    <script>
        // Configuration Management JavaScript
        let currentEditingHost = null;
        let currentEditingUser = null;
        let currentChatController = null; // For cancelling chat requests
        let chatContext = null; // For maintaining conversation context

        // Authentication token (should be set from login)
        let authToken = localStorage.getItem('authToken') || '';

        // API Base URL
        const API_BASE = '';

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, initializing...');
            // Simplified initialization - just load the data
            authToken = 'disabled';
            loadOverview();
        });

        // Tab Management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');

            // Load tab-specific data
            switch(tabName) {
                case 'overview':
                    loadOverview();
                    break;
                case 'hosts':
                    loadHosts();
                    break;
                case 'users':
                    loadUsers();
                    break;
                case 'metrics':
                    loadMetrics();
                    break;
                case 'profiling':
                    loadProfilingData();
                    break;
                case 'backups':
                    loadBackups();
                    break;
                case 'settings':
                    loadSettings();
                    break;
            }
        }

        // Streaming API Helper Function
        async function streamingApiCall(endpoint, method = 'POST', data = null, abortController = null, onChunk = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            // Only add Authorization header if auth is enabled and we have a token
            if (authToken && authToken !== 'disabled') {
                options.headers['Authorization'] = `Bearer ${authToken}`;
            }

            if (data) {
                options.body = JSON.stringify(data);
            }

            // Add abort signal if provided
            if (abortController) {
                options.signal = abortController.signal;
            }

            try {
                console.log(`Streaming API Call: ${method} ${endpoint}`);
                if (data) {
                    console.log(`API Request Body:`, JSON.stringify(data, null, 2));
                }

                const response = await fetch(`${API_BASE}${endpoint}`, options);
                console.log(`Streaming API Response: ${response.status} ${response.statusText}`);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    const errorMessage = errorData.detail || `HTTP ${response.status}: ${response.statusText}`;
                    console.error(`Streaming API Error: ${errorMessage}`);
                    throw new Error(errorMessage);
                }

                // Handle streaming response
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                try {
                    while (true) {
                        const { done, value } = await reader.read();

                        if (done) break;

                        buffer += decoder.decode(value, { stream: true });

                        // Process complete lines
                        const lines = buffer.split('\n');
                        buffer = lines.pop() || ''; // Keep incomplete line in buffer

                        for (const line of lines) {
                            if (line.trim()) {
                                try {
                                    const chunk = JSON.parse(line);
                                    if (onChunk) {
                                        await onChunk(chunk);
                                    }
                                } catch (e) {
                                    console.warn('Failed to parse streaming chunk:', line, e);
                                }
                            }
                        }
                    }

                    // Process any remaining data in buffer
                    if (buffer.trim()) {
                        try {
                            const chunk = JSON.parse(buffer);
                            if (onChunk) {
                                await onChunk(chunk);
                            }
                        } catch (e) {
                            console.warn('Failed to parse final streaming chunk:', buffer, e);
                        }
                    }

                } finally {
                    reader.releaseLock();
                }

            } catch (error) {
                console.error(`Streaming API call failed:`, error);
                throw error;
            }
        }

        // API Helper Functions
        async function apiCall(endpoint, method = 'GET', data = null, abortController = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            // Only add Authorization header if auth is enabled and we have a token
            if (authToken && authToken !== 'disabled') {
                options.headers['Authorization'] = `Bearer ${authToken}`;
            }

            if (data) {
                options.body = JSON.stringify(data);
            }

            // Add abort signal if provided
            if (abortController) {
                options.signal = abortController.signal;
            }

            try {
                console.log(`API Call: ${method} ${endpoint}`);
                if (data) {
                    console.log(`API Request Body:`, JSON.stringify(data, null, 2));
                }
                const response = await fetch(`${API_BASE}${endpoint}`, options);
                console.log(`API Response: ${response.status} ${response.statusText}`);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    const errorMessage = errorData.detail || `HTTP ${response.status}: ${response.statusText}`;
                    console.error(`API Error: ${errorMessage}`);
                    throw new Error(errorMessage);
                }

                const responseData = await response.json();
                console.log(`API Data:`, responseData);
                return responseData;
            } catch (error) {
                console.error('API call failed:', error);
                throw error;
            }
        }

        // Alert System
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;

            alertContainer.appendChild(alertDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // Modal Management
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';

            // Reset forms
            if (modalId === 'hostModal') {
                document.getElementById('hostForm').reset();
                currentEditingHost = null;
            } else if (modalId === 'userModal') {
                document.getElementById('userForm').reset();
                currentEditingUser = null;
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // Data Loading Functions
        async function refreshData() {
            try {
                console.log('Starting data refresh...');
                await loadOverview();

                // Load other data based on current tab
                const activeTab = document.querySelector('.tab-content.active');
                if (activeTab) {
                    const tabId = activeTab.id;
                    switch(tabId) {
                        case 'hosts':
                            await loadHosts();
                            break;
                        case 'users':
                            await loadUsers();
                            break;
                        case 'backups':
                            await loadBackups();
                            break;
                        case 'settings':
                            await loadSettings();
                            break;
                    }
                }

                console.log('Data refresh completed successfully');
                showAlert('Data refreshed successfully');
            } catch (error) {
                console.error('Failed to refresh data:', error);
                showAlert('Failed to refresh data: ' + error.message, 'error');
            }
        }

        async function loadOverview() {
            try {
                console.log('Loading overview data...');

                // Load data sequentially to avoid Promise.all issues
                const health = await apiCall('/config/health');
                console.log('Health loaded:', health);

                const hosts = await apiCall('/config/hosts');
                console.log('Hosts loaded:', hosts.length);

                const users = await apiCall('/config/users');
                console.log('Users loaded:', users.length);

                // Update stats
                document.getElementById('total-hosts').textContent = health.hosts_count !== undefined ? health.hosts_count : hosts.length;
                document.getElementById('enabled-hosts').textContent = health.enabled_hosts !== undefined ? health.enabled_hosts : hosts.filter(h => h.enabled).length;
                document.getElementById('total-users').textContent = health.users_count !== undefined ? health.users_count : users.length;
                document.getElementById('admin-users').textContent = health.admin_users !== undefined ? health.admin_users : users.filter(u => u.is_admin).length;

                // Load and update token statistics
                try {
                    const metrics = await apiCall('/proxy/metrics');
                    const tokenStats = metrics.token_stats || {};

                    document.getElementById('total-tokens').textContent = formatNumber(tokenStats.total_tokens || 0);
                    document.getElementById('prompt-tokens').textContent = formatNumber(tokenStats.total_prompt_tokens || 0);
                    document.getElementById('completion-tokens').textContent = formatNumber(tokenStats.total_completion_tokens || 0);
                    document.getElementById('token-efficiency').textContent = calculateTokenEfficiency(tokenStats) + '%';

                    // Update real-time performance metrics
                    document.getElementById('prompt-tokens-per-sec').textContent = formatTokensPerSecond(tokenStats.global_prompt_tokens_per_sec || 0);
                    document.getElementById('completion-tokens-per-sec').textContent = formatTokensPerSecond(tokenStats.global_completion_tokens_per_sec || 0);

                    // Calculate active connections and success rate
                    const hostStats = metrics.host_stats || {};
                    let totalActiveConnections = 0;
                    Object.values(hostStats).forEach(stats => {
                        totalActiveConnections += stats.active_connections || 0;
                    });

                    const requestStats = metrics.request_stats || {};
                    const successRate = calculateSuccessRate(metrics);

                    document.getElementById('active-connections').textContent = totalActiveConnections;
                    document.getElementById('success-rate').textContent = successRate;

                } catch (error) {
                    console.error('Failed to load token statistics:', error);
                    document.getElementById('total-tokens').textContent = 'Error';
                    document.getElementById('prompt-tokens').textContent = 'Error';
                    document.getElementById('completion-tokens').textContent = 'Error';
                    document.getElementById('token-efficiency').textContent = 'Error';
                    document.getElementById('prompt-tokens-per-sec').textContent = 'Error';
                    document.getElementById('completion-tokens-per-sec').textContent = 'Error';
                    document.getElementById('active-connections').textContent = 'Error';
                    document.getElementById('success-rate').textContent = 'Error';
                }

                // Update health status with comprehensive display
                await updateSystemHealthDisplay(health);

                console.log('Overview updated successfully');
            } catch (error) {
                console.error('Failed to load overview:', error);

                // Set fallback values
                document.getElementById('total-hosts').textContent = 'Error';
                document.getElementById('enabled-hosts').textContent = 'Error';
                document.getElementById('total-users').textContent = 'Error';
                document.getElementById('admin-users').textContent = 'Error';
                document.getElementById('health-status').innerHTML = '<span class="status-badge status-disabled">❌ Connection Error</span>';

                showAlert('Failed to load overview: ' + error.message, 'error');
            }
        }

        async function loadHosts() {
            try {
                const hosts = await apiCall('/config/hosts');
                const container = document.getElementById('hosts-table-container');

                if (hosts.length === 0) {
                    container.innerHTML = '<p>No hosts configured. Click "Add Host" to get started.</p>';
                    return;
                }

                // Fetch host performance metrics
                let hostStats = {};
                try {
                    const metricsResponse = await fetch('/proxy/metrics');
                    if (metricsResponse.ok) {
                        const metrics = await metricsResponse.json();
                        hostStats = metrics.host_stats || {};
                    }
                } catch (error) {
                    console.warn('Failed to fetch host metrics:', error);
                }

                const hostsGrid = `
                    <div class="hosts-grid">
                        ${hosts.map(host => {
                            const hostMetrics = hostStats[host.host] || {};
                            const hasMetrics = hostMetrics.requests > 0;

                            return `
                            <div class="host-card">
                                <div class="host-card-header">
                                    <div class="host-card-title">
                                        <div class="host-card-id">ID: ${host.id}</div>
                                        <h3 class="host-card-name">${host.name || host.id}</h3>
                                        ${host.description ? `<div style="font-size: 0.85em; color: #666; margin-top: 4px;">${host.description}</div>` : ''}
                                    </div>
                                    <div class="host-card-actions">
                                        <button class="btn btn-small btn-primary" onclick="editHost('${host.id}')" title="Edit Host">✏️</button>
                                        <button class="btn btn-small btn-secondary" onclick="testHostConnection('${host.host}')" title="Test Connection">🔍</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteHost('${host.id}')" title="Delete Host">🗑️</button>
                                    </div>
                                </div>

                                <div class="host-card-body">
                                    <div class="host-info-row">
                                        <span class="host-info-label">Host URL:</span>
                                        <span class="host-info-value host-url">${host.host}</span>
                                    </div>

                                    ${hasMetrics ? `
                                    <div style="background: #f8f9fa; border-radius: 6px; padding: 12px; margin: 12px 0; border-left: 3px solid #28a745;">
                                        <div style="font-weight: 600; color: #333; margin-bottom: 8px; font-size: 0.9em;">📊 Performance Metrics</div>
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.85em;">
                                            <div>
                                                <span style="color: #666;">Requests:</span>
                                                <span style="font-weight: 500; color: #333;">${hostMetrics.requests}</span>
                                            </div>
                                            <div>
                                                <span style="color: #666;">Success Rate:</span>
                                                <span style="font-weight: 500; color: ${hostMetrics.error_rate < 0.05 ? '#28a745' : hostMetrics.error_rate < 0.2 ? '#ffc107' : '#dc3545'};">${((1 - hostMetrics.error_rate) * 100).toFixed(1)}%</span>
                                            </div>
                                            <div>
                                                <span style="color: #666;">Avg Response:</span>
                                                <span style="font-weight: 500; color: #333;">${formatDuration(hostMetrics.avg_response_time)}</span>
                                            </div>
                                            <div>
                                                <span style="color: #666;">Health:</span>
                                                <span style="font-weight: 500; color: ${hostMetrics.is_healthy ? '#28a745' : '#dc3545'};">${hostMetrics.is_healthy ? 'Healthy' : 'Unknown'}</span>
                                            </div>
                                        </div>
                                    </div>
                                    ` : `
                                    <div style="background: #f8f9fa; border-radius: 6px; padding: 10px; margin: 12px 0; border-left: 3px solid #dee2e6;">
                                        <div style="font-size: 0.85em; color: #6c757d; text-align: center;">
                                            📊 No performance data yet
                                        </div>
                                    </div>
                                    `}

                                    <div class="host-info-row">
                                        <span class="host-info-label">Status:</span>
                                        <span class="host-info-value">
                                            <span class="status-badge ${host.enabled ? 'status-enabled' : 'status-disabled'}">${host.enabled ? 'Enabled' : 'Disabled'}</span>
                                        </span>
                                    </div>

                                    <div class="host-info-row">
                                        <span class="host-info-label">Weight:</span>
                                        <span class="host-info-value">${host.weight}</span>
                                    </div>

                                    <div class="host-info-row">
                                        <span class="host-info-label">Max Connections:</span>
                                        <span class="host-info-value">${host.max_connections}</span>
                                    </div>

                                    <div class="host-info-row">
                                        <span class="host-info-label">Timeout:</span>
                                        <span class="host-info-value">${host.timeout}s</span>
                                    </div>
                                </div>

                                <div class="host-models-section">
                                    <div class="host-models-header">
                                        <span class="host-models-title">Models</span>
                                        <button class="btn btn-small btn-info" onclick="loadHostModels('${host.id}', '${host.host}')">Load Models</button>
                                    </div>
                                    <div id="models-${host.id}" class="models-info">
                                        <span style="color: #666; font-size: 0.9em;">Click "Load Models" to view available models</span>
                                    </div>
                                </div>
                            </div>
                            `;
                        }).join('')}
                    </div>
                `;

                container.innerHTML = hostsGrid;
            } catch (error) {
                console.error('Failed to load hosts:', error);
                showAlert('Failed to load hosts: ' + error.message, 'error');
            }
        }

        async function loadMetrics() {
            try {
                // Load both metrics and host configuration data
                const [metrics, hosts] = await Promise.all([
                    apiCall('/proxy/metrics'),
                    apiCall('/config/hosts')
                ]);
                const container = document.getElementById('metrics-container');

                const metricsHtml = `
                    <div class="metrics-grid">
                        <!-- System Overview Metrics -->
                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Total Requests</h4>
                                <span class="metric-card-icon">📊</span>
                            </div>
                            <div class="metric-value">${formatNumber((metrics.request_stats || {}).total_requests || 0)}</div>
                            <div class="metric-label">All-time requests processed</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Success Rate</h4>
                                <span class="metric-card-icon">✅</span>
                            </div>
                            <div class="metric-value">${calculateSuccessRate(metrics)}%</div>
                            <div class="metric-label">Request success percentage</div>
                            <div class="metric-bar">
                                <div class="metric-bar-fill" style="width: ${calculateSuccessRate(metrics)}%"></div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Average Response Time</h4>
                                <span class="metric-card-icon">⏱️</span>
                            </div>
                            <div class="metric-value">${formatDuration(getAverageResponseTime(metrics))}</div>
                            <div class="metric-label">Mean response time</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Active Connections</h4>
                                <span class="metric-card-icon">🔗</span>
                            </div>
                            <div class="metric-value">${(metrics.health_status || {}).active_connections || 0}</div>
                            <div class="metric-label">Current active connections</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Error Rate</h4>
                                <span class="metric-card-icon">❌</span>
                            </div>
                            <div class="metric-value">${calculateErrorRate(metrics)}%</div>
                            <div class="metric-label">Request error percentage</div>
                            <div class="metric-bar">
                                <div class="metric-bar-fill" style="width: ${calculateErrorRate(metrics)}%; background: #dc3545;"></div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">System Uptime</h4>
                                <span class="metric-card-icon">⏰</span>
                            </div>
                            <div class="metric-value">${formatUptime((metrics.health_status || {}).uptime || 0)}</div>
                            <div class="metric-label">System running time</div>
                        </div>
                    </div>

                    <!-- Token Usage Statistics -->
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Total Tokens</h4>
                                <span class="metric-card-icon">🎯</span>
                            </div>
                            <div class="metric-value">${formatNumber((metrics.token_stats || {}).total_tokens || 0)}</div>
                            <div class="metric-label">All-time tokens processed</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Prompt Tokens</h4>
                                <span class="metric-card-icon">📝</span>
                            </div>
                            <div class="metric-value">${formatNumber((metrics.token_stats || {}).total_prompt_tokens || 0)}</div>
                            <div class="metric-label">Input tokens processed</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Completion Tokens</h4>
                                <span class="metric-card-icon">💬</span>
                            </div>
                            <div class="metric-value">${formatNumber((metrics.token_stats || {}).total_completion_tokens || 0)}</div>
                            <div class="metric-label">Output tokens generated</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Output Ratio</h4>
                                <span class="metric-card-icon">📊</span>
                            </div>
                            <div class="metric-value">${calculateTokenEfficiency(metrics.token_stats || {})}%</div>
                            <div class="metric-label">Output/Input token ratio</div>
                            <div class="metric-bar">
                                <div class="metric-bar-fill" style="width: ${Math.min(calculateTokenEfficiency(metrics.token_stats || {}), 100)}%; background: #28a745;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Token Performance Statistics -->
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Prompt Processing Speed</h4>
                                <span class="metric-card-icon">🚀</span>
                            </div>
                            <div class="metric-value">${formatTokensPerSecond((metrics.token_stats || {}).global_prompt_tokens_per_sec || 0)}</div>
                            <div class="metric-label">Tokens per second (input)</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Completion Generation Speed</h4>
                                <span class="metric-card-icon">💨</span>
                            </div>
                            <div class="metric-value">${formatTokensPerSecond((metrics.token_stats || {}).global_completion_tokens_per_sec || 0)}</div>
                            <div class="metric-label">Tokens per second (output)</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Total Processing Time</h4>
                                <span class="metric-card-icon">⏱️</span>
                            </div>
                            <div class="metric-value">${formatDuration(((metrics.token_stats || {}).total_prompt_eval_duration || 0) / 1_000_000_000 + ((metrics.token_stats || {}).total_eval_duration || 0) / 1_000_000_000)}</div>
                            <div class="metric-label">Total time spent processing</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-card-header">
                                <h4 class="metric-card-title">Processing Efficiency</h4>
                                <span class="metric-card-icon">📊</span>
                            </div>
                            <div class="metric-value">${calculateProcessingEfficiency(metrics.token_stats || {})}%</div>
                            <div class="metric-label">Completion vs Prompt speed</div>
                            <div class="metric-bar">
                                <div class="metric-bar-fill" style="width: ${Math.min(calculateProcessingEfficiency(metrics.token_stats || {}), 100)}%; background: #17a2b8;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Request Breakdown Section -->
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <h4 class="metric-card-title">Request Breakdown</h4>
                            <span class="metric-card-icon">📋</span>
                        </div>
                        <div class="request-breakdown">
                            ${generateRequestBreakdown(metrics.request_stats || {}, metrics.health_check_count || 0, metrics.health_check_errors || 0)}
                        </div>
                    </div>

                    <!-- Host Performance Table -->
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <h4 class="metric-card-title">Host Performance</h4>
                            <span class="metric-card-icon">🖥️</span>
                        </div>
                        <table class="metrics-table">
                            <thead>
                                <tr>
                                    <th>Host</th>
                                    <th>Total<br><small>Requests</small></th>
                                    <th>Avg<br><small>Time</small></th>
                                    <th>Success<br><small>Rate</small></th>
                                    <th>Total<br><small>Tokens</small></th>
                                    <th>Prompt/<br><small>Completion</small></th>
                                    <th>Tokens/sec<br><small>(P|C)</small></th>
                                    <th>Active<br><small>Conn</small></th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${generateHostMetricsRows(metrics.host_stats || {}, metrics.recent_activity || [], hosts || [])}
                            </tbody>
                        </table>
                    </div>

                    <!-- Model Usage Table -->
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <h4 class="metric-card-title">Model Usage Statistics</h4>
                            <span class="metric-card-icon">🤖</span>
                        </div>
                        <table class="metrics-table model-tree-table">
                            <thead>
                                <tr>
                                    <th>Model / Host</th>
                                    <th>Total<br><small>Requests</small></th>
                                    <th>Avg<br><small>Time</small></th>
                                    <th>Success<br><small>Rate</small></th>
                                    <th>Total<br><small>Tokens</small></th>
                                    <th>Prompt/<br><small>Completion</small></th>
                                    <th>Tokens/sec<br><small>(P|C)</small></th>
                                    <th>Last<br><small>Used</small></th>
                                </tr>
                            </thead>
                            <tbody>
                                ${generateModelTreeRows(metrics.model_stats || {}, metrics.recent_activity || [], metrics.host_stats || {}, hosts || [])}
                            </tbody>
                        </table>
                    </div>

                    <!-- Recent Errors -->
                    ${metrics.recent_errors && metrics.recent_errors.length > 0 ? `
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <h4 class="metric-card-title">Recent Errors</h4>
                            <span class="metric-card-icon">🚨</span>
                        </div>
                        <table class="metrics-table">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Endpoint</th>
                                    <th>Error</th>
                                    <th>Host</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${generateErrorRows(metrics.recent_errors)}
                            </tbody>
                        </table>
                    </div>
                    ` : ''}

                    <div class="metric-timestamp">
                        Last updated: ${new Date().toLocaleString()}
                    </div>
                `;

                container.innerHTML = metricsHtml;
            } catch (error) {
                document.getElementById('metrics-container').innerHTML =
                    '<div class="error">Failed to load metrics: ' + error.message + '</div>';
            }
        }

        async function loadUsers() {
            try {
                const users = await apiCall('/config/users');
                const container = document.getElementById('users-table-container');

                if (users.length === 0) {
                    container.innerHTML = '<p>No users configured. Click "Add User" to get started.</p>';
                    return;
                }

                const table = `
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Role</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${users.map(user => `
                                <tr>
                                    <td><strong>${user.username}</strong></td>
                                    <td><span class="status-badge ${user.is_admin ? 'status-admin' : 'status-user'}">${user.is_admin ? 'Administrator' : 'Regular User'}</span></td>
                                    <td>
                                        <button class="btn btn-small btn-primary" onclick="editUser('${user.username}')">Edit</button>
                                        <button class="btn btn-small btn-danger" onclick="deleteUser('${user.username}')">Delete</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;

                container.innerHTML = table;
            } catch (error) {
                console.error('Failed to load users:', error);
                showAlert('Failed to load users: ' + error.message, 'error');
            }
        }

        async function loadBackups() {
            try {
                const backups = await apiCall('/config/backups');
                const container = document.getElementById('backups-table-container');

                if (backups.length === 0) {
                    container.innerHTML = '<p>No backups available.</p>';
                    return;
                }

                const table = `
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Filename</th>
                                <th>Size</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${backups.map(backup => `
                                <tr>
                                    <td><strong>${backup.filename}</strong></td>
                                    <td>${formatBytes(backup.size)}</td>
                                    <td>${formatDate(backup.created)}</td>
                                    <td>
                                        <button class="btn btn-small btn-success" onclick="restoreBackup('${backup.filename}')">Restore</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;

                container.innerHTML = table;
            } catch (error) {
                console.error('Failed to load backups:', error);
                showAlert('Failed to load backups: ' + error.message, 'error');
            }
        }

        async function loadSettings() {
            try {
                const settings = await apiCall('/config/settings');
                const container = document.getElementById('settings-container');

                const settingsHtml = `
                    <div class="settings-container">
                    <h3>🖥️ Server Configuration</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Server Host</label>
                            <input type="text" value="${settings.host}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Server Port</label>
                            <input type="number" value="${settings.port}" readonly>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Debug Mode</label>
                            <input type="text" value="${settings.debug ? 'Enabled' : 'Disabled'}" readonly>
                        </div>
                        <div class="form-group">
                            <label>CORS Allow Origins</label>
                            <input type="text" value="${settings.cors_allow_origins}" readonly>
                        </div>
                    </div>

                    <h3>🌐 Web Management Interface</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Management Interface Port</label>
                            <input type="text" value="${window.location.port || '80'}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Management Interface URL</label>
                            <input type="text" value="${window.location.origin}/admin" readonly>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Login Page URL</label>
                            <input type="text" value="${window.location.origin}/login" readonly>
                        </div>
                        <div class="form-group">
                            <label>API Documentation URL</label>
                            <input type="text" value="${window.location.origin}/docs" readonly>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Static Files Path</label>
                            <input type="text" value="/static" readonly>
                        </div>
                        <div class="form-group">
                            <label>Configuration Alias</label>
                            <input type="text" value="${window.location.origin}/config" readonly>
                        </div>
                    </div>

                    <h3>⚖️ Load Balancing</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Load Balancer Strategy</label>
                            <input type="text" value="${settings.load_balancer_strategy}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Max Concurrent Requests</label>
                            <input type="number" value="${settings.max_concurrent_requests}" readonly>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Request Timeout (seconds)</label>
                            <input type="number" value="${settings.request_timeout}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Hosts Config Path</label>
                            <input type="text" value="${settings.hosts_config_path || 'hosts.json'}" readonly>
                        </div>
                    </div>

                    <h3>🔐 Authentication & Security</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Authentication</label>
                            <input type="text" value="${settings.auth_enabled ? 'Enabled' : 'Disabled'}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Auth Config Path</label>
                            <input type="text" value="${settings.auth_config_path || 'authorized_users.txt'}" readonly>
                        </div>
                    </div>

                    <h3>🏥 Health Monitoring</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Health Check Interval (seconds)</label>
                            <input type="number" value="${settings.health_check_interval}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Health Check Timeout (seconds)</label>
                            <input type="number" value="${settings.health_check_timeout}" readonly>
                        </div>
                    </div>

                    <h3>📊 Logging & Metrics</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Log Level</label>
                            <input type="text" value="${settings.log_level}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Log Path</label>
                            <input type="text" value="${settings.log_path || 'Console Only'}" readonly>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Metrics Collection</label>
                            <input type="text" value="${settings.metrics_enabled ? 'Enabled' : 'Disabled'}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Metrics Storage</label>
                            <input type="text" value="data/metrics.json" readonly>
                        </div>
                    </div>

                    <h3>🐳 Docker Configuration</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Container Internal Port</label>
                            <input type="text" value="${settings.port}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Host External Port</label>
                            <input type="text" value="${window.location.port || '80'}" readonly>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Port Mapping</label>
                            <input type="text" value="${window.location.port || '80'}:${settings.port}" readonly>
                        </div>
                        <div class="form-group">
                            <label>Container Name</label>
                            <input type="text" value="llm-proxy-server" readonly>
                        </div>
                    </div>

                    <div class="settings-note">
                        <p><strong>📝 Configuration Notes:</strong></p>
                        <ul>
                            <li>System settings are read-only and configured via environment variables or configuration files</li>
                            <li>Changes require server restart to take effect</li>
                            <li>Configuration files: <code>.env</code>, <code>hosts.json</code>, <code>authorized_users.txt</code></li>
                            <li>Environment variables override default values</li>
                            <li>Web interface runs on the same port as the API server</li>
                            <li>Docker maps external port ${window.location.port || '80'} to internal port ${settings.port}</li>
                            <li>Access URLs: <code>/admin</code>, <code>/config</code>, <code>/login</code>, <code>/docs</code></li>
                        </ul>
                    </div>
                    </div>
                `;

                container.innerHTML = settingsHtml;
            } catch (error) {
                console.error('Failed to load settings:', error);
                showAlert('Failed to load settings: ' + error.message, 'error');
            }
        }

        // Host Management Functions
        function showAddHostModal() {
            document.getElementById('hostModalTitle').textContent = 'Add Host';
            document.getElementById('hostForm').reset();
            document.getElementById('hostId').readOnly = false;
            currentEditingHost = null;
            showModal('hostModal');
        }

        async function editHost(hostId) {
            try {
                const hosts = await apiCall('/config/hosts');
                const host = hosts.find(h => h.id === hostId);

                if (!host) {
                    showAlert('Host not found', 'error');
                    return;
                }

                // Populate form
                document.getElementById('hostId').value = host.id;
                document.getElementById('hostName').value = host.name || '';
                document.getElementById('hostDescription').value = host.description || '';
                document.getElementById('hostUrl').value = host.host;
                document.getElementById('hostWeight').value = host.weight;
                document.getElementById('hostMaxConnections').value = host.max_connections;
                document.getElementById('hostTimeout').value = host.timeout;
                document.getElementById('hostEnabled').value = host.enabled.toString();

                // Disable ID field for editing
                document.getElementById('hostId').readOnly = true;

                document.getElementById('hostModalTitle').textContent = 'Edit Host';
                currentEditingHost = hostId;
                showModal('hostModal');
            } catch (error) {
                showAlert('Failed to load host data: ' + error.message, 'error');
            }
        }

        async function deleteHost(hostId) {
            if (!confirm(`Are you sure you want to delete host "${hostId}"?`)) {
                return;
            }

            try {
                await apiCall(`/config/hosts/${hostId}`, 'DELETE');
                showAlert('Host deleted successfully');
                loadHosts();
                loadOverview();
            } catch (error) {
                showAlert('Failed to delete host: ' + error.message, 'error');
            }
        }

        async function testHostConnection(hostUrl) {
            try {
                showAlert('Testing connection...', 'warning');
                const result = await apiCall('/config/hosts/test', 'POST', { host_url: hostUrl });

                if (result.status === 'success') {
                    showAlert(`Connection successful! Models: ${result.models_count}, Response time: ${result.response_time}s`);
                } else {
                    showAlert(`Connection failed: ${result.message}`, 'error');
                }
            } catch (error) {
                showAlert('Connection test failed: ' + error.message, 'error');
            }
        }

        // Helper function to fetch metrics data
        async function fetchMetricsData() {
            try {
                const response = await fetch('/proxy/metrics');
                if (response.ok) {
                    return await response.json();
                }
            } catch (error) {
                console.warn('Failed to fetch metrics:', error);
            }
            return { host_stats: {}, model_stats: {} };
        }

        // Get model-specific metrics for a host from recent activity data
        function getModelMetricsForHost(modelName, hostUrl, modelStats, hostStats, recentActivity) {
            const modelData = modelStats[modelName] || {};

            // Check if this model has been used on this host
            const hostUsage = modelData.host_usage || {};
            const requestsOnHost = hostUsage[hostUrl] || 0;

            if (requestsOnHost === 0) {
                return { hasMetrics: false };
            }

            // Try to calculate model-specific performance for this host from recent activity
            const modelHostActivity = (recentActivity || [])
                .filter(activity => activity.model === modelName && activity.host === hostUrl)
                .filter(activity => activity.prompt_eval_count && activity.eval_count); // Only activities with token data

            if (modelHostActivity.length > 0) {
                // Use recent activity data for precise model-host performance
                let totalPromptTokens = 0;
                let totalCompletionTokens = 0;
                let totalPromptDuration = 0;
                let totalEvalDuration = 0;
                let totalResponseTime = 0;

                modelHostActivity.forEach(activity => {
                    totalPromptTokens += activity.prompt_eval_count || 0;
                    totalCompletionTokens += activity.eval_count || 0;
                    totalPromptDuration += activity.prompt_eval_duration || 0;
                    totalEvalDuration += activity.eval_duration || 0;
                    totalResponseTime += activity.duration || 0;
                });

                // Calculate performance metrics
                const promptTokensPerSec = totalPromptDuration > 0 ?
                    totalPromptTokens / (totalPromptDuration / 1_000_000_000) : 0;
                const completionTokensPerSec = totalEvalDuration > 0 ?
                    totalCompletionTokens / (totalEvalDuration / 1_000_000_000) : 0;
                const avgResponseTime = modelHostActivity.length > 0 ?
                    totalResponseTime / modelHostActivity.length : 0;

                return {
                    hasMetrics: true,
                    requests: requestsOnHost,
                    promptTokensPerSec: promptTokensPerSec,
                    completionTokensPerSec: completionTokensPerSec,
                    totalTokens: totalPromptTokens + totalCompletionTokens,
                    avgResponseTime: avgResponseTime
                };
            } else {
                // Fallback: use model-level stats as approximation
                const modelTokenStats = modelData.token_stats || {};
                const hostData = hostStats[hostUrl] || {};

                return {
                    hasMetrics: true,
                    requests: requestsOnHost,
                    promptTokensPerSec: modelTokenStats.prompt_tokens_per_sec || 0,
                    completionTokensPerSec: modelTokenStats.completion_tokens_per_sec || 0,
                    totalTokens: modelTokenStats.total_tokens || 0,
                    avgResponseTime: hostData.avg_response_time || 0
                };
            }
        }

        // Generate performance section for model card
        function generateModelPerformanceSection(metrics) {
            if (!metrics.hasMetrics) {
                return `
                    <div style="background: #f8f9fa; border-radius: 6px; padding: 8px; margin: 8px 0; border-left: 3px solid #dee2e6;">
                        <div style="font-size: 0.8em; color: #666; text-align: center;">
                            📊 No performance data yet
                        </div>
                    </div>
                `;
            }

            return `
                <div style="background: #f0f8ff; border-radius: 6px; padding: 6px; margin: 6px 0; border-left: 3px solid #007bff; font-size: 0.75em;">
                    <div style="font-weight: 600; color: #333; margin-bottom: 4px; font-size: 0.9em;">📊 Performance</div>
                    <div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center;">
                        <span style="color: #666;">Requests: <strong style="color: #333;">${metrics.requests}</strong></span>
                        <span style="color: #666;">Tokens: <strong style="color: #333;">${formatNumber(metrics.totalTokens)}</strong></span>
                        <span style="color: #666;">Speed: <strong style="color: #007bff;">${formatTokensPerSecondCompact(metrics.promptTokensPerSec, metrics.completionTokensPerSec)}</strong></span>
                    </div>
                </div>
            `;
        }

        // Load models for a host
        async function loadHostModels(hostId, hostUrl) {
            const modelsContainer = document.getElementById(`models-${hostId}`);

            try {
                modelsContainer.innerHTML = '<div class="loading"></div> Loading...';

                // Fetch both models and metrics data
                const [modelsResult, metricsData] = await Promise.all([
                    apiCall('/config/hosts/models', 'POST', { host_url: hostUrl }),
                    fetchMetricsData()
                ]);

                const result = modelsResult;

                if (result.status === 'success') {
                    const models = result.models || [];
                    const totalSize = formatBytes(result.total_size || 0);

                    if (models.length === 0) {
                        modelsContainer.innerHTML = `
                            <div class="models-summary">
                                <strong>0 models</strong> (0 B)
                                <button class="btn btn-small btn-success" onclick="showInstallModelModal('${hostId}', '${hostUrl}')" style="margin-left: 5px;">
                                    + Install First Model
                                </button>
                            </div>
                            <div class="models-empty">
                                <div class="models-empty-icon">📦</div>
                                <p>No models installed on this host</p>
                                <p style="font-size: 0.9em; color: #888;">Click "Install First Model" to get started</p>
                            </div>
                        `;
                        return;
                    }

                    let modelsHtml = `
                        <div class="models-summary">
                            <strong>${models.length} models</strong> (${totalSize})
                            <button class="btn btn-small btn-info" onclick="toggleModelsList('${hostId}')" style="margin-left: 5px;">
                                <span id="toggle-${hostId}">Show</span>
                            </button>
                            <button class="btn btn-small btn-success" onclick="showInstallModelModal('${hostId}', '${hostUrl}')" style="margin-left: 5px;">
                                + Install
                            </button>
                        </div>
                        <div id="models-list-${hostId}" class="models-list" style="display: none;">
                            <div class="models-header">
                                <input type="text" class="models-search" placeholder="Search models..."
                                       onkeyup="filterModels('${hostId}', this.value)">
                                <select class="models-sort" onchange="sortModels('${hostId}', this.value)">
                                    <option value="name">Sort by Name</option>
                                    <option value="size">Sort by Size</option>
                                    <option value="date">Sort by Date</option>
                                    <option value="type">Sort by Type</option>
                                </select>
                            </div>
                            <div id="models-grid-${hostId}" class="models-grid">
                    `;

                    // Model loading with performance metrics
                    const hostStats = metricsData.host_stats || {};
                    const modelStats = metricsData.model_stats || {};
                    const recentActivity = metricsData.recent_activity || [];
                    const currentHostStats = hostStats[hostUrl] || {};

                    models.forEach(model => {
                        const modelType = getModelType(model.name);
                        const formattedDate = formatDate(model.modified_at);

                        // Get model-specific metrics for this host
                        let modelMetrics;
                        try {
                            modelMetrics = getModelMetricsForHost(model.name, hostUrl, modelStats, hostStats, recentActivity);
                        } catch (error) {
                            console.error('Error getting model metrics:', error);
                            modelMetrics = { hasMetrics: false };
                        }

                        modelsHtml += `
                            <div class="model-card" data-name="${model.name.toLowerCase()}" data-size="${model.size}" data-date="${model.modified_at}" data-type="${modelType}">
                                <div class="model-card-header">
                                    <h4 class="model-name">${model.name}</h4>
                                    <div class="model-actions">
                                        <button class="btn btn-small btn-danger" onclick="removeModel('${hostId}', '${hostUrl}', '${model.name}')" title="Remove model">
                                            🗑️
                                        </button>
                                    </div>
                                </div>
                                <div class="model-details">
                                    <div class="model-detail-row">
                                        <span>Size:</span>
                                        <span class="model-size">${formatBytes(model.size)}</span>
                                    </div>
                                    <div class="model-detail-row">
                                        <span>Parameters:</span>
                                        <span>${model.parameter_size}</span>
                                    </div>
                                    <div class="model-detail-row">
                                        <span>Quantization:</span>
                                        <span>${model.quantization}</span>
                                    </div>
                                    ${generateModelPerformanceSection(modelMetrics)}
                                    <div style="margin-top: 8px;">
                                        <span class="model-tag">${modelType}</span>
                                        <div class="model-date">Modified: ${formattedDate}</div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    modelsHtml += '</div></div>';
                    modelsContainer.innerHTML = modelsHtml;
                } else {
                    modelsContainer.innerHTML = `<span class="model-details" style="color: #d32f2f;">❌ ${result.message}</span>`;
                }
            } catch (error) {
                modelsContainer.innerHTML = `<span class="model-details" style="color: #d32f2f;">❌ Error: ${error.message}</span>`;
            }
        }

        // Toggle models list visibility
        function toggleModelsList(hostId) {
            const modelsList = document.getElementById(`models-list-${hostId}`);
            const toggleButton = document.getElementById(`toggle-${hostId}`);

            if (modelsList.style.display === 'none') {
                modelsList.style.display = 'block';
                toggleButton.textContent = 'Hide';
            } else {
                modelsList.style.display = 'none';
                toggleButton.textContent = 'Show';
            }
        }

        // Format bytes to human readable format
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // Format date to readable format
        function formatDate(dateString) {
            if (!dateString) return 'Unknown';
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }

        // Get model type based on name
        function getModelType(modelName) {
            const name = modelName.toLowerCase();
            if (name.includes('code') || name.includes('coder')) return 'Code';
            if (name.includes('embed') || name.includes('embedding')) return 'Embedding';
            if (name.includes('vision') || name.includes('llava')) return 'Vision';
            if (name.includes('instruct') || name.includes('chat')) return 'Chat';
            if (name.includes('llama')) return 'Llama';
            if (name.includes('mistral')) return 'Mistral';
            if (name.includes('gemma')) return 'Gemma';
            if (name.includes('phi')) return 'Phi';
            if (name.includes('qwen')) return 'Qwen';
            return 'General';
        }

        // Metrics helper functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        function formatDuration(seconds) {
            if (!seconds || seconds === 0) return '0ms';

            if (seconds < 0.001) {
                // For very small numbers, show with more precision
                return (seconds * 1000).toFixed(2) + 'ms';
            } else if (seconds < 1) {
                return (seconds * 1000).toFixed(1) + 'ms';
            } else if (seconds < 60) {
                return seconds.toFixed(2) + 's';
            } else {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = (seconds % 60).toFixed(0);
                return `${minutes}m ${remainingSeconds}s`;
            }
        }

        function formatUptime(seconds) {
            if (!seconds || seconds === 0) return '0s';

            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const remainingSeconds = Math.floor(seconds % 60);

            if (days > 0) {
                return `${days}d ${hours}h ${minutes}m`;
            } else if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else if (minutes > 0) {
                return `${minutes}m ${remainingSeconds}s`;
            } else {
                return `${remainingSeconds}s`;
            }
        }

        // Token statistics helper functions
        function calculateTokenEfficiency(tokenStats) {
            const promptTokens = tokenStats.total_prompt_tokens || 0;
            const completionTokens = tokenStats.total_completion_tokens || 0;

            if (promptTokens === 0) return 0;
            return ((completionTokens / promptTokens) * 100).toFixed(1);
        }

        function formatTokenRatio(promptTokens, completionTokens) {
            if (promptTokens === 0 && completionTokens === 0) return '-';
            return `${formatNumber(promptTokens)}/${formatNumber(completionTokens)}`;
        }

        // Token performance helper functions
        function formatTokensPerSecond(tokensPerSec) {
            if (!tokensPerSec || tokensPerSec === 0) return '-';
            if (tokensPerSec < 1) return tokensPerSec.toFixed(2);
            if (tokensPerSec < 10) return tokensPerSec.toFixed(1);
            return Math.round(tokensPerSec).toString();
        }

        function formatDuration(seconds) {
            if (!seconds || seconds === 0) return '-';
            if (seconds < 1) return `${(seconds * 1000).toFixed(0)}ms`;
            if (seconds < 60) return `${seconds.toFixed(1)}s`;
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = (seconds % 60).toFixed(0);
            return `${minutes}m ${remainingSeconds}s`;
        }

        function calculateProcessingEfficiency(tokenStats) {
            const promptSpeed = tokenStats.global_prompt_tokens_per_sec || 0;
            const completionSpeed = tokenStats.global_completion_tokens_per_sec || 0;

            if (promptSpeed === 0) return 0;
            return ((completionSpeed / promptSpeed) * 100).toFixed(1);
        }

        function formatTokensPerSecondRatio(promptSpeed, completionSpeed) {
            if (promptSpeed === 0 && completionSpeed === 0) return '-';
            return `${formatTokensPerSecond(promptSpeed)}/${formatTokensPerSecond(completionSpeed)}`;
        }

        function formatTokensPerSecondCompact(promptSpeed, completionSpeed) {
            if (promptSpeed === 0 && completionSpeed === 0) return '-';
            const prompt = formatTokensPerSecond(promptSpeed);
            const completion = formatTokensPerSecond(completionSpeed);
            return `${prompt} | ${completion}`;
        }

        function getTokenStatsForHost(hostStats) {
            const tokenStats = hostStats.token_stats || {};
            return {
                total: tokenStats.total_tokens || 0,
                prompt: tokenStats.prompt_tokens || 0,
                completion: tokenStats.completion_tokens || 0
            };
        }

        function getTokenStatsForModel(modelStats) {
            const tokenStats = modelStats.token_stats || {};
            return {
                total: tokenStats.total_tokens || 0,
                prompt: tokenStats.prompt_tokens || 0,
                completion: tokenStats.completion_tokens || 0
            };
        }

        async function updateSystemHealthDisplay(configHealth) {
            const healthStatus = document.getElementById('health-status');

            try {
                // Get comprehensive health data from both metrics and status
                // Add cache-busting to ensure fresh data
                const timestamp = Date.now();
                const [metricsResponse, statusResponse] = await Promise.all([
                    fetch(`/proxy/metrics?_t=${timestamp}`),
                    fetch(`/proxy/status?_t=${timestamp}`)
                ]);

                const metrics = await metricsResponse.json();
                const status = await statusResponse.json();

                const healthData = metrics.health_status || {};
                const hostStats = metrics.host_stats || {};
                const requestStats = metrics.request_stats || {};

                // Use actual host data from status endpoint
                const actualHosts = status.hosts || [];

                // Determine overall system status
                const overallStatus = determineOverallStatus(configHealth, healthData, hostStats, actualHosts);

                healthStatus.innerHTML = `
                    <div class="health-grid">
                        ${generateHealthIndicator('🎯', 'Overall Status', overallStatus.status, overallStatus.description, overallStatus.status)}
                        ${generateHealthIndicator('⏱️', 'System Uptime', formatUptime(healthData.uptime || 0), 'Time since last restart', 'healthy')}
                        ${generateHealthIndicator('📊', 'Request Health', getRequestHealthStatus(requestStats), getRequestHealthDescription(requestStats), getRequestHealthLevel(requestStats))}
                        ${generateHealthIndicator('🖥️', 'Backend Hosts', getActualHostHealthStatus(actualHosts), getActualHostHealthDescription(actualHosts), getActualHostHealthLevel(actualHosts))}
                        ${generateHealthIndicator('🔄', 'Load Balancer', 'Active', 'Distributing requests', 'healthy')}
                        ${generateHealthIndicator('💾', 'Memory Usage', 'Normal', 'Within acceptable limits', 'healthy')}
                    </div>

                    <div class="health-details">
                        <h4 style="margin-bottom: 16px; color: #333;">📋 System Details</h4>
                        ${generateHealthDetailRow('Total Requests', formatNumber((requestStats.total_requests || 0) + (metrics.health_check_count || 0)), 'All API requests processed since server startup, including chat, generate, and health checks')}
                        ${generateHealthDetailRow('Error Rate', `${((requestStats.error_rate || 0) * 100).toFixed(1)}%`, 'Percentage of requests that resulted in HTTP 4xx/5xx errors or timeouts')}
                        ${(() => {
                            const avgTimes = requestStats.avg_response_times || {};
                            const times = Object.values(avgTimes);
                            let avgSeconds = 0;
                            if (times.length > 0) {
                                avgSeconds = times.reduce((sum, time) => sum + time, 0) / times.length;
                            }

                            // Format duration inline
                            let formatted = '0ms';
                            if (avgSeconds > 0) {
                                if (avgSeconds < 0.001) {
                                    formatted = (avgSeconds * 1000).toFixed(2) + 'ms';
                                } else if (avgSeconds < 1) {
                                    formatted = (avgSeconds * 1000).toFixed(1) + 'ms';
                                } else if (avgSeconds < 60) {
                                    formatted = avgSeconds.toFixed(2) + 's';
                                } else {
                                    const minutes = Math.floor(avgSeconds / 60);
                                    const remainingSeconds = (avgSeconds % 60).toFixed(0);
                                    formatted = `${minutes}m ${remainingSeconds}s`;
                                }
                            }

                            return generateHealthDetailRow('Average Response Time', formatted, 'Mean time from request start to completion across all endpoints and models');
                        })()}
                        ${generateHealthDetailRow('Active Endpoints', Object.keys(requestStats.requests_by_endpoint || {}).length, 'Number of API endpoints that have received at least one request')}
                        ${generateHealthDetailRow('Status Codes', Object.keys(requestStats.status_codes || {}).join(', '), 'HTTP response codes returned by the server (200=success, 500=server error, etc.)')}
                        ${generateHealthDetailRow('Backend Hosts', `${status.total_hosts || 0} configured, ${status.active_hosts || 0} active`, 'Ollama servers configured in hosts.json and currently responding to health checks')}
                        ${generateHealthDetailRow('Metrics Storage', 'Persistent (auto-saved every 10 requests)', 'Performance data is automatically saved to disk and survives server restarts')}
                        ${generateHealthDetailRow('Last Updated', new Date().toLocaleString(), 'When this dashboard data was last refreshed from the server')}
                    </div>
                `;

            } catch (error) {
                console.error('Failed to load comprehensive health data:', error);
                // Fallback to simple display
                if (configHealth.status === 'healthy') {
                    healthStatus.innerHTML = '<span class="status-badge status-enabled">✅ System Healthy</span>';
                } else {
                    healthStatus.innerHTML = `<span class="status-badge status-disabled">❌ System Unhealthy: ${configHealth.error || 'Unknown error'}</span>`;
                }
            }
        }

        function generateHealthIndicator(icon, title, value, description, level) {
            return `
                <div class="health-indicator ${level}">
                    <div class="health-icon">${icon}</div>
                    <div class="health-title">${title}</div>
                    <div class="health-value">${value}</div>
                    <div class="health-description">${description}</div>
                </div>
            `;
        }

        function generateHealthDetailRow(label, value, description = '') {
            return `
                <div class="health-detail-row">
                    <div class="health-detail-main">
                        <span class="health-detail-label">${label}:</span>
                        <span class="health-detail-value">${value}</span>
                    </div>
                    ${description ? `<div class="health-detail-description">${description}</div>` : ''}
                </div>
            `;
        }

        function determineOverallStatus(configHealth, healthData, hostStats, actualHosts) {
            const errorRate = healthData.error_rate || 0;

            // Use actual host data from status endpoint
            const hostCount = actualHosts.length;
            const healthyHosts = actualHosts.filter(host => host.healthy && host.enabled).length;

            if (configHealth.status !== 'healthy') {
                return { status: 'unhealthy', description: configHealth.error || 'Configuration issues detected' };
            }

            if (errorRate > 0.1) {
                return { status: 'unhealthy', description: `High error rate: ${(errorRate * 100).toFixed(1)}%` };
            }

            if (hostCount > 0 && healthyHosts === 0) {
                return { status: 'unhealthy', description: 'No healthy backend hosts' };
            }

            if (errorRate > 0.05 || (hostCount > 0 && healthyHosts < hostCount * 0.5)) {
                return { status: 'degraded', description: 'Some issues detected' };
            }

            return { status: 'healthy', description: 'All systems operational' };
        }

        function getRequestHealthStatus(requestStats) {
            const total = requestStats.total_requests || 0;
            const errors = requestStats.total_errors || 0;
            const errorRate = total > 0 ? errors / total : 0;

            if (total === 0) return 'No Data';
            if (errorRate > 0.1) return 'Poor';
            if (errorRate > 0.05) return 'Fair';
            return 'Excellent';
        }

        function getRequestHealthDescription(requestStats) {
            const total = requestStats.total_requests || 0;
            const errors = requestStats.total_errors || 0;

            if (total === 0) return 'No requests processed yet';
            return `${total} requests, ${errors} errors`;
        }

        function getRequestHealthLevel(requestStats) {
            const total = requestStats.total_requests || 0;
            const errorRate = total > 0 ? (requestStats.total_errors || 0) / total : 0;

            if (total === 0) return 'healthy';
            if (errorRate > 0.1) return 'unhealthy';
            if (errorRate > 0.05) return 'degraded';
            return 'healthy';
        }

        function getHostHealthStatus(hostStats) {
            const hosts = Object.values(hostStats);
            if (hosts.length === 0) return 'No Hosts';

            const healthyHosts = hosts.filter(host => host.is_healthy).length;
            return `${healthyHosts}/${hosts.length} Healthy`;
        }

        function getHostHealthDescription(hostStats) {
            const hosts = Object.values(hostStats);
            if (hosts.length === 0) return 'No backend hosts configured';

            const healthyHosts = hosts.filter(host => host.is_healthy).length;
            if (healthyHosts === hosts.length) return 'All hosts operational';
            if (healthyHosts === 0) return 'All hosts down';
            return `${hosts.length - healthyHosts} hosts have issues`;
        }

        function getHostHealthLevel(hostStats) {
            const hosts = Object.values(hostStats);
            if (hosts.length === 0) return 'healthy';

            const healthyHosts = hosts.filter(host => host.is_healthy).length;
            if (healthyHosts === 0) return 'unhealthy';
            if (healthyHosts < hosts.length * 0.5) return 'degraded';
            return 'healthy';
        }

        // New functions for actual host data from status endpoint
        function getActualHostHealthStatus(actualHosts) {
            if (actualHosts.length === 0) return 'No Hosts';

            const healthyHosts = actualHosts.filter(host => host.healthy && host.enabled).length;
            return `${healthyHosts}/${actualHosts.length} Healthy`;
        }

        function getActualHostHealthDescription(actualHosts) {
            if (actualHosts.length === 0) return 'No backend hosts configured';

            const healthyHosts = actualHosts.filter(host => host.healthy && host.enabled).length;
            const enabledHosts = actualHosts.filter(host => host.enabled).length;

            if (healthyHosts === enabledHosts && enabledHosts === actualHosts.length) {
                return 'All hosts operational';
            }
            if (healthyHosts === 0) {
                return 'All enabled hosts down';
            }

            const disabledHosts = actualHosts.length - enabledHosts;
            const unhealthyHosts = enabledHosts - healthyHosts;

            let description = '';
            if (unhealthyHosts > 0) {
                description += `${unhealthyHosts} unhealthy`;
            }
            if (disabledHosts > 0) {
                if (description) description += ', ';
                description += `${disabledHosts} disabled`;
            }

            return description || 'All hosts operational';
        }

        function getActualHostHealthLevel(actualHosts) {
            if (actualHosts.length === 0) return 'degraded'; // No hosts is a problem

            const enabledHosts = actualHosts.filter(host => host.enabled);
            const healthyHosts = actualHosts.filter(host => host.healthy && host.enabled);

            if (enabledHosts.length === 0) return 'unhealthy'; // No enabled hosts
            if (healthyHosts.length === 0) return 'unhealthy'; // No healthy hosts
            if (healthyHosts.length < enabledHosts.length * 0.5) return 'degraded'; // Less than 50% healthy

            return 'healthy';
        }

        function getAverageResponseTime(requestStats) {
            const avgTimes = requestStats.avg_response_times || {};
            const times = Object.values(avgTimes);
            if (times.length === 0) return 0;

            const totalTime = times.reduce((sum, time) => sum + time, 0);
            return totalTime / times.length;
        }

        async function refreshSystemHealth() {
            const healthStatus = document.getElementById('health-status');
            healthStatus.innerHTML = '<div class="loading"></div> Refreshing health status...';

            try {
                // Refresh the overview which will update health
                await loadOverview();
            } catch (error) {
                console.error('Failed to refresh health:', error);
                healthStatus.innerHTML = '<span class="status-badge status-disabled">❌ Failed to refresh health status</span>';
            }
        }

        async function saveMetrics() {
            try {
                const response = await fetch('/proxy/metrics/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showAlert('✅ Metrics saved to disk successfully!', 'success');
                    console.log('Metrics saved:', result);
                } else {
                    const error = await response.text();
                    showAlert('❌ Failed to save metrics: ' + error, 'error');
                }
            } catch (error) {
                console.error('Save metrics failed:', error);
                showAlert('❌ Failed to save metrics: ' + error.message, 'error');
            }
        }

        async function resetMetrics() {
            // Confirm before resetting
            if (!confirm('⚠️ Are you sure you want to reset all metrics data? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch('/proxy/metrics/reset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showAlert('✅ Metrics reset successfully!', 'success');
                    console.log('Metrics reset:', result);

                    // Refresh the metrics display
                    await refreshMetrics();
                } else {
                    const error = await response.text();
                    showAlert('❌ Failed to reset metrics: ' + error, 'error');
                }
            } catch (error) {
                console.error('Reset metrics failed:', error);
                showAlert('❌ Failed to reset metrics: ' + error.message, 'error');
            }
        }

        function calculateSuccessRate(metrics) {
            const requestStats = metrics.request_stats || {};
            const total = requestStats.total_requests || 0;
            const errors = requestStats.total_errors || 0;
            if (total === 0) return 100;
            return ((total - errors) / total * 100).toFixed(1);
        }

        function calculateErrorRate(metrics) {
            const requestStats = metrics.request_stats || {};
            const total = requestStats.total_requests || 0;
            const errors = requestStats.total_errors || 0;
            if (total === 0) return 0;
            return (errors / total * 100).toFixed(1);
        }

        function getAverageResponseTime(metrics) {
            const requestStats = metrics.request_stats || {};
            const avgTimes = requestStats.avg_response_times || {};

            // Calculate overall average from all endpoints
            const times = Object.values(avgTimes);
            if (times.length === 0) return 0;

            const totalTime = times.reduce((sum, time) => sum + time, 0);
            return totalTime / times.length;
        }

        function generateRequestBreakdown(requestStats, healthCheckCount = 0, healthCheckErrors = 0) {
            const requestsByEndpoint = requestStats.requests_by_endpoint || {};
            const avgResponseTimes = requestStats.avg_response_times || {};
            const totalRequests = requestStats.total_requests || 0;

            // Total including health checks for percentage calculations
            const totalWithHealthChecks = totalRequests + healthCheckCount;

            if (totalWithHealthChecks === 0) {
                return '<div class="no-data">No request data available</div>';
            }

            // Categorize requests
            const categories = {
                'AI Requests': {
                    icon: '🤖',
                    color: '#28a745',
                    endpoints: ['POST /api/generate', 'POST /api/chat', 'POST /api/embeddings'],
                    description: 'Your actual AI model requests'
                },
                'Health Monitoring': {
                    icon: '🏥',
                    color: '#17a2b8',
                    endpoints: ['GET /proxy/health'],
                    description: 'Docker health checks (every 30s)'
                },
                'System Monitoring': {
                    icon: '📊',
                    color: '#ffc107',
                    endpoints: ['GET /proxy/metrics', 'GET /proxy/status', 'GET /api/version'],
                    description: 'Web interface and system monitoring'
                },
                'API Discovery': {
                    icon: '🔍',
                    color: '#6f42c1',
                    endpoints: ['GET /api/tags', 'GET /api/models', 'GET /'],
                    description: 'Model listing and API discovery'
                }
            };

            let html = '<div class="request-categories">';

            for (const [categoryName, category] of Object.entries(categories)) {
                let categoryRequests;
                let percentage;

                // Handle Health Monitoring separately using dedicated counters
                if (categoryName === 'Health Monitoring') {
                    categoryRequests = healthCheckCount;
                    percentage = totalWithHealthChecks > 0 ? ((categoryRequests / totalWithHealthChecks) * 100).toFixed(1) : 0;
                } else {
                    categoryRequests = category.endpoints.reduce((sum, endpoint) => {
                        return sum + (requestsByEndpoint[endpoint] || 0);
                    }, 0);
                    percentage = totalWithHealthChecks > 0 ? ((categoryRequests / totalWithHealthChecks) * 100).toFixed(1) : 0;
                }

                if (categoryRequests > 0) {
                    html += `
                        <div class="request-category">
                            <div class="category-header">
                                <span class="category-icon">${category.icon}</span>
                                <div class="category-info">
                                    <h5 class="category-name">${categoryName}</h5>
                                    <p class="category-description">${category.description}</p>
                                </div>
                                <div class="category-stats">
                                    <div class="category-count">${formatNumber(categoryRequests)}</div>
                                    <div class="category-percentage">${percentage}%</div>
                                </div>
                            </div>
                            <div class="category-endpoints">
                                ${categoryName === 'Health Monitoring' ?
                                    // Special handling for health monitoring
                                    (healthCheckCount > 0 ? `
                                        <div class="endpoint-row">
                                            <span class="endpoint-name">GET /proxy/health</span>
                                            <span class="endpoint-count">${formatNumber(healthCheckCount)}</span>
                                            <span class="endpoint-time">~0.03ms</span>
                                        </div>
                                        ${healthCheckErrors > 0 ? `
                                        <div class="endpoint-row" style="color: #dc3545; font-size: 0.85em; padding-left: 20px;">
                                            <span class="endpoint-name">└─ Errors: ${formatNumber(healthCheckErrors)}</span>
                                            <span class="endpoint-count"></span>
                                            <span class="endpoint-time">-</span>
                                        </div>
                                        ` : ''}
                                    ` : '') :
                                    // Normal endpoint handling
                                    category.endpoints.map(endpoint => {
                                        const count = requestsByEndpoint[endpoint] || 0;
                                        const avgTime = avgResponseTimes[endpoint] || 0;
                                        if (count === 0) return '';
                                        return `
                                            <div class="endpoint-row">
                                                <span class="endpoint-name">${endpoint}</span>
                                                <span class="endpoint-count">${formatNumber(count)}</span>
                                                <span class="endpoint-time">${formatDuration(avgTime)}</span>
                                            </div>
                                        `;
                                    }).filter(Boolean).join('')
                                }
                            </div>
                        </div>
                    `;
                }
            }

            html += '</div>';
            return html;
        }

        function getStatusClass(stats) {
            if (stats.status === 'Orphaned' || !stats.is_configured) {
                return 'status-orphaned';
            } else if (stats.is_healthy) {
                return 'status-enabled';
            } else {
                return 'status-disabled';
            }
        }

        function getStatusText(stats) {
            if (stats.status) {
                return stats.status;
            } else if (!stats.is_configured) {
                return 'Orphaned';
            } else {
                return stats.is_healthy ? 'Healthy' : 'Unhealthy';
            }
        }

        function getHostDisplayInfo(hostUrl, hostsConfig) {
            // Find host configuration by URL
            const hostConfig = hostsConfig.find(h => h.host === hostUrl);
            if (hostConfig && hostConfig.name) {
                const description = hostConfig.description ? ` - ${hostConfig.description}` : '';
                return `<div><strong>${hostConfig.name}</strong><br><small style="color: #666;">${hostUrl}${description}</small></div>`;
            }
            return `<strong>${hostUrl}</strong>`;
        }

        function generateHostMetricsRows(hostStats, recentActivity, hostsConfig = []) {
            // If no host stats, try to extract host info from recent activity
            if (!hostStats || Object.keys(hostStats).length === 0) {
                const hostsFromActivity = new Set();
                (recentActivity || []).forEach(activity => {
                    if (activity.host) {
                        hostsFromActivity.add(activity.host);
                    }
                });

                if (hostsFromActivity.size === 0) {
                    return '<tr><td colspan="8" style="text-align: center; color: #666;">No host data available - hosts may not be properly configured</td></tr>';
                }

                // Generate stats from recent activity
                return Array.from(hostsFromActivity).map(host => {
                    const hostActivity = (recentActivity || []).filter(activity => activity.host === host);
                    const requests = hostActivity.length;
                    const errors = hostActivity.filter(activity => activity.status_code >= 400).length;
                    const durations = hostActivity.map(activity => activity.duration);
                    const avgResponseTime = durations.length > 0 ?
                        durations.reduce((sum, duration) => sum + duration, 0) / durations.length : 0;

                    return `
                        <tr>
                            <td>${getHostDisplayInfo(host, hostsConfig)}</td>
                            <td>${formatNumber(requests)}</td>
                            <td>${formatDuration(avgResponseTime)}</td>
                            <td>${requests > 0 ? (((requests - errors) / requests * 100).toFixed(1)) : 100}%</td>
                            <td>-</td>
                            <td>-</td>
                            <td>-</td>
                            <td>0</td>
                            <td><span class="status-badge status-enabled">Active</span></td>
                        </tr>
                    `;
                }).join('');
            }

            return Object.entries(hostStats).map(([host, stats]) => {
                const tokenStats = getTokenStatsForHost(stats);
                return `
                <tr>
                    <td>${getHostDisplayInfo(host, hostsConfig)}</td>
                    <td>${formatNumber(stats.requests || 0)}</td>
                    <td>${formatDuration(stats.avg_response_time || 0)}</td>
                    <td>${calculateHostSuccessRate(stats)}%</td>
                    <td>${formatNumber(tokenStats.total)}</td>
                    <td>${formatTokenRatio(tokenStats.prompt, tokenStats.completion)}</td>
                    <td>${formatTokensPerSecondCompact(stats.token_stats?.prompt_tokens_per_sec || 0, stats.token_stats?.completion_tokens_per_sec || 0)}</td>
                    <td>${stats.active_connections || 0}</td>
                    <td><span class="status-badge ${getStatusClass(stats)}">${getStatusText(stats)}</span></td>
                </tr>
            `;}).join('');
        }

        function getHostDisplayInfoCompact(hostUrl, hostsConfig) {
            // Find host configuration by URL
            const hostConfig = hostsConfig.find(h => h.host === hostUrl);
            if (hostConfig && hostConfig.name) {
                return hostConfig.name;
            }
            // Fallback to simplified URL
            return hostUrl.replace('http://', '').replace(':11434', '').replace(':11440', '');
        }

        function generateModelTreeRows(modelStats, recentActivity, hostStats, hostsConfig = []) {
            if (!modelStats || Object.keys(modelStats).length === 0) {
                return '<tr><td colspan="8" style="text-align: center; color: #666;">No model data available</td></tr>';
            }

            const rows = [];

            Object.entries(modelStats).forEach(([model, stats]) => {
                // Find the most recent activity for this model
                const modelActivity = (recentActivity || [])
                    .filter(activity => activity.model === model)
                    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

                const lastUsed = modelActivity.length > 0 ?
                    new Date(modelActivity[0].timestamp).toLocaleString() : 'Never';

                // Calculate model-level aggregated statistics
                const modelDurations = modelActivity.map(activity => activity.duration);
                const avgResponseTime = modelDurations.length > 0 ?
                    modelDurations.reduce((sum, duration) => sum + duration, 0) / modelDurations.length : 0;

                // Get aggregated token statistics for this model
                const tokenStats = getTokenStatsForModel(stats);
                const totalRequests = stats.requests || 0;

                // Calculate aggregated performance metrics
                let totalPromptTokensPerSec = 0;
                let totalCompletionTokensPerSec = 0;
                let hostCount = 0;

                if (stats.host_usage && Object.keys(stats.host_usage).length > 0) {
                    Object.entries(stats.host_usage).forEach(([host, hostRequests]) => {
                        const hostData = hostStats[host] || {};
                        const hostTokenStats = hostData.token_stats || {};
                        totalPromptTokensPerSec += hostTokenStats.prompt_tokens_per_sec || 0;
                        totalCompletionTokensPerSec += hostTokenStats.completion_tokens_per_sec || 0;
                        hostCount++;
                    });
                }

                // Create model summary row (parent)
                const modelId = `model-${model.replace(/[^a-zA-Z0-9]/g, '-')}`;
                rows.push(`
                    <tr class="model-row" onclick="toggleModelHosts('${modelId}')" data-model="${modelId}">
                        <td class="model-summary"><strong>${model}</strong> <span style="color: #666; font-weight: normal;">(${hostCount} host${hostCount !== 1 ? 's' : ''})</span></td>
                        <td>${formatNumber(totalRequests)}</td>
                        <td>${formatDuration(avgResponseTime)}</td>
                        <td>${calculateHostSuccessRate(stats)}%</td>
                        <td>${formatNumber(tokenStats.total)}</td>
                        <td>${formatTokenRatio(tokenStats.prompt, tokenStats.completion)}</td>
                        <td>${formatTokensPerSecondCompact(totalPromptTokensPerSec, totalCompletionTokensPerSec)}</td>
                        <td>${lastUsed}</td>
                    </tr>
                `);

                // Create host detail rows (children)
                if (stats.host_usage && Object.keys(stats.host_usage).length > 0) {
                    Object.entries(stats.host_usage).forEach(([host, hostRequests]) => {
                        const hostName = getHostDisplayInfoCompact(host, hostsConfig);

                        // Get host-specific token performance data
                        const hostData = hostStats[host] || {};
                        const hostTokenStats = hostData.token_stats || {};

                        // Calculate host-specific average response time for this model
                        const hostModelActivity = modelActivity.filter(activity => activity.host === host);
                        const hostModelDurations = hostModelActivity.map(activity => activity.duration);
                        const hostAvgResponseTime = hostModelDurations.length > 0 ?
                            hostModelDurations.reduce((sum, duration) => sum + duration, 0) / hostModelDurations.length : 0;

                        // Get host-specific last used time
                        const hostLastUsed = hostModelActivity.length > 0 ?
                            new Date(hostModelActivity[0].timestamp).toLocaleString() : 'Never';

                        rows.push(`
                            <tr class="host-row" data-parent="${modelId}">
                                <td class="host-name">${hostName}</td>
                                <td>${formatNumber(hostRequests || 0)}</td>
                                <td>${formatDuration(hostAvgResponseTime)}</td>
                                <td>${calculateHostSuccessRate(stats)}%</td>
                                <td>${formatNumber(hostTokenStats.total_tokens || 0)}</td>
                                <td>${formatTokenRatio(hostTokenStats.prompt_tokens || 0, hostTokenStats.completion_tokens || 0)}</td>
                                <td>${formatTokensPerSecondCompact(hostTokenStats.prompt_tokens_per_sec || 0, hostTokenStats.completion_tokens_per_sec || 0)}</td>
                                <td>${hostLastUsed}</td>
                            </tr>
                        `);
                    });
                }
            });

            return rows.join('');
        }

        function toggleModelHosts(modelId) {
            const modelRow = document.querySelector(`[data-model="${modelId}"]`);
            const hostRows = document.querySelectorAll(`[data-parent="${modelId}"]`);

            if (modelRow && hostRows.length > 0) {
                const isExpanded = modelRow.classList.contains('expanded');

                if (isExpanded) {
                    // Collapse
                    modelRow.classList.remove('expanded');
                    hostRows.forEach(row => row.classList.remove('visible'));
                } else {
                    // Expand
                    modelRow.classList.add('expanded');
                    hostRows.forEach(row => row.classList.add('visible'));
                }
            }
        }

        function formatModelHostInfo(stats) {
            if (!stats.host_usage || Object.keys(stats.host_usage).length === 0) {
                return '<span style="color: #666;">No host data</span>';
            }

            // If there's only one host, show it simply
            const hosts = Object.entries(stats.host_usage);
            if (hosts.length === 1) {
                const [host, count] = hosts[0];
                const hostName = host.replace('http://', '').replace(':11434', '');
                return `<strong>${hostName}</strong> (${count})`;
            }

            // If there are multiple hosts, show the primary host with a tooltip
            if (stats.primary_host) {
                const primaryHostName = stats.primary_host.replace('http://', '').replace(':11434', '');
                const primaryCount = stats.host_usage[stats.primary_host];
                const totalHosts = hosts.length;
                return `<strong>${primaryHostName}</strong> (${primaryCount}) <span style="color: #666;">+${totalHosts - 1} more</span>`;
            }

            return '<span style="color: #666;">Multiple hosts</span>';
        }

        function generateErrorRows(errors) {
            return errors.slice(0, 10).map(error => `
                <tr>
                    <td>${new Date(error.timestamp).toLocaleString()}</td>
                    <td><code>${error.endpoint}</code></td>
                    <td style="color: #dc3545;">${error.error}</td>
                    <td>${error.host || 'Unknown'}</td>
                </tr>
            `).join('');
        }

        function calculateHostSuccessRate(stats) {
            const total = stats.requests || 0;
            const errors = stats.errors || 0;
            if (total === 0) return 100;
            return ((total - errors) / total * 100).toFixed(1);
        }

        function refreshMetrics() {
            loadMetrics();
        }

        // Model Management Functions
        let currentInstallHost = null;
        let currentInstallHostUrl = null;

        function showInstallModelModal(hostId, hostUrl) {
            currentInstallHost = hostId;
            currentInstallHostUrl = hostUrl;

            // Find host name for display
            const hostElement = document.getElementById(`models-${hostId}`);
            const hostName = hostId; // You could enhance this to show the actual host name

            document.getElementById('targetHost').value = `${hostName} (${hostUrl})`;
            document.getElementById('modelName').value = '';
            showModal('installModelModal');
        }

        async function installModel(hostId, hostUrl, modelName) {
            try {
                showAlert(`Installing model '${modelName}' on ${hostId}...`, 'info');

                const result = await apiCall('/config/hosts/models/install', 'POST', {
                    host_url: hostUrl,
                    model_name: modelName
                });

                if (result.status === 'success') {
                    showAlert(`✅ ${result.message}. This may take several minutes to complete.`, 'success');
                    // Refresh models list after a delay
                    setTimeout(() => {
                        loadHostModels(hostId, hostUrl);
                    }, 2000);
                } else {
                    showAlert(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                showAlert(`Failed to install model: ${error.message}`, 'error');
            }
        }

        async function removeModel(hostId, hostUrl, modelName) {
            if (!confirm(`Are you sure you want to remove model '${modelName}' from this host?`)) {
                return;
            }

            try {
                showAlert(`Removing model '${modelName}' from ${hostId}...`, 'info');

                const result = await apiCall('/config/hosts/models/remove', 'POST', {
                    host_url: hostUrl,
                    model_name: modelName
                });

                if (result.status === 'success') {
                    showAlert(`✅ ${result.message}`, 'success');
                    // Refresh models list
                    loadHostModels(hostId, hostUrl);
                } else {
                    showAlert(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                showAlert(`Failed to remove model: ${error.message}`, 'error');
            }
        }

        // Filter models based on search term
        function filterModels(hostId, searchTerm) {
            const grid = document.getElementById(`models-grid-${hostId}`);
            const cards = grid.querySelectorAll('.model-card');

            cards.forEach(card => {
                const modelName = card.dataset.name;
                const modelType = card.dataset.type.toLowerCase();
                const isVisible = modelName.includes(searchTerm.toLowerCase()) ||
                                modelType.includes(searchTerm.toLowerCase());
                card.style.display = isVisible ? 'block' : 'none';
            });
        }

        // Sort models by different criteria
        function sortModels(hostId, sortBy) {
            const grid = document.getElementById(`models-grid-${hostId}`);
            const cards = Array.from(grid.querySelectorAll('.model-card'));

            cards.sort((a, b) => {
                switch(sortBy) {
                    case 'name':
                        return a.dataset.name.localeCompare(b.dataset.name);
                    case 'size':
                        return parseInt(b.dataset.size) - parseInt(a.dataset.size); // Largest first
                    case 'date':
                        return new Date(b.dataset.date) - new Date(a.dataset.date); // Newest first
                    case 'type':
                        return a.dataset.type.localeCompare(b.dataset.type);
                    default:
                        return 0;
                }
            });

            // Re-append sorted cards
            cards.forEach(card => grid.appendChild(card));
        }

        // User Management Functions
        function showAddUserModal() {
            document.getElementById('userModalTitle').textContent = 'Add User';
            document.getElementById('userForm').reset();
            document.getElementById('username').readOnly = false;
            currentEditingUser = null;
            showModal('userModal');
        }

        async function editUser(username) {
            try {
                const users = await apiCall('/config/users');
                const user = users.find(u => u.username === username);

                if (!user) {
                    showAlert('User not found', 'error');
                    return;
                }

                // Populate form
                document.getElementById('username').value = user.username;
                document.getElementById('password').value = ''; // Don't show current password
                document.getElementById('isAdmin').value = user.is_admin.toString();

                // Disable username field for editing
                document.getElementById('username').readOnly = true;

                document.getElementById('userModalTitle').textContent = 'Edit User';
                currentEditingUser = username;
                showModal('userModal');
            } catch (error) {
                showAlert('Failed to load user data: ' + error.message, 'error');
            }
        }

        async function deleteUser(username) {
            if (!confirm(`Are you sure you want to delete user "${username}"?`)) {
                return;
            }

            try {
                await apiCall(`/config/users/${username}`, 'DELETE');
                showAlert('User deleted successfully');
                loadUsers();
                loadOverview();
            } catch (error) {
                showAlert('Failed to delete user: ' + error.message, 'error');
            }
        }

        // Form Handlers
        document.getElementById('hostForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const hostData = {
                id: formData.get('id'),
                name: formData.get('name'),
                description: formData.get('description'),
                host: formData.get('host'),
                enabled: formData.get('enabled') === 'true',
                weight: parseFloat(formData.get('weight')),
                max_connections: parseInt(formData.get('max_connections')),
                timeout: parseInt(formData.get('timeout'))
            };

            try {
                if (currentEditingHost) {
                    await apiCall(`/config/hosts/${currentEditingHost}`, 'PUT', hostData);
                    showAlert('Host updated successfully');
                } else {
                    await apiCall('/config/hosts', 'POST', hostData);
                    showAlert('Host added successfully');
                }

                closeModal('hostModal');
                loadHosts();
                loadOverview();
            } catch (error) {
                showAlert('Failed to save host: ' + error.message, 'error');
            }
        });

        document.getElementById('userForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const userData = {
                username: formData.get('username'),
                password: formData.get('password'),
                is_admin: formData.get('is_admin') === 'true'
            };

            try {
                if (currentEditingUser) {
                    // For updates, only send non-empty password
                    const updateData = {
                        is_admin: userData.is_admin
                    };
                    if (userData.password) {
                        updateData.password = userData.password;
                    }

                    await apiCall(`/config/users/${currentEditingUser}`, 'PUT', updateData);
                    showAlert('User updated successfully');
                } else {
                    await apiCall('/config/users', 'POST', userData);
                    showAlert('User added successfully');
                }

                closeModal('userModal');
                loadUsers();
                loadOverview();
            } catch (error) {
                showAlert('Failed to save user: ' + error.message, 'error');
            }
        });

        // Install Model Form Handler
        document.getElementById('installModelForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const modelName = formData.get('model_name');

            if (!currentInstallHost || !currentInstallHostUrl || !modelName) {
                showAlert('Missing required information', 'error');
                return;
            }

            closeModal('installModelModal');
            await installModel(currentInstallHost, currentInstallHostUrl, modelName);
        });

        // Backup Functions
        async function restoreBackup(filename) {
            if (!confirm(`Are you sure you want to restore from backup "${filename}"? This will overwrite current configuration.`)) {
                return;
            }

            try {
                await apiCall(`/config/backups/${filename}/restore`, 'POST');
                showAlert('Configuration restored successfully');
                refreshData();
            } catch (error) {
                showAlert('Failed to restore backup: ' + error.message, 'error');
            }
        }

        // Export Configuration
        async function exportConfig() {
            try {
                const config = await apiCall('/config/export');
                const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `llm-proxy-config-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                showAlert('Configuration exported successfully');
            } catch (error) {
                showAlert('Failed to export configuration: ' + error.message, 'error');
            }
        }

        // Utility Functions
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleString();
        }

        // Test connection function for modal
        async function testConnection() {
            const hostUrl = document.getElementById('hostUrl').value;
            if (!hostUrl) {
                showAlert('Please enter a host URL first', 'warning');
                return;
            }

            await testHostConnection(hostUrl);
        }

        // Chat Interface Functions
        function toggleChatInterface() {
            const modal = document.getElementById('chatModal');
            modal.style.display = 'block';
            loadChatModelsAndHosts();
        }

        function closeChatInterface() {
            document.getElementById('chatModal').style.display = 'none';
        }

        async function loadChatModelsAndHosts() {
            try {
                // Load hosts
                const hosts = await apiCall('/config/hosts');

                // Populate host dropdown
                const hostSelect = document.getElementById('chatHost');
                hostSelect.innerHTML = '<option value="">Auto (Load Balanced)</option>';
                hosts.forEach(hostConfig => {
                    if (hostConfig.enabled) {
                        const hostUrl = hostConfig.host || hostConfig.host_url || '';
                        const hostName = hostConfig.name || hostConfig.id || 'Unknown Host';
                        hostSelect.innerHTML += `<option value="${hostUrl}">${hostName} (${hostUrl})</option>`;
                    }
                });

                // Add event listener for host selection change
                hostSelect.addEventListener('change', async function() {
                    await loadChatModels(this.value);
                });

                // Load all models initially (for Auto Load Balanced)
                await loadChatModels('');
            } catch (error) {
                console.error('Failed to load chat data:', error);
                showAlert('Failed to load models and hosts', 'error');
            }
        }

        async function loadChatModels(hostUrl) {
            try {
                const modelSelect = document.getElementById('chatModel');
                modelSelect.innerHTML = '<option value="">Loading models...</option>';

                let modelsResponse;
                if (hostUrl) {
                    // Load models from specific host
                    modelsResponse = await apiCall('/config/hosts/models', 'POST', { host_url: hostUrl });
                } else {
                    // Load all models (Auto Load Balanced)
                    modelsResponse = await apiCall('/api/tags');
                }

                // Populate model dropdown
                modelSelect.innerHTML = '<option value="">Select Model...</option>';
                const models = modelsResponse.models || [];

                // Extract and sort model names
                const modelNames = models
                    .map(model => model.name || model.model)
                    .filter(name => name) // Remove any null/undefined names
                    .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase())); // Case-insensitive alphabetical sort

                modelNames.forEach(modelName => {
                    modelSelect.innerHTML += `<option value="${modelName}">${modelName}</option>`;
                });

                if (models.length === 0) {
                    modelSelect.innerHTML = '<option value="">No models available</option>';
                }
            } catch (error) {
                console.error('Failed to load models:', error);
                const modelSelect = document.getElementById('chatModel');
                modelSelect.innerHTML = '<option value="">Error loading models</option>';
                showAlert('Failed to load models', 'error');
            }
        }

        function clearChatHistory() {
            const messagesDiv = document.getElementById('chatMessages');
            messagesDiv.innerHTML = '<div style="text-align: center; color: #666; font-style: italic;">Chat history cleared. Ready for new conversation...</div>';
            // Clear conversation context to start fresh
            chatContext = null;
        }

        function cancelChatMessage() {
            if (currentChatController) {
                currentChatController.abort();
                currentChatController = null;

                // Reset UI
                const sendButton = document.getElementById('sendButton');
                const cancelButton = document.getElementById('cancelButton');

                sendButton.disabled = false;
                sendButton.textContent = 'Send';
                cancelButton.style.display = 'none';

                // Add cancellation message
                const messagesDiv = document.getElementById('chatMessages');
                messagesDiv.innerHTML += `
                    <div class="chat-message chat-message-error">
                        <div class="chat-message-header" style="color: #ff9800;">⚠️ Cancelled:</div>
                        <div class="chat-message-content">
                            Request was cancelled by user.
                        </div>
                    </div>
                `;

                // Scroll to bottom
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
        }

        async function sendChatMessage() {
            const input = document.getElementById('chatInput');
            const model = document.getElementById('chatModel').value;
            const host = document.getElementById('chatHost').value;
            const message = input.value.trim();

            if (!model) {
                showAlert('Please select a model first', 'warning');
                return;
            }

            if (!message) {
                showAlert('Please enter a message', 'warning');
                return;
            }

            const messagesDiv = document.getElementById('chatMessages');
            const sendButton = document.getElementById('sendButton');
            const cancelButton = document.getElementById('cancelButton');

            // Clear placeholder text if present
            if (messagesDiv.innerHTML.includes('Select a model and start chatting')) {
                messagesDiv.innerHTML = '';
            }

            // Add user message
            messagesDiv.innerHTML += `
                <div class="chat-message chat-message-user">
                    <div class="chat-message-header">👤 You:</div>
                    <div class="chat-message-content">
                        ${formatResponse(message)}
                    </div>
                </div>
            `;

            // Clear input and update UI for sending state
            input.value = '';
            sendButton.disabled = true;
            sendButton.textContent = 'Sending...';
            cancelButton.style.display = 'inline-block';

            // Create abort controller for this request
            currentChatController = new AbortController();

            // Scroll to bottom
            messagesDiv.scrollTop = messagesDiv.scrollHeight;

            try {
                const startTime = Date.now();

                // Prepare request body for streaming
                let finalPrompt = message;

                // For DeepSeek-R1 models, try to get cleaner output
                if (model.toLowerCase().includes('deepseek-r1') || model.toLowerCase().includes('deepseek_r1')) {
                    finalPrompt = `${message}\n\nPlease provide a clear, concise answer without showing your reasoning process.`;
                }

                const requestBody = {
                    model: model,
                    prompt: finalPrompt,
                    stream: true
                };

                // Add conversation context if available
                if (chatContext) {
                    requestBody.context = chatContext;
                }

                // Add host if specified
                if (host) {
                    requestBody.host = host;
                }

                console.log('Chat streaming request body:', JSON.stringify(requestBody, null, 2));

                // Create assistant message container for streaming
                const assistantMessageId = 'assistant-' + Date.now();
                messagesDiv.innerHTML += `
                    <div class="chat-message chat-message-assistant" id="${assistantMessageId}">
                        <div class="chat-message-header">🤖 ${model}:</div>
                        <div id="${assistantMessageId}-thinking" class="thinking-container"></div>
                        <div class="chat-message-content" id="${assistantMessageId}-content">
                            <div class="streaming-indicator">💭 Thinking...</div>
                        </div>
                        <div class="chat-message-metrics" id="${assistantMessageId}-metrics" style="display: none;">
                        </div>
                    </div>
                `;

                // Scroll to bottom
                messagesDiv.scrollTop = messagesDiv.scrollHeight;

                // Variables for streaming
                let accumulatedResponse = '';
                let finalMetrics = null;
                let thinkingContent = null;
                let hostUsed = host || 'Load Balanced';

                // Handle streaming chunks
                await streamingApiCall('/api/generate', 'POST', requestBody, currentChatController, async (chunk) => {
                    console.log('Received chunk:', chunk);

                    const contentDiv = document.getElementById(`${assistantMessageId}-content`);
                    const metricsDiv = document.getElementById(`${assistantMessageId}-metrics`);

                    if (chunk.response) {
                        accumulatedResponse += chunk.response;

                        // Update content in real-time
                        contentDiv.innerHTML = formatResponse(accumulatedResponse);

                        // Auto-scroll to bottom
                        messagesDiv.scrollTop = messagesDiv.scrollHeight;
                    }

                    // Handle final chunk with complete data
                    if (chunk.done) {
                        const endTime = Date.now();
                        const responseTime = endTime - startTime;

                        // Update conversation context for next message
                        if (chunk.context) {
                            chatContext = chunk.context;
                        }

                        // Extract metrics
                        hostUsed = chunk.proxy_server || host || 'Load Balanced';
                        const promptTokens = chunk.prompt_eval_count || 0;
                        const completionTokens = chunk.eval_count || 0;
                        const promptDuration = chunk.prompt_eval_duration || 0;
                        const evalDuration = chunk.eval_duration || 0;

                        const promptSpeed = promptDuration > 0 ? (promptTokens / (promptDuration / 1_000_000_000)).toFixed(1) : 0;
                        const completionSpeed = evalDuration > 0 ? (completionTokens / (evalDuration / 1_000_000_000)).toFixed(1) : 0;

                        // Add thinking section if available
                        if (chunk.thinking) {
                            const thinkingDiv = document.getElementById(`${assistantMessageId}-thinking`);
                            thinkingDiv.innerHTML = formatThinkingSection(
                                chunk.thinking,
                                chunk.reasoning_tokens,
                                chunk.reasoning_duration
                            );
                        }

                        // Update metrics
                        metricsDiv.innerHTML = `
                            <span>📡 Host: ${hostUsed}</span>
                            <span>⏱️ Response: ${responseTime}ms</span>
                            <span>🔤 Tokens: ${promptTokens}+${completionTokens}</span>
                            <span>⚡ Speed: ${promptSpeed}|${completionSpeed} tok/s</span>
                        `;
                        metricsDiv.style.display = 'block';
                    }
                });

            } catch (error) {
                console.error('Chat error:', error);

                // Check if error is due to cancellation
                if (error.name === 'AbortError') {
                    // Request was cancelled, message already added by cancelChatMessage()
                    return;
                }

                messagesDiv.innerHTML += `
                    <div class="chat-message chat-message-error">
                        <div class="chat-message-header" style="color: #d32f2f;">❌ Error:</div>
                        <div class="chat-message-content">
                            ${formatResponse(error.message)}
                        </div>
                    </div>
                `;
            } finally {
                // Clean up controller and reset UI
                currentChatController = null;
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
                cancelButton.style.display = 'none';

                // Scroll to bottom
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function formatThinkingSection(thinking, reasoningTokens, reasoningDuration) {
            if (!thinking) return '';

            const thinkingId = 'thinking-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

            let metricsHtml = '';
            if (reasoningTokens || reasoningDuration) {
                const metrics = [];
                if (reasoningTokens) metrics.push(`🧠 Reasoning Tokens: ${reasoningTokens}`);
                if (reasoningDuration) {
                    const reasoningMs = Math.round(reasoningDuration / 1_000_000);
                    metrics.push(`⏱️ Reasoning Time: ${reasoningMs}ms`);
                }
                if (metrics.length > 0) {
                    metricsHtml = `<div class="thinking-metrics">${metrics.join(' • ')}</div>`;
                }
            }

            // Clean and format the thinking content
            const cleanedThinking = cleanThinkingContent(thinking);

            return `
                <div class="thinking-section">
                    <div class="thinking-header" onclick="toggleThinking('${thinkingId}')">
                        <span class="thinking-toggle">▶</span>
                        <span>🤔 Thinking Process</span>
                    </div>
                    <div id="${thinkingId}" class="thinking-content" style="display: none;">
                        ${cleanedThinking}
                    </div>
                    ${metricsHtml}
                </div>
            `;
        }

        function cleanThinkingContent(thinking) {
            if (!thinking) return '';

            // Clean up excessive whitespace and normalize
            let cleaned = thinking
                .replace(/\s+/g, ' ')
                .replace(/\n\s*\n/g, '\n\n')
                .trim();

            // Remove repetitive phrases and clean up stream-of-consciousness
            cleaned = cleaned
                .replace(/\b(But|Well|Actually|Hmm|Let's see|Wait|No|Yes)\b,?\s*/gi, '')
                .replace(/\b(I think|I know|I can|I should|I need to|Let me)\b\s*/gi, '')
                .replace(/\?\s*No[,.]?\s*/gi, '. ')
                .replace(/\.\s*\.\s*\./g, '.')
                .replace(/\s+/g, ' ')
                .trim();

            // Split into logical steps
            const steps = [];
            const sentences = cleaned.split(/(?<=[.!?])\s+/);
            let currentStep = [];

            for (let sentence of sentences) {
                sentence = sentence.trim();
                if (!sentence) continue;

                // Identify step markers
                const isStepStart = sentence.match(/^(First|Second|Third|Next|Now|Then|So|Therefore|Finally|Step \d+)/i);
                const isCalculation = sentence.match(/\d+\s*[+\-*/×÷]\s*\d+|=\s*\d+/);
                const isConclusion = sentence.match(/^(So|Therefore|Thus|Hence|The answer is|Final answer)/i);

                // Start new step
                if ((isStepStart || isConclusion) && currentStep.length > 0) {
                    steps.push(currentStep.join(' '));
                    currentStep = [];
                }

                currentStep.push(sentence);

                // End step after calculations or conclusions
                if ((isCalculation || isConclusion) && currentStep.length >= 1) {
                    steps.push(currentStep.join(' '));
                    currentStep = [];
                }
            }

            // Add remaining content
            if (currentStep.length > 0) {
                steps.push(currentStep.join(' '));
            }

            // Format steps with better structure
            const formattedSteps = steps
                .filter(step => step.trim().length > 10) // Filter out very short fragments
                .map((step, index) => {
                    step = step.trim();

                    // Add step numbering for major steps
                    if (step.match(/^(First|Second|Third|Next|Now|Then)/i)) {
                        return `<div class="thinking-step"><strong>Step ${index + 1}:</strong> ${escapeHtml(step)}</div>`;
                    }

                    // Highlight calculations
                    if (step.match(/\d+\s*[+\-*/×÷]\s*\d+|=\s*\d+/)) {
                        return `<div class="thinking-calculation">${escapeHtml(step)}</div>`;
                    }

                    // Highlight conclusions
                    if (step.match(/^(So|Therefore|Thus|Hence|The answer is|Final answer)/i)) {
                        return `<div class="thinking-conclusion"><strong>Conclusion:</strong> ${escapeHtml(step)}</div>`;
                    }

                    return `<div class="thinking-paragraph">${escapeHtml(step)}</div>`;
                })
                .join('');

            return formattedSteps || `<div class="thinking-paragraph">${escapeHtml(cleaned)}</div>`;
        }

        function toggleThinking(thinkingId) {
            const content = document.getElementById(thinkingId);
            const toggle = content.previousElementSibling.querySelector('.thinking-toggle');

            if (content.style.display === 'none') {
                content.style.display = 'block';
                toggle.textContent = '▼';
                toggle.classList.add('expanded');
            } else {
                content.style.display = 'none';
                toggle.textContent = '▶';
                toggle.classList.remove('expanded');
            }
        }

        // Performance Profiling Functions
        let profilingData = {
            hosts: [],
            models: [],
            hostModels: {}, // Maps host URLs to their available models
            modelHosts: {}, // Maps model names to hosts that support them
            selectedHosts: new Set(),
            selectedModels: new Set(),
            isRunning: false,
            results: []
        };

        function selectPredefinedPrompt() {
            const selector = document.getElementById('prompt-selector');
            const textarea = document.getElementById('profiling-prompt');

            if (selector.value) {
                textarea.value = selector.value;
                // Reset the selector to show the placeholder
                selector.value = '';
            }
        }

        function formatResponse(text) {
            // Basic markdown-like formatting for better readability
            let formatted = text;

            // Convert code blocks (```language\ncode\n```)
            formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)\n```/g, (match, lang, code) => {
                return `<pre><code class="language-${lang || 'text'}">${escapeHtml(code.trim())}</code></pre>`;
            });

            // Convert inline code (`code`)
            formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');

            // Convert headers
            formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');
            formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
            formatted = formatted.replace(/^# (.*$)/gm, '<h1>$1</h1>');

            // Convert bold text
            formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

            // Convert bullet points
            formatted = formatted.replace(/^\* (.*$)/gm, '<li>$1</li>');
            formatted = formatted.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

            // Convert numbered lists
            formatted = formatted.replace(/^\d+\. (.*$)/gm, '<li>$1</li>');

            // Convert line breaks to paragraphs
            formatted = formatted.replace(/\n\n/g, '</p><p>');
            formatted = '<p>' + formatted + '</p>';

            // Clean up empty paragraphs
            formatted = formatted.replace(/<p><\/p>/g, '');
            formatted = formatted.replace(/<p>(<[^>]+>)/g, '$1');
            formatted = formatted.replace(/(<\/[^>]+>)<\/p>/g, '$1');

            return formatted;
        }

        function toggleResponse(resultId) {
            const preview = document.getElementById(`preview-${resultId}`);
            const full = document.getElementById(`full-${resultId}`);
            const btn = document.getElementById(`btn-${resultId}`);

            if (full.style.display === 'none') {
                // Show full response
                preview.style.display = 'none';
                full.style.display = 'block';
                btn.textContent = '📄 Show Preview';
                btn.title = 'Show truncated preview';
            } else {
                // Show preview
                preview.style.display = 'block';
                full.style.display = 'none';
                btn.textContent = '📖 Show Full Response';
                btn.title = 'Show complete response';
            }
        }

        async function loadProfilingData() {
            try {
                // Load hosts
                const hosts = await apiCall('/config/hosts');
                profilingData.hosts = hosts.filter(host => host.enabled);

                // Load models for each host to build compatibility mapping
                profilingData.hostModels = {};
                profilingData.modelHosts = {};

                const hostModelPromises = profilingData.hosts.map(async (host) => {
                    try {
                        const modelsResponse = await apiCall('/config/hosts/models', 'POST', { host_url: host.host });
                        const models = modelsResponse.models || [];
                        const modelNames = models
                            .map(model => model.name || model.model)
                            .filter(name => name);

                        profilingData.hostModels[host.host] = modelNames;

                        // Build reverse mapping (models to hosts)
                        modelNames.forEach(modelName => {
                            if (!profilingData.modelHosts[modelName]) {
                                profilingData.modelHosts[modelName] = [];
                            }
                            profilingData.modelHosts[modelName].push(host.host);
                        });

                        return { host: host.host, models: modelNames };
                    } catch (error) {
                        console.warn(`Failed to load models for host ${host.host}:`, error);
                        profilingData.hostModels[host.host] = [];
                        return { host: host.host, models: [] };
                    }
                });

                // Wait for all host model loading to complete
                await Promise.all(hostModelPromises);

                // Get all unique models across all hosts
                const allModels = new Set();
                Object.values(profilingData.hostModels).forEach(models => {
                    models.forEach(model => allModels.add(model));
                });
                profilingData.models = Array.from(allModels).sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()));

                // Render the selection interfaces
                renderHostSelection();
                renderModelSelection();
                updateSelectionCounts();

            } catch (error) {
                console.error('Failed to load profiling data:', error);
                showAlert('Failed to load profiling data: ' + error.message, 'error');
            }
        }

        function renderHostSelection() {
            const hostSelection = document.getElementById('host-selection');
            hostSelection.innerHTML = profilingData.hosts.map(host => {
                const isCompatible = isHostCompatibleWithSelectedModels(host.host);
                const isDisabled = profilingData.selectedModels.size > 0 && !isCompatible;

                return `
                    <div class="selection-item ${isDisabled ? 'disabled' : ''}">
                        <input type="checkbox" id="host-${host.id}" value="${host.host}"
                               ${isDisabled ? 'disabled' : ''}
                               ${profilingData.selectedHosts.has(host.host) ? 'checked' : ''}
                               onchange="toggleHostSelection('${host.host}', this.checked)">
                        <label for="host-${host.id}" ${isDisabled ? 'style="color: #ccc;"' : ''}>
                            ${host.name || host.id} (${host.host})
                            ${isDisabled ? ' - No compatible models' : ''}
                        </label>
                    </div>
                `;
            }).join('');
        }

        function renderModelSelection() {
            const modelSelection = document.getElementById('model-selection');
            modelSelection.innerHTML = profilingData.models.map(modelName => {
                const isCompatible = isModelCompatibleWithSelectedHosts(modelName);
                const isDisabled = profilingData.selectedHosts.size > 0 && !isCompatible;
                const hostCount = profilingData.modelHosts[modelName]?.length || 0;

                return `
                    <div class="selection-item ${isDisabled ? 'disabled' : ''}">
                        <input type="checkbox" id="model-${modelName.replace(/[^a-zA-Z0-9]/g, '-')}"
                               value="${modelName}"
                               ${isDisabled ? 'disabled' : ''}
                               ${profilingData.selectedModels.has(modelName) ? 'checked' : ''}
                               onchange="toggleModelSelection('${modelName}', this.checked)">
                        <label for="model-${modelName.replace(/[^a-zA-Z0-9]/g, '-')}" ${isDisabled ? 'style="color: #ccc;"' : ''}>
                            ${modelName}
                            <small style="color: #666;">(${hostCount} host${hostCount !== 1 ? 's' : ''})</small>
                            ${isDisabled ? ' - Not on selected hosts' : ''}
                        </label>
                    </div>
                `;
            }).join('');
        }

        function isHostCompatibleWithSelectedModels(hostUrl) {
            if (profilingData.selectedModels.size === 0) return true;

            const hostModels = profilingData.hostModels[hostUrl] || [];
            return Array.from(profilingData.selectedModels).some(model => hostModels.includes(model));
        }

        function isModelCompatibleWithSelectedHosts(modelName) {
            if (profilingData.selectedHosts.size === 0) return true;

            const modelHosts = profilingData.modelHosts[modelName] || [];
            return Array.from(profilingData.selectedHosts).some(host => modelHosts.includes(host));
        }

        function toggleHostSelection(hostUrl, isSelected) {
            if (isSelected) {
                profilingData.selectedHosts.add(hostUrl);
            } else {
                profilingData.selectedHosts.delete(hostUrl);
            }

            // Update model selection to show only compatible models
            renderModelSelection();

            // Update selection count display
            updateSelectionCounts();
        }

        function toggleModelSelection(modelName, isSelected) {
            if (isSelected) {
                profilingData.selectedModels.add(modelName);
            } else {
                profilingData.selectedModels.delete(modelName);
            }

            // Update host selection to show only compatible hosts
            renderHostSelection();

            // Update selection count display
            updateSelectionCounts();
        }

        function updateSelectionCounts() {
            // Update host selection header
            const hostHeader = document.getElementById('host-selection-title');
            if (hostHeader) {
                const enabledHosts = profilingData.hosts.filter(host =>
                    isHostCompatibleWithSelectedModels(host.host)
                ).length;
                hostHeader.innerHTML = `🖥️ Select Hosts (${profilingData.selectedHosts.size} selected, ${enabledHosts} available)`;
            }

            // Update model selection header
            const modelHeader = document.getElementById('model-selection-title');
            if (modelHeader) {
                const enabledModels = profilingData.models.filter(model =>
                    isModelCompatibleWithSelectedHosts(model)
                ).length;
                modelHeader.innerHTML = `🤖 Select Models (${profilingData.selectedModels.size} selected, ${enabledModels} available)`;
            }
        }

        function selectAllHosts() {
            profilingData.hosts.forEach(host => {
                if (isHostCompatibleWithSelectedModels(host.host)) {
                    profilingData.selectedHosts.add(host.host);
                }
            });
            renderHostSelection();
            renderModelSelection();
            updateSelectionCounts();
        }

        function clearAllHosts() {
            profilingData.selectedHosts.clear();
            renderHostSelection();
            renderModelSelection();
            updateSelectionCounts();
        }

        function selectAllModels() {
            profilingData.models.forEach(model => {
                if (isModelCompatibleWithSelectedHosts(model)) {
                    profilingData.selectedModels.add(model);
                }
            });
            renderHostSelection();
            renderModelSelection();
            updateSelectionCounts();
        }

        function clearAllModels() {
            profilingData.selectedModels.clear();
            renderHostSelection();
            renderModelSelection();
            updateSelectionCounts();
        }

        async function startProfiling() {
            const prompt = document.getElementById('profiling-prompt').value.trim();

            if (!prompt) {
                showAlert('Please enter a test prompt', 'warning');
                return;
            }

            if (profilingData.selectedHosts.size === 0) {
                showAlert('Please select at least one host', 'warning');
                return;
            }

            if (profilingData.selectedModels.size === 0) {
                showAlert('Please select at least one model', 'warning');
                return;
            }

            if (profilingData.isRunning) {
                showAlert('Profiling is already running', 'warning');
                return;
            }

            profilingData.isRunning = true;
            profilingData.results = [];

            // Update UI
            const startBtn = document.getElementById('start-profiling-btn');
            const progressBar = document.getElementById('profiling-progress');
            const progressFill = document.getElementById('profiling-progress-bar');
            const status = document.getElementById('profiling-status');

            startBtn.disabled = true;
            startBtn.textContent = '⏳ Running Tests...';
            progressBar.style.display = 'block';
            status.style.display = 'block';
            status.textContent = 'Initializing performance tests...';

            // Calculate total tests
            const totalTests = profilingData.selectedHosts.size * profilingData.selectedModels.size;
            let completedTests = 0;

            // Clear previous results
            document.getElementById('profiling-results').innerHTML = '';

            try {
                // Run tests for each host-model combination
                for (const hostUrl of profilingData.selectedHosts) {
                    for (const modelName of profilingData.selectedModels) {
                        status.textContent = `Testing ${modelName} on ${getHostName(hostUrl)}...`;

                        const result = await runSingleTest(hostUrl, modelName, prompt);
                        profilingData.results.push(result);

                        completedTests++;
                        const progress = (completedTests / totalTests) * 100;
                        progressFill.style.width = `${progress}%`;

                        // Update results display
                        displayProfilingResults();
                    }
                }

                status.textContent = `Completed ${totalTests} tests successfully!`;
                showAlert(`Performance profiling completed! Tested ${profilingData.selectedModels.size} models across ${profilingData.selectedHosts.size} hosts.`, 'success');

            } catch (error) {
                console.error('Profiling error:', error);
                status.textContent = 'Profiling failed with errors';
                showAlert('Profiling failed: ' + error.message, 'error');
            } finally {
                // Reset UI
                profilingData.isRunning = false;
                startBtn.disabled = false;
                startBtn.textContent = '🚀 Start Performance Test';
                setTimeout(() => {
                    progressBar.style.display = 'none';
                    status.style.display = 'none';
                }, 3000);
            }
        }

        async function runSingleTest(hostUrl, modelName, prompt) {
            const startTime = Date.now();
            const result = {
                id: `result-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                hostUrl: hostUrl,
                hostName: getHostName(hostUrl),
                modelName: modelName,
                prompt: prompt,
                startTime: startTime,
                endTime: null,
                duration: null,
                status: 'running',
                response: null,
                error: null,
                metrics: {
                    promptTokens: 0,
                    completionTokens: 0,
                    totalTokens: 0,
                    promptSpeed: 0,
                    completionSpeed: 0,
                    promptDuration: 0,
                    evalDuration: 0
                }
            };

            try {
                // Use the main API with specific host to ensure model availability
                const requestBody = {
                    model: modelName,
                    prompt: prompt,
                    stream: false,
                    host: hostUrl  // This will force the request to use this specific host
                };

                const response = await apiCall('/api/generate', 'POST', requestBody);

                result.endTime = Date.now();
                result.duration = result.endTime - result.startTime;
                result.status = 'success';
                result.response = response.response;

                // Extract performance metrics
                result.metrics.promptTokens = response.prompt_eval_count || 0;
                result.metrics.completionTokens = response.eval_count || 0;
                result.metrics.totalTokens = result.metrics.promptTokens + result.metrics.completionTokens;
                result.metrics.promptDuration = response.prompt_eval_duration || 0;
                result.metrics.evalDuration = response.eval_duration || 0;

                // Calculate speeds (tokens per second)
                if (result.metrics.promptDuration > 0) {
                    result.metrics.promptSpeed = (result.metrics.promptTokens / (result.metrics.promptDuration / 1_000_000_000)).toFixed(1);
                }
                if (result.metrics.evalDuration > 0) {
                    result.metrics.completionSpeed = (result.metrics.completionTokens / (result.metrics.evalDuration / 1_000_000_000)).toFixed(1);
                }

            } catch (error) {
                result.endTime = Date.now();
                result.duration = result.endTime - result.startTime;
                result.status = 'error';

                // Extract more detailed error information
                if (error.response && error.response.data) {
                    result.error = error.response.data.detail || error.response.data.message || error.message;
                } else if (error.detail) {
                    result.error = error.detail;
                } else {
                    result.error = error.message || 'Unknown error occurred';
                }

                console.error(`Performance test failed for ${modelName} on ${hostUrl}:`, error);
            }

            return result;
        }

        function getHostName(hostUrl) {
            const host = profilingData.hosts.find(h => h.host === hostUrl);
            return host ? (host.name || host.id) : hostUrl.replace('http://', '').replace(':11434', '');
        }

        function displayProfilingResults() {
            const resultsContainer = document.getElementById('profiling-results');

            if (profilingData.results.length === 0) {
                resultsContainer.innerHTML = '';
                return;
            }

            // Group results by model for better organization
            const resultsByModel = {};
            profilingData.results.forEach(result => {
                if (!resultsByModel[result.modelName]) {
                    resultsByModel[result.modelName] = [];
                }
                resultsByModel[result.modelName].push(result);
            });

            let html = '<div class="results-grid">';

            // Display individual results
            profilingData.results.forEach(result => {
                const statusClass = result.status === 'success' ? 'success' :
                                  result.status === 'error' ? 'error' : 'running';

                html += `
                    <div class="result-card">
                        <div class="result-header">
                            <div class="result-title">${result.modelName} @ ${result.hostName}</div>
                            <div class="result-status ${statusClass}">${result.status.toUpperCase()}</div>
                        </div>

                        ${result.status === 'success' ? `
                            <div class="result-metrics">
                                <div class="result-metric">
                                    <div class="result-metric-value">${result.duration}ms</div>
                                    <div class="result-metric-label">Response Time</div>
                                </div>
                                <div class="result-metric">
                                    <div class="result-metric-value">${result.metrics.totalTokens}</div>
                                    <div class="result-metric-label">Total Tokens</div>
                                </div>
                                <div class="result-metric">
                                    <div class="result-metric-value">${result.metrics.promptSpeed}</div>
                                    <div class="result-metric-label">Prompt tok/s</div>
                                </div>
                                <div class="result-metric">
                                    <div class="result-metric-value">${result.metrics.completionSpeed}</div>
                                    <div class="result-metric-label">Completion tok/s</div>
                                </div>
                            </div>

                            <div class="result-response">
                                ${result.thinking ? formatThinkingSection(result.thinking, result.reasoning_tokens, result.reasoning_duration) : ''}
                                <div class="response-preview response-content" id="preview-${result.id}">
                                    ${result.response.length > 200 ?
                                        formatResponse(result.response.substring(0, 200)) + '<p><em>... (truncated)</em></p>' :
                                        formatResponse(result.response)
                                    }
                                </div>
                                ${result.response.length > 200 ? `
                                    <div class="response-full response-content" id="full-${result.id}" style="display: none;">
                                        ${formatResponse(result.response)}
                                    </div>
                                    <button class="expand-btn" onclick="toggleResponse('${result.id}')" id="btn-${result.id}">
                                        📖 Show Full Response
                                    </button>
                                ` : ''}
                            </div>
                        ` : result.status === 'error' ? `
                            <div class="result-response" style="color: #dc3545; background: #f8d7da;">
                                Error: ${escapeHtml(result.error)}
                            </div>
                        ` : `
                            <div class="result-response">
                                Test in progress...
                            </div>
                        `}
                    </div>
                `;
            });

            html += '</div>';

            // Add summary table if we have completed results
            const completedResults = profilingData.results.filter(r => r.status === 'success');
            if (completedResults.length > 0) {
                html += generateSummaryTable(completedResults);
            }

            resultsContainer.innerHTML = html;
        }

        function generateSummaryTable(results) {
            // Calculate summary statistics
            const summaryData = [];

            // Group by model
            const modelGroups = {};
            results.forEach(result => {
                if (!modelGroups[result.modelName]) {
                    modelGroups[result.modelName] = [];
                }
                modelGroups[result.modelName].push(result);
            });

            // Calculate averages for each model
            Object.entries(modelGroups).forEach(([modelName, modelResults]) => {
                const avgDuration = modelResults.reduce((sum, r) => sum + r.duration, 0) / modelResults.length;
                const avgTokens = modelResults.reduce((sum, r) => sum + r.metrics.totalTokens, 0) / modelResults.length;
                const avgPromptSpeed = modelResults.reduce((sum, r) => sum + parseFloat(r.metrics.promptSpeed || 0), 0) / modelResults.length;
                const avgCompletionSpeed = modelResults.reduce((sum, r) => sum + parseFloat(r.metrics.completionSpeed || 0), 0) / modelResults.length;

                summaryData.push({
                    model: modelName,
                    avgDuration: avgDuration,
                    avgTokens: avgTokens,
                    avgPromptSpeed: avgPromptSpeed,
                    avgCompletionSpeed: avgCompletionSpeed,
                    hostCount: modelResults.length
                });
            });

            // Sort by average response time (fastest first)
            summaryData.sort((a, b) => a.avgDuration - b.avgDuration);

            // Find best performers for highlighting
            const fastestResponse = Math.min(...summaryData.map(s => s.avgDuration));
            const highestPromptSpeed = Math.max(...summaryData.map(s => s.avgPromptSpeed));
            const highestCompletionSpeed = Math.max(...summaryData.map(s => s.avgCompletionSpeed));

            let html = `
                <table class="summary-table">
                    <thead>
                        <tr>
                            <th>Model</th>
                            <th>Avg Response Time</th>
                            <th>Avg Tokens</th>
                            <th>Avg Prompt Speed (tok/s)</th>
                            <th>Avg Completion Speed (tok/s)</th>
                            <th>Hosts Tested</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            summaryData.forEach(data => {
                const isFastestResponse = Math.abs(data.avgDuration - fastestResponse) < 1;
                const isFastestPrompt = Math.abs(data.avgPromptSpeed - highestPromptSpeed) < 0.1;
                const isFastestCompletion = Math.abs(data.avgCompletionSpeed - highestCompletionSpeed) < 0.1;

                html += `
                    <tr>
                        <td><strong>${data.model}</strong></td>
                        <td class="metric-cell ${isFastestResponse ? 'best-metric' : ''}">${Math.round(data.avgDuration)}ms</td>
                        <td class="metric-cell">${Math.round(data.avgTokens)}</td>
                        <td class="metric-cell ${isFastestPrompt ? 'best-metric' : ''}">${data.avgPromptSpeed.toFixed(1)} tok/s</td>
                        <td class="metric-cell ${isFastestCompletion ? 'best-metric' : ''}">${data.avgCompletionSpeed.toFixed(1)} tok/s</td>
                        <td class="metric-cell">${data.hostCount}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
            `;

            return html;
        }

        function clearProfilingResults() {
            if (profilingData.isRunning) {
                showAlert('Cannot clear results while profiling is running', 'warning');
                return;
            }

            profilingData.results = [];
            document.getElementById('profiling-results').innerHTML = '';
            showAlert('Profiling results cleared', 'success');
        }

        async function refreshModelRegistry() {
            const refreshBtn = document.getElementById('refresh-models-btn');
            const originalText = refreshBtn.textContent;

            try {
                refreshBtn.disabled = true;
                refreshBtn.textContent = '🔄 Refreshing...';

                const response = await apiCall('/api/tags?refresh=true', 'GET');

                showAlert(`✅ Model registry refreshed! Found ${response.models.length} models.`, 'success');

                // Reload profiling data to reflect changes
                await loadProfilingData();

                document.getElementById('profiling-status').textContent =
                    `Model registry refreshed. Found ${response.models.length} models across all hosts.`;

            } catch (error) {
                console.error('Error refreshing model registry:', error);
                showAlert(`❌ Error refreshing model registry: ${error.message}`, 'error');
            } finally {
                refreshBtn.disabled = false;
                refreshBtn.textContent = originalText;
            }
        }

        async function cleanupOrphanedHosts() {
            const cleanupBtn = document.getElementById('cleanup-hosts-btn');
            const originalText = cleanupBtn.textContent;

            try {
                cleanupBtn.disabled = true;
                cleanupBtn.textContent = '🧹 Cleaning...';

                const response = await apiCall('/proxy/metrics/cleanup', 'POST');

                showAlert(`✅ Orphaned host metrics cleaned up! Currently configured: ${response.configured_hosts.length} hosts.`, 'success');

                // Reload profiling data to reflect changes
                await loadProfilingData();

                document.getElementById('profiling-status').textContent =
                    `Orphaned hosts cleaned up. Currently configured: ${response.configured_hosts.length} hosts.`;

            } catch (error) {
                console.error('Error cleaning up orphaned hosts:', error);
                showAlert(`❌ Error cleaning up orphaned hosts: ${error.message}`, 'error');
            } finally {
                cleanupBtn.disabled = false;
                cleanupBtn.textContent = originalText;
            }
        }


    </script>
</body>
</html>
