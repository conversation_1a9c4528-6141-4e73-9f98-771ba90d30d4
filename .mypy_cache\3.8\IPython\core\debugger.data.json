{".class": "MypyFile", "_fullname": "IPython.core.debugger", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BdbQuit_IPython_excepthook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "et", "ev", "tb", "tb_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.BdbQuit_IPython_excepthook", "name": "BdbQuit_IPython_excepthook", "type": null}}, "BdbQuit_excepthook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["et", "ev", "tb", "excepthook"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.BdbQuit_excepthook", "name": "BdbQuit_excepthook", "type": null}}, "CHAIN_EXCEPTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.CHAIN_EXCEPTIONS", "name": "CHAIN_EXCEPTIONS", "type": "builtins.bool"}}, "DEBUGGERSKIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.DEBUGGERSKIP", "name": "DEBUGGERSKIP", "type": "builtins.str"}}, "InterruptiblePdb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.debugger.Pdb"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.debugger.InterruptiblePdb", "name": "InterruptiblePdb", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.InterruptiblePdb", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.debugger", "mro": ["IPython.core.debugger.InterruptiblePdb", "IPython.core.debugger.Pdb", "pdb.Pdb", "bdb.Bdb", "cmd.Cmd", "builtins.object"], "names": {".class": "SymbolTable", "_cmdloop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.InterruptiblePdb._cmdloop", "name": "_cmdloop", "type": null}}, "allow_kbdint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.debugger.InterruptiblePdb.allow_kbdint", "name": "allow_kbdint", "type": "builtins.bool"}}, "cmdloop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "intro"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.InterruptiblePdb.cmdloop", "name": "cmdloop", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.debugger.InterruptiblePdb.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.debugger.InterruptiblePdb", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OldPdb": {".class": "SymbolTableNode", "cross_ref": "pdb.Pdb", "kind": "Gdef"}, "Pdb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pdb.Pdb"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.debugger.Pdb", "name": "Pdb", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.debugger", "mro": ["IPython.core.debugger.Pdb", "pdb.Pdb", "bdb.Bdb", "cmd.Cmd", "builtins.object"], "names": {".class": "SymbolTable", "MAX_CHAINED_EXCEPTION_DEPTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.Pdb.MAX_CHAINED_EXCEPTION_DEPTH", "name": "MAX_CHAINED_EXCEPTION_DEPTH", "type": "builtins.int"}}, "__format_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "tpl_line", "filename", "lineno", "line", "arrow"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.__format_line", "name": "__format_line", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "completekey", "stdin", "stdout", "context", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.__init__", "name": "__init__", "type": null}}, "_chained_exception_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.debugger.Pdb._chained_exception_index", "name": "_chained_exception_index", "type": "builtins.int"}}, "_chained_exceptions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.debugger.Pdb._chained_exceptions", "name": "_chained_exceptions", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_get_frame_locals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb._get_frame_locals", "name": "_get_frame_locals", "type": null}}, "_get_tb_and_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tb_or_exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb._get_tb_and_exceptions", "name": "_get_tb_and_exceptions", "type": null}}, "_hidden_predicate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb._hidden_predicate", "name": "_hidden_predicate", "type": null}}, "_hold_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "IPython.core.debugger.Pdb._hold_exceptions", "name": "_hold_exceptions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.debugger.Pdb._hold_exceptions", "name": "_hold_exceptions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exceptions"], "arg_types": ["IPython.core.debugger.Pdb", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_hold_exceptions of Pdb", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_is_in_decorator_internal_and_should_skip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb._is_in_decorator_internal_and_should_skip", "name": "_is_in_decorator_internal_and_should_skip", "type": null}}, "_predicates": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.debugger.Pdb._predicates", "name": "_predicates", "type": {".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "break_anywhere": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.break_anywhere", "name": "break_anywhere", "type": null}}, "color_scheme_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.debugger.Pdb.color_scheme_table", "name": "color_scheme_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.debugger.Pdb.context", "name": "context", "type": "builtins.int"}}, "default_predicates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.Pdb.default_predicates", "name": "default_predicates", "type": {".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "do_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_context", "name": "do_context", "type": null}}, "do_d": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.Pdb.do_d", "name": "do_d", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "arg_types": ["IPython.core.debugger.Pdb", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_debug", "name": "do_debug", "type": null}}, "do_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_down", "name": "do_down", "type": null}}, "do_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_exceptions", "name": "do_exceptions", "type": null}}, "do_l": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.Pdb.do_l", "name": "do_l", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "arg_types": ["IPython.core.debugger.Pdb", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_list", "name": "do_list", "type": null}}, "do_ll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.Pdb.do_ll", "name": "do_ll", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "arg_types": ["IPython.core.debugger.Pdb", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_longlist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_longlist", "name": "do_longlist", "type": null}}, "do_pdef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_pdef", "name": "do_pdef", "type": null}}, "do_pdoc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_pdoc", "name": "do_pdoc", "type": null}}, "do_pfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_pfile", "name": "do_pfile", "type": null}}, "do_pinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_pinfo", "name": "do_pinfo", "type": null}}, "do_pinfo2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_pinfo2", "name": "do_pinfo2", "type": null}}, "do_psource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_psource", "name": "do_psource", "type": null}}, "do_q": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.Pdb.do_q", "name": "do_q", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "do_quit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.Pdb.do_quit", "name": "do_quit", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "do_skip_hidden": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_skip_hidden", "name": "do_skip_hidden", "type": null}}, "do_skip_predicates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_skip_predicates", "name": "do_skip_predicates", "type": null}}, "do_u": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.Pdb.do_u", "name": "do_u", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "arg_types": ["IPython.core.debugger.Pdb", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_up", "name": "do_up", "type": null}}, "do_w": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.Pdb.do_w", "name": "do_w", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "arg_types": ["IPython.core.debugger.Pdb", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_where": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.do_where", "name": "do_where", "type": null}}, "format_stack_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "frame_lineno", "lprefix", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.format_stack_entry", "name": "format_stack_entry", "type": null}}, "getsourcelines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.getsourcelines", "name": "getsourcelines", "type": null}}, "hidden_frames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stack"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.hidden_frames", "name": "hidden_frames", "type": null}}, "initial_frame": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.debugger.Pdb.initial_frame", "name": "initial_frame", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "interaction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "frame", "tb_or_exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.interaction", "name": "interaction", "type": null}}, "new_do_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.new_do_frame", "name": "new_do_frame", "type": null}}, "new_do_quit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.new_do_quit", "name": "new_do_quit", "type": null}}, "new_do_restart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.new_do_restart", "name": "new_do_restart", "type": null}}, "parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.debugger.Pdb.parser", "name": "parser", "type": "IPython.utils.PyColorize.Parser"}}, "precmd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.precmd", "name": "precmd", "type": null}}, "print_list_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "filename", "first", "last"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.print_list_lines", "name": "print_list_lines", "type": null}}, "print_stack_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "frame_lineno", "prompt_prefix", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.print_stack_entry", "name": "print_stack_entry", "type": null}}, "print_stack_trace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.print_stack_trace", "name": "print_stack_trace", "type": null}}, "report_skipped": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.debugger.Pdb.report_skipped", "name": "report_skipped", "type": "builtins.bool"}}, "set_colors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scheme"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.set_colors", "name": "set_colors", "type": null}}, "set_trace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.set_trace", "name": "set_trace", "type": null}}, "shell": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.debugger.Pdb.shell", "name": "shell", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "skip_hidden": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.debugger.Pdb.skip_hidden", "name": "skip_hidden", "type": "builtins.bool"}}, "stop_here": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.Pdb.stop_here", "name": "stop_here", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.debugger.Pdb.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.debugger.Pdb", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PyColorize": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.PyColorize", "kind": "Gdef"}, "RGX_EXTRA_INDENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.RGX_EXTRA_INDENT", "name": "RGX_EXTRA_INDENT", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.debugger.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.debugger.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.debugger.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.debugger.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.debugger.__package__", "name": "__package__", "type": "builtins.str"}}, "__skip_doctest__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.__skip_doctest__", "name": "__skip_doctest__", "type": "builtins.bool"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.debugger.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "coloransi": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.coloransi", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "decorate_fn_with_doc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["new_fn", "old_fn", "additional_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.decorate_fn_with_doc", "name": "decorate_fn_with_doc", "type": null}}, "exception_colors": {".class": "SymbolTableNode", "cross_ref": "IPython.core.excolors.exception_colors", "kind": "Gdef"}, "get_ipython": {".class": "SymbolTableNode", "cross_ref": "IPython.core.getipython.get_ipython", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "linecache": {".class": "SymbolTableNode", "cross_ref": "linecache", "kind": "Gdef"}, "make_arrow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.make_arrow", "name": "make_arrow", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "prompt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.debugger.prompt", "name": "prompt", "type": "builtins.str"}}, "py3compat": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.py3compat", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "set_trace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["frame", "header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.set_trace", "name": "set_trace", "type": null}}, "strip_indentation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["multiline_string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.debugger.strip_indentation", "name": "strip_indentation", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\debugger.py"}