{"data_mtime": 1753839571, "dep_lines": [14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30], "dependencies": ["starlette._exception_handler", "starlette._utils", "starlette.concurrency", "starlette.convertors", "starlette.datastructures", "starlette.exceptions", "starlette.middleware", "starlette.requests", "starlette.responses", "starlette.types", "starlette.websockets", "__future__", "contextlib", "functools", "inspect", "re", "traceback", "types", "typing", "warnings", "enum", "builtins", "json", "traitlets.utils.warnings", "pprint", "os", "sys", "logging", "_frozen_importlib", "_typeshed", "abc"], "hash": "d1bfb901ddcd992c2080e9f906b6b04abb28f0b7", "id": "starlette.routing", "ignore_all": true, "interface_hash": "9336babfb513219b315ff2100aff5eb3c1d30394", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\starlette\\routing.py", "plugin_data": null, "size": 34569, "suppressed": [], "version_id": "1.15.0"}