{".class": "MypyFile", "_fullname": "prompt_toolkit.eventloop", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "InputHook": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.inputhook.InputHook", "kind": "Gdef"}, "InputHookContext": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.inputhook.InputHookContext", "kind": "Gdef"}, "InputHookSelector": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.inputhook.InputHookSelector", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.eventloop.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.eventloop.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.eventloop.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.eventloop.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.eventloop.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.eventloop.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.eventloop.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.eventloop.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aclosing": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.async_generator.aclosing", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "call_soon_threadsafe": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.utils.call_soon_threadsafe", "kind": "Gdef"}, "generator_to_async_generator": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.async_generator.generator_to_async_generator", "kind": "Gdef"}, "get_traceback_from_context": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.utils.get_traceback_from_context", "kind": "Gdef"}, "new_eventloop_with_inputhook": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.inputhook.new_eventloop_with_inputhook", "kind": "Gdef"}, "run_in_executor_with_context": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.utils.run_in_executor_with_context", "kind": "Gdef"}, "set_eventloop_with_inputhook": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.inputhook.set_eventloop_with_inputhook", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\eventloop\\__init__.py"}