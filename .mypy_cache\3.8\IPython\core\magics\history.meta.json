{"data_mtime": 1753839580, "dep_lines": [22, 23, 24, 26, 27, 27, 16, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 20, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.error", "IPython.core.magic", "IPython.core.magic_arguments", "IPython.testing.skipdoctest", "IPython.utils.io", "IPython.utils", "os", "sys", "io", "fnmatch", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "typing", "functools", "logging", "IPython.testing", "_frozen_importlib", "_typeshed", "abc", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "typing_extensions"], "hash": "9af27a23c156fbc67ca9dd860299105e06ee203b", "id": "IPython.core.magics.history", "ignore_all": true, "interface_hash": "59fba13c6a75cfb358944821ea6810febcc1a8f3", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\magics\\history.py", "plugin_data": null, "size": 12628, "suppressed": [], "version_id": "1.15.0"}