{".class": "MypyFile", "_fullname": "numpy.linalg", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "LinAlgError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.linalg.LinAlgError", "name": "LinAlgError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.linalg.LinAlgError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.linalg", "mro": ["numpy.linalg.LinAlgError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.linalg.LinAlgError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.linalg.LinAlgError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestTester": {".class": "SymbolTableNode", "cross_ref": "numpy._pytesttester.PytestTester", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cholesky": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.cholesky", "kind": "Gdef", "module_public": false}, "cond": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.cond", "kind": "Gdef", "module_public": false}, "det": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.det", "kind": "Gdef", "module_public": false}, "eig": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.eig", "kind": "Gdef", "module_public": false}, "eigh": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.eigh", "kind": "Gdef", "module_public": false}, "eigvals": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.eigvals", "kind": "Gdef", "module_public": false}, "eigvalsh": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.eig<PERSON>sh", "kind": "Gdef", "module_public": false}, "inv": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.inv", "kind": "Gdef", "module_public": false}, "lstsq": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.lstsq", "kind": "Gdef", "module_public": false}, "matrix_power": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.matrix_power", "kind": "Gdef", "module_public": false}, "matrix_rank": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.matrix_rank", "kind": "Gdef", "module_public": false}, "multi_dot": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.multi_dot", "kind": "Gdef", "module_public": false}, "norm": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.norm", "kind": "Gdef", "module_public": false}, "pinv": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.pinv", "kind": "Gdef", "module_public": false}, "qr": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.qr", "kind": "Gdef", "module_public": false}, "slogdet": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.slogdet", "kind": "Gdef", "module_public": false}, "solve": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.solve", "kind": "Gdef", "module_public": false}, "svd": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.svd", "kind": "Gdef", "module_public": false}, "tensorinv": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.tensorinv", "kind": "Gdef", "module_public": false}, "tensorsolve": {".class": "SymbolTableNode", "cross_ref": "numpy.linalg.linalg.tensorsolve", "kind": "Gdef", "module_public": false}, "test": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.linalg.test", "name": "test", "type": "numpy._pytesttester.PytestTester"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\numpy\\linalg\\__init__.pyi"}