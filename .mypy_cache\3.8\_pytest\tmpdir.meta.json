{"data_mtime": 1753839573, "dep_lines": [27, 18, 23, 24, 28, 29, 31, 32, 33, 34, 4, 6, 7, 8, 9, 10, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 10, 5, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.pathlib", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.monkeypatch", "_pytest.nodes", "_pytest.reports", "_pytest.stash", "__future__", "dataclasses", "os", "pathlib", "re", "shutil", "tempfile", "typing", "builtins", "json", "traitlets.utils.warnings", "pprint", "functools", "sys", "logging", "_frozen_importlib", "_typeshed", "abc", "enum", "pluggy", "pluggy._hooks", "typing_extensions"], "hash": "b0a26cac781690f2f75be22c64f29c0de3ecc29b", "id": "_pytest.tmpdir", "ignore_all": true, "interface_hash": "12f0a8994229d89fbc6d9b0a9afbaf5a2f6c7842", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\tmpdir.py", "plugin_data": null, "size": 11375, "suppressed": [], "version_id": "1.15.0"}