{".class": "MypyFile", "_fullname": "prompt_toolkit.shortcuts.prompt", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AfterInput": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.AfterInput", "kind": "Gdef", "module_public": false}, "AnyCursorShapeConfig": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.cursor_shapes.AnyCursorShapeConfig", "kind": "Gdef", "module_public": false}, "AnyFormattedText": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText", "kind": "Gdef", "module_public": false}, "AppendAutoSuggestion": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.AppendAutoSuggestion", "kind": "Gdef", "module_public": false}, "Application": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.application.Application", "kind": "Gdef", "module_public": false}, "AutoSuggest": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.auto_suggest.AutoSuggest", "kind": "Gdef", "module_public": false}, "BaseStyle": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.BaseStyle", "kind": "Gdef", "module_public": false}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.buffer.Buffer", "kind": "Gdef", "module_public": false}, "BufferControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.BufferControl", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Clipboard": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.clipboard.base.Clipboard", "kind": "Gdef", "module_public": false}, "ColorDepth": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.output.color_depth.ColorDepth", "kind": "Gdef", "module_public": false}, "CompleteStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.prompt.CompleteStyle", "name": "CompleteStyle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "prompt_toolkit.shortcuts.prompt.CompleteStyle", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.prompt", "mro": ["prompt_toolkit.shortcuts.prompt.CompleteStyle", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "COLUMN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.prompt.CompleteStyle.COLUMN", "name": "COLUMN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "COLUMN"}, "type_ref": "builtins.str"}}}, "MULTI_COLUMN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.prompt.CompleteStyle.MULTI_COLUMN", "name": "MULTI_COLUMN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "MULTI_COLUMN"}, "type_ref": "builtins.str"}}}, "READLINE_LIKE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.prompt.CompleteStyle.READLINE_LIKE", "name": "READLINE_LIKE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "READLINE_LIKE"}, "type_ref": "builtins.str"}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.shortcuts.prompt.CompleteStyle.value", "name": "value", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt.CompleteStyle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.prompt.CompleteStyle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Completer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.Completer", "kind": "Gdef", "module_public": false}, "CompletionsMenu": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.menus.CompletionsMenu", "kind": "Gdef", "module_public": false}, "Condition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.Condition", "kind": "Gdef", "module_public": false}, "ConditionalContainer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.ConditionalContainer", "kind": "Gdef", "module_public": false}, "ConditionalKeyBindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings", "kind": "Gdef", "module_public": false}, "ConditionalProcessor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.ConditionalProcessor", "kind": "Gdef", "module_public": false}, "ConditionalStyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation", "kind": "Gdef", "module_public": false}, "CursorShapeConfig": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.cursor_shapes.CursorShapeConfig", "kind": "Gdef", "module_public": false}, "DEFAULT_BUFFER": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.enums.DEFAULT_BUFFER", "kind": "Gdef", "module_public": false}, "Dimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.Dimension", "kind": "Gdef", "module_public": false}, "DisplayMultipleCursors": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.DisplayMultipleCursors", "kind": "Gdef", "module_public": false}, "Document": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.document.Document", "kind": "Gdef", "module_public": false}, "DummyOutput": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.output.base.DummyOutput", "kind": "Gdef", "module_public": false}, "DynamicAutoSuggest": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.auto_suggest.DynamicAutoSuggest", "kind": "Gdef", "module_public": false}, "DynamicClipboard": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.clipboard.base.DynamicClipboard", "kind": "Gdef", "module_public": false}, "DynamicCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.DynamicCompleter", "kind": "Gdef", "module_public": false}, "DynamicCursorShapeConfig": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.cursor_shapes.DynamicCursorShapeConfig", "kind": "Gdef", "module_public": false}, "DynamicKeyBindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings", "kind": "Gdef", "module_public": false}, "DynamicLexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.base.DynamicLexer", "kind": "Gdef", "module_public": false}, "DynamicProcessor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.DynamicProcessor", "kind": "Gdef", "module_public": false}, "DynamicStyle": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.DynamicStyle", "kind": "Gdef", "module_public": false}, "DynamicStyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.DynamicStyleTransformation", "kind": "Gdef", "module_public": false}, "DynamicValidator": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.validation.DynamicValidator", "kind": "Gdef", "module_public": false}, "E": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "prompt_toolkit.shortcuts.prompt.E", "line": 144, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "prompt_toolkit.key_binding.key_processor.KeyPressEvent"}}, "EditingMode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.enums.EditingMode", "kind": "Gdef", "module_public": false}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_public": false}, "FilterOrBool": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.FilterOrBool", "kind": "Gdef", "module_public": false}, "Float": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.Float", "kind": "Gdef", "module_public": false}, "FloatContainer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.FloatContainer", "kind": "Gdef", "module_public": false}, "FormattedTextControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.FormattedTextControl", "kind": "Gdef", "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_public": false}, "HSplit": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.HSplit", "kind": "Gdef", "module_public": false}, "HighlightIncrementalSearchProcessor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.HighlightIncrementalSearchProcessor", "kind": "Gdef", "module_public": false}, "HighlightSelectionProcessor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.HighlightSelectionProcessor", "kind": "Gdef", "module_public": false}, "History": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.history.History", "kind": "Gdef", "module_public": false}, "InMemoryClipboard": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.clipboard.in_memory.InMemoryClipboard", "kind": "Gdef", "module_public": false}, "InMemoryHistory": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.history.InMemoryHistory", "kind": "Gdef", "module_public": false}, "Input": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.input.base.Input", "kind": "Gdef", "module_public": false}, "InputHook": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.inputhook.InputHook", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "KeyBindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "kind": "Gdef", "module_public": false}, "KeyBindingsBase": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "kind": "Gdef", "module_public": false}, "KeyPressEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_processor.KeyPressEvent", "kind": "Gdef", "module_public": false}, "Keys": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.keys.Keys", "kind": "Gdef", "module_public": false}, "Layout": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.layout.Layout", "kind": "Gdef", "module_public": false}, "Lexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.base.Lexer", "kind": "Gdef", "module_public": false}, "MagicFormattedText": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.MagicFormattedText", "kind": "Gdef", "module_public": false}, "MultiColumnCompletionsMenu": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.menus.MultiColumnCompletionsMenu", "kind": "Gdef", "module_public": false}, "Output": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.output.base.Output", "kind": "Gdef", "module_public": false}, "PasswordProcessor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.PasswordProcessor", "kind": "Gdef", "module_public": false}, "Processor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.Processor", "kind": "Gdef", "module_public": false}, "PromptContinuationText": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "prompt_toolkit.shortcuts.prompt.PromptContinuationText", "line": 214, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "prompt_toolkit.formatted_text.base.MagicFormattedText", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}}}, "PromptSession": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession", "name": "PromptSession", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.shortcuts.prompt", "mro": ["prompt_toolkit.shortcuts.prompt.PromptSession", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "message", "multiline", "wrap_lines", "is_password", "vi_mode", "editing_mode", "complete_while_typing", "validate_while_typing", "enable_history_search", "search_ignore_case", "lexer", "enable_system_prompt", "enable_suspend", "enable_open_in_editor", "validator", "completer", "complete_in_thread", "reserve_space_for_menu", "complete_style", "auto_suggest", "style", "style_transformation", "swap_light_and_dark_colors", "color_depth", "cursor", "include_default_pygments_style", "history", "clipboard", "prompt_continuation", "rprompt", "bottom_toolbar", "mouse_support", "input_processors", "placeholder", "key_bindings", "erase_when_done", "tempfile_suffix", "tempfile", "refresh_interval", "input", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "message", "multiline", "wrap_lines", "is_password", "vi_mode", "editing_mode", "complete_while_typing", "validate_while_typing", "enable_history_search", "search_ignore_case", "lexer", "enable_system_prompt", "enable_suspend", "enable_open_in_editor", "validator", "completer", "complete_in_thread", "reserve_space_for_menu", "complete_style", "auto_suggest", "style", "style_transformation", "swap_light_and_dark_colors", "color_depth", "cursor", "include_default_pygments_style", "history", "clipboard", "prompt_continuation", "rprompt", "bottom_toolbar", "mouse_support", "input_processors", "placeholder", "key_bindings", "erase_when_done", "tempfile_suffix", "tempfile", "refresh_interval", "input", "output"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, "builtins.bool", "prompt_toolkit.enums.EditingMode", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": ["prompt_toolkit.lexers.base.Lexer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": ["prompt_toolkit.validation.Validator", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.completion.base.Completer", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.int", "prompt_toolkit.shortcuts.prompt.CompleteStyle", {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.AutoSuggest", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.styles.style_transformation.StyleTransformation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": ["prompt_toolkit.output.color_depth.ColorDepth", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.cursor_shapes.AnyCursorShapeConfig"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": ["prompt_toolkit.history.History", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.clipboard.base.Clipboard", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.shortcuts.prompt.PromptContinuationText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": ["prompt_toolkit.input.base.Input", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.output.base.Output", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PromptSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_pre_run_callables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pre_run", "accept_default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._add_pre_run_callables", "name": "_add_pre_run_callables", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pre_run", "accept_default"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_pre_run_callables of PromptSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_application": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "editing_mode", "erase_when_done"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._create_application", "name": "_create_application", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "editing_mode", "erase_when_done"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, "prompt_toolkit.enums.EditingMode", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_application of PromptSession", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_default_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._create_default_buffer", "name": "_create_default_buffer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_default_buffer of PromptSession", "ret_type": "prompt_toolkit.buffer.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._create_layout", "name": "_create_layout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_layout of PromptSession", "ret_type": "prompt_toolkit.layout.layout.Layout", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_prompt_bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._create_prompt_bindings", "name": "_create_prompt_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_prompt_bindings of PromptSession", "ret_type": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_search_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._create_search_buffer", "name": "_create_search_buffer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_search_buffer of PromptSession", "ret_type": "prompt_toolkit.buffer.Buffer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dumb_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._dumb_prompt", "name": "_dumb_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "message"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dumb_prompt of PromptSession", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._dumb_prompt", "name": "_dumb_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "message"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dumb_prompt of PromptSession", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_dyncond": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._dyncond", "name": "_dyncond", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr_name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dyncond of PromptSession", "ret_type": "prompt_toolkit.filters.base.Condition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_get_arg_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._get_arg_text", "name": "_get_arg_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_arg_text of PromptSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_continuation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "width", "line_number", "wrap_count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._get_continuation", "name": "_get_continuation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "width", "line_number", "wrap_count"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_continuation of PromptSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_default_buffer_control_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._get_default_buffer_control_height", "name": "_get_default_buffer_control_height", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_default_buffer_control_height of PromptSession", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_line_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "line_number", "wrap_count", "get_prompt_text_2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._get_line_prefix", "name": "_get_line_prefix", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "line_number", "wrap_count", "get_prompt_text_2"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, "builtins.int", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.shortcuts.prompt._StyleAndTextTuplesCallable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_line_prefix of PromptSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._get_prompt", "name": "_get_prompt", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_prompt of PromptSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_inline_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._inline_arg", "name": "_inline_arg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inline_arg of PromptSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_input": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._input", "name": "_input", "type": {".class": "UnionType", "items": ["prompt_toolkit.input.base.Input", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession._output", "name": "_output", "type": {".class": "UnionType", "items": ["prompt_toolkit.output.base.Output", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.app", "name": "app", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}}}, "auto_suggest": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.auto_suggest", "name": "auto_suggest", "type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.AutoSuggest", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "bottom_toolbar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.bottom_toolbar", "name": "bottom_toolbar", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}}}, "clipboard": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.clipboard", "name": "clipboard", "type": "prompt_toolkit.clipboard.base.Clipboard"}}, "color_depth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.color_depth", "name": "color_depth", "type": {".class": "UnionType", "items": ["prompt_toolkit.output.color_depth.ColorDepth", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "complete_in_thread": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.complete_in_thread", "name": "complete_in_thread", "type": "builtins.bool"}}, "complete_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.complete_style", "name": "complete_style", "type": "prompt_toolkit.shortcuts.prompt.CompleteStyle"}}, "complete_while_typing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.complete_while_typing", "name": "complete_while_typing", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "completer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.completer", "name": "completer", "type": {".class": "UnionType", "items": ["prompt_toolkit.completion.base.Completer", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "cursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.cursor", "name": "cursor", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.cursor_shapes.AnyCursorShapeConfig"}}}, "default_buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.default_buffer", "name": "default_buffer", "type": "prompt_toolkit.buffer.Buffer"}}, "editing_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.editing_mode", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.editing_mode", "name": "editing_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "editing_mode of PromptSession", "ret_type": "prompt_toolkit.enums.EditingMode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.editing_mode", "name": "editing_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "editing_mode of PromptSession", "ret_type": "prompt_toolkit.enums.EditingMode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.editing_mode", "name": "editing_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, "prompt_toolkit.enums.EditingMode"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "editing_mode of PromptSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "editing_mode", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "editing_mode of PromptSession", "ret_type": "prompt_toolkit.enums.EditingMode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "enable_history_search": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.enable_history_search", "name": "enable_history_search", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "enable_open_in_editor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.enable_open_in_editor", "name": "enable_open_in_editor", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "enable_suspend": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.enable_suspend", "name": "enable_suspend", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "enable_system_prompt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.enable_system_prompt", "name": "enable_system_prompt", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "history": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.history", "name": "history", "type": "prompt_toolkit.history.History"}}, "include_default_pygments_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.include_default_pygments_style", "name": "include_default_pygments_style", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.input", "name": "input", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "input of PromptSession", "ret_type": "prompt_toolkit.input.base.Input", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.input", "name": "input", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "input of PromptSession", "ret_type": "prompt_toolkit.input.base.Input", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "input_processors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.input_processors", "name": "input_processors", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "is_password": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.is_password", "name": "is_password", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "key_bindings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.key_bindings", "name": "key_bindings", "type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "layout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.layout", "name": "layout", "type": "prompt_toolkit.layout.layout.Layout"}}, "lexer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.lexer", "name": "lexer", "type": {".class": "UnionType", "items": ["prompt_toolkit.lexers.base.Lexer", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.message", "name": "message", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}}}, "mouse_support": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.mouse_support", "name": "mouse_support", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "multiline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.multiline", "name": "multiline", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.output", "name": "output", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "output of PromptSession", "ret_type": "prompt_toolkit.output.base.Output", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.output", "name": "output", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "output of PromptSession", "ret_type": "prompt_toolkit.output.base.Output", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "placeholder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.placeholder", "name": "placeholder", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "message", "editing_mode", "refresh_interval", "vi_mode", "lexer", "completer", "complete_in_thread", "is_password", "key_bindings", "bottom_toolbar", "style", "color_depth", "cursor", "include_default_pygments_style", "style_transformation", "swap_light_and_dark_colors", "rprompt", "multiline", "prompt_continuation", "wrap_lines", "enable_history_search", "search_ignore_case", "complete_while_typing", "validate_while_typing", "complete_style", "auto_suggest", "validator", "clipboard", "mouse_support", "input_processors", "placeholder", "reserve_space_for_menu", "enable_system_prompt", "enable_suspend", "enable_open_in_editor", "tempfile_suffix", "tempfile", "default", "accept_default", "pre_run", "set_exception_handler", "handle_sigint", "in_thread", "inputhook"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.prompt", "name": "prompt", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "message", "editing_mode", "refresh_interval", "vi_mode", "lexer", "completer", "complete_in_thread", "is_password", "key_bindings", "bottom_toolbar", "style", "color_depth", "cursor", "include_default_pygments_style", "style_transformation", "swap_light_and_dark_colors", "rprompt", "multiline", "prompt_continuation", "wrap_lines", "enable_history_search", "search_ignore_case", "complete_while_typing", "validate_while_typing", "complete_style", "auto_suggest", "validator", "clipboard", "mouse_support", "input_processors", "placeholder", "reserve_space_for_menu", "enable_system_prompt", "enable_suspend", "enable_open_in_editor", "tempfile_suffix", "tempfile", "default", "accept_default", "pre_run", "set_exception_handler", "handle_sigint", "in_thread", "inputhook"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.enums.EditingMode", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.lexers.base.Lexer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.completion.base.Completer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.output.color_depth.ColorDepth", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.cursor_shapes.AnyCursorShapeConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.styles.style_transformation.StyleTransformation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.shortcuts.prompt.PromptContinuationText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.shortcuts.prompt.CompleteStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.AutoSuggest", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.validation.Validator", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.clipboard.base.Clipboard", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "prompt_toolkit.document.Document"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.eventloop.inputhook.InputHook"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prompt of PromptSession", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prompt_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "message", "editing_mode", "refresh_interval", "vi_mode", "lexer", "completer", "complete_in_thread", "is_password", "key_bindings", "bottom_toolbar", "style", "color_depth", "cursor", "include_default_pygments_style", "style_transformation", "swap_light_and_dark_colors", "rprompt", "multiline", "prompt_continuation", "wrap_lines", "enable_history_search", "search_ignore_case", "complete_while_typing", "validate_while_typing", "complete_style", "auto_suggest", "validator", "clipboard", "mouse_support", "input_processors", "placeholder", "reserve_space_for_menu", "enable_system_prompt", "enable_suspend", "enable_open_in_editor", "tempfile_suffix", "tempfile", "default", "accept_default", "pre_run", "set_exception_handler", "handle_sigint"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.prompt_async", "name": "prompt_async", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "message", "editing_mode", "refresh_interval", "vi_mode", "lexer", "completer", "complete_in_thread", "is_password", "key_bindings", "bottom_toolbar", "style", "color_depth", "cursor", "include_default_pygments_style", "style_transformation", "swap_light_and_dark_colors", "rprompt", "multiline", "prompt_continuation", "wrap_lines", "enable_history_search", "search_ignore_case", "complete_while_typing", "validate_while_typing", "complete_style", "auto_suggest", "validator", "clipboard", "mouse_support", "input_processors", "placeholder", "reserve_space_for_menu", "enable_system_prompt", "enable_suspend", "enable_open_in_editor", "tempfile_suffix", "tempfile", "default", "accept_default", "pre_run", "set_exception_handler", "handle_sigint"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.enums.EditingMode", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.lexers.base.Lexer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.completion.base.Completer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.output.color_depth.ColorDepth", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.cursor_shapes.CursorShapeConfig", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.styles.style_transformation.StyleTransformation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.shortcuts.prompt.PromptContinuationText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.shortcuts.prompt.CompleteStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.AutoSuggest", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.validation.Validator", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.clipboard.base.Clipboard", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "prompt_toolkit.document.Document"], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prompt_async of PromptSession", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prompt_continuation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.prompt_continuation", "name": "prompt_continuation", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.shortcuts.prompt.PromptContinuationText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "refresh_interval": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.refresh_interval", "name": "refresh_interval", "type": "builtins.float"}}, "reserve_space_for_menu": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.reserve_space_for_menu", "name": "reserve_space_for_menu", "type": "builtins.int"}}, "rprompt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.rprompt", "name": "rprompt", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}}}, "search_buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.search_buffer", "name": "search_buffer", "type": "prompt_toolkit.buffer.Buffer"}}, "search_ignore_case": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.search_ignore_case", "name": "search_ignore_case", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.style", "name": "style", "type": {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "style_transformation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.style_transformation", "name": "style_transformation", "type": {".class": "UnionType", "items": ["prompt_toolkit.styles.style_transformation.StyleTransformation", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "swap_light_and_dark_colors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.swap_light_and_dark_colors", "name": "swap_light_and_dark_colors", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "tempfile": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.tempfile", "name": "tempfile", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "tempfile_suffix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.tempfile_suffix", "name": "tempfile_suffix", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "validate_while_typing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.validate_while_typing", "name": "validate_while_typing", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}, "validator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.validator", "name": "validator", "type": {".class": "UnionType", "items": ["prompt_toolkit.validation.Validator", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "wrap_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.wrap_lines", "name": "wrap_lines", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt.PromptSession.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "id": 1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.prompt.PromptSession", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "ReverseSearchProcessor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.ReverseSearchProcessor", "kind": "Gdef", "module_public": false}, "SEARCH_BUFFER": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.enums.SEARCH_BUFFER", "kind": "Gdef", "module_public": false}, "SearchBufferControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.SearchBufferControl", "kind": "Gdef", "module_public": false}, "SearchToolbar": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.toolbars.SearchToolbar", "kind": "Gdef", "module_public": false}, "StyleAndTextTuples": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples", "kind": "Gdef", "module_public": false}, "StyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.StyleTransformation", "kind": "Gdef", "module_public": false}, "SwapLightAndDarkStyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.SwapLightAndDarkStyleTransformation", "kind": "Gdef", "module_public": false}, "SystemToolbar": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.toolbars.SystemToolbar", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "ThreadedCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.ThreadedCompleter", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "ValidationToolbar": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.toolbars.ValidationToolbar", "kind": "Gdef", "module_public": false}, "Validator": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.validation.Validator", "kind": "Gdef", "module_public": false}, "Window": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.Window", "kind": "Gdef", "module_public": false}, "WindowAlign": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.WindowAlign", "kind": "Gdef", "module_public": false}, "_RPrompt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.containers.Window"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.prompt._RPrompt", "name": "_RPrompt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt._RPrompt", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.prompt", "mro": ["prompt_toolkit.shortcuts.prompt._RPrompt", "prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.containers.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt._RPrompt.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["prompt_toolkit.shortcuts.prompt._RPrompt", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _RPrompt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._RPrompt.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.prompt._RPrompt", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_StyleAndTextTuplesCallable": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "prompt_toolkit.shortcuts.prompt._StyleAndTextTuplesCallable", "line": 143, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.prompt._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.prompt.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.prompt.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.prompt.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.prompt.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.prompt.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.prompt.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.prompt.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_split_multiline_prompt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["get_prompt_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt._split_multiline_prompt", "name": "_split_multiline_prompt", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["get_prompt_text"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.shortcuts.prompt._StyleAndTextTuplesCallable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_split_multiline_prompt", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.shortcuts.prompt._StyleAndTextTuplesCallable"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.shortcuts.prompt._StyleAndTextTuplesCallable"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "confirm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["message", "suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.confirm", "name": "confirm", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["message", "suffix"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "confirm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef", "module_public": false}, "create_confirm_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["message", "suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.create_confirm_session", "name": "create_confirm_session", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["message", "suffix"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_confirm_session", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "display_completions_like_readline": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.completion.display_completions_like_readline", "kind": "Gdef", "module_public": false}, "explode_text_fragments": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.utils.explode_text_fragments", "kind": "Gdef", "module_public": false}, "fragment_list_to_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.utils.fragment_list_to_text", "kind": "Gdef", "module_public": false}, "get_app": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app", "kind": "Gdef", "module_public": false}, "get_cwidth": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.get_cwidth", "kind": "Gdef", "module_public": false}, "get_running_loop": {".class": "SymbolTableNode", "cross_ref": "_asyncio.get_running_loop", "kind": "Gdef", "module_public": false}, "has_arg": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.has_arg", "kind": "Gdef", "module_public": false}, "has_focus": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.has_focus", "kind": "Gdef", "module_public": false}, "is_done": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.is_done", "kind": "Gdef", "module_public": false}, "is_dumb_terminal": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.is_dumb_terminal", "kind": "Gdef", "module_public": false}, "is_true": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.is_true", "kind": "Gdef", "module_public": false}, "load_auto_suggest_bindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.auto_suggest.load_auto_suggest_bindings", "kind": "Gdef", "module_public": false}, "load_open_in_editor_bindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.open_in_editor.load_open_in_editor_bindings", "kind": "Gdef", "module_public": false}, "merge_formatted_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.merge_formatted_text", "kind": "Gdef", "module_public": false}, "merge_key_bindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.merge_key_bindings", "kind": "Gdef", "module_public": false}, "merge_processors": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.merge_processors", "kind": "Gdef", "module_public": false}, "merge_style_transformations": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.merge_style_transformations", "kind": "Gdef", "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_public": false}, "prompt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["message", "history", "editing_mode", "refresh_interval", "vi_mode", "lexer", "completer", "complete_in_thread", "is_password", "key_bindings", "bottom_toolbar", "style", "color_depth", "cursor", "include_default_pygments_style", "style_transformation", "swap_light_and_dark_colors", "rprompt", "multiline", "prompt_continuation", "wrap_lines", "enable_history_search", "search_ignore_case", "complete_while_typing", "validate_while_typing", "complete_style", "auto_suggest", "validator", "clipboard", "mouse_support", "input_processors", "placeholder", "reserve_space_for_menu", "enable_system_prompt", "enable_suspend", "enable_open_in_editor", "tempfile_suffix", "tempfile", "default", "accept_default", "pre_run", "set_exception_handler", "handle_sigint", "in_thread", "inputhook"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.prompt.prompt", "name": "prompt", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["message", "history", "editing_mode", "refresh_interval", "vi_mode", "lexer", "completer", "complete_in_thread", "is_password", "key_bindings", "bottom_toolbar", "style", "color_depth", "cursor", "include_default_pygments_style", "style_transformation", "swap_light_and_dark_colors", "rprompt", "multiline", "prompt_continuation", "wrap_lines", "enable_history_search", "search_ignore_case", "complete_while_typing", "validate_while_typing", "complete_style", "auto_suggest", "validator", "clipboard", "mouse_support", "input_processors", "placeholder", "reserve_space_for_menu", "enable_system_prompt", "enable_suspend", "enable_open_in_editor", "tempfile_suffix", "tempfile", "default", "accept_default", "pre_run", "set_exception_handler", "handle_sigint", "in_thread", "inputhook"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.history.History", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.enums.EditingMode", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.lexers.base.Lexer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.completion.base.Completer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.output.color_depth.ColorDepth", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.cursor_shapes.AnyCursorShapeConfig"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.styles.style_transformation.StyleTransformation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.shortcuts.prompt.PromptContinuationText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.shortcuts.prompt.CompleteStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.AutoSuggest", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.validation.Validator", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.clipboard.base.Clipboard", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.eventloop.inputhook.InputHook"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prompt", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "renderer_height_is_known": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.renderer_height_is_known", "kind": "Gdef", "module_public": false}, "suspend_to_background_supported": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.suspend_to_background_supported", "kind": "Gdef", "module_public": false}, "to_filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.to_filter", "kind": "Gdef", "module_public": false}, "to_formatted_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.to_formatted_text", "kind": "Gdef", "module_public": false}, "to_str": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.to_str", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\shortcuts\\prompt.py"}