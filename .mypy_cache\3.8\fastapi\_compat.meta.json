{"data_mtime": 1753839572, "dep_lines": [54, 57, 58, 291, 21, 22, 24, 25, 59, 60, 71, 297, 300, 303, 327, 336, 339, 1, 2, 3, 4, 5, 6, 23, 26, 62, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._schema_generation_shared", "pydantic._internal._typing_extra", "pydantic._internal._utils", "fastapi.openapi.constants", "fastapi.exceptions", "fastapi.types", "pydantic.version", "starlette.datastructures", "pydantic.fields", "pydantic.json_schema", "pydantic_core.core_schema", "pydantic.class_validators", "pydantic.error_wrappers", "pydantic.errors", "pydantic.schema", "pydantic.typing", "pydantic.utils", "collections", "copy", "dataclasses", "enum", "functools", "typing", "pydantic", "typing_extensions", "pydantic_core", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "os", "sys", "logging", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.annotated_handlers", "pydantic.config", "pydantic.deprecated", "pydantic.deprecated.config", "pydantic.main", "pydantic.networks", "pydantic.type_adapter", "pydantic_core._pydantic_core"], "hash": "8f2a155b7a57a767235d042b3134169d63b871d7", "id": "fastapi._compat", "ignore_all": true, "interface_hash": "ebf662b89c3ea6a6059643d3e7bf7d036cf218ff", "mtime": 1750471027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\fastapi\\_compat.py", "plugin_data": null, "size": 23953, "suppressed": [], "version_id": "1.15.0"}