{"data_mtime": 1753876885, "dep_lines": [3, 3, 3, 1, 3, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 20, 5, 30, 30], "dependencies": ["requests.cookies", "requests.models", "requests.utils", "typing", "requests", "builtins", "_frozen_importlib", "abc"], "hash": "8ba61b259810686b2f38876c8ecc04d3370d2ad0", "id": "requests.auth", "ignore_all": true, "interface_hash": "5ec587f969fa1d50c4f763ff9e51a3a724470582", "mtime": 1750874647, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\requests-stubs\\auth.pyi", "plugin_data": null, "size": 1191, "suppressed": [], "version_id": "1.15.0"}