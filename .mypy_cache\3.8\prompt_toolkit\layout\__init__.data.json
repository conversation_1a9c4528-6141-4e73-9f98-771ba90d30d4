{".class": "MypyFile", "_fullname": "prompt_toolkit.layout", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnyContainer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.AnyContainer", "kind": "Gdef"}, "AnyDimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.AnyDimension", "kind": "Gdef"}, "BufferControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.BufferControl", "kind": "Gdef"}, "ColorColumn": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.ColorColumn", "kind": "Gdef"}, "CompletionsMenu": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.menus.CompletionsMenu", "kind": "Gdef"}, "ConditionalContainer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.ConditionalContainer", "kind": "Gdef"}, "ConditionalMargin": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.margins.ConditionalMargin", "kind": "Gdef"}, "Container": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.Container", "kind": "Gdef"}, "D": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.D", "kind": "Gdef"}, "Dimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.Dimension", "kind": "Gdef"}, "DummyControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.DummyControl", "kind": "Gdef"}, "DynamicContainer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.DynamicContainer", "kind": "Gdef"}, "Float": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.Float", "kind": "Gdef"}, "FloatContainer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.FloatContainer", "kind": "Gdef"}, "FormattedTextControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.FormattedTextControl", "kind": "Gdef"}, "HSplit": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.HSplit", "kind": "Gdef"}, "HorizontalAlign": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.HorizontalAlign", "kind": "Gdef"}, "InvalidLayoutError": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.layout.InvalidLayoutError", "kind": "Gdef"}, "Layout": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.layout.Layout", "kind": "Gdef"}, "Margin": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.margins.Margin", "kind": "Gdef"}, "MultiColumnCompletionsMenu": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.menus.MultiColumnCompletionsMenu", "kind": "Gdef"}, "NumberedMargin": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.margins.NumberedMargin", "kind": "Gdef"}, "PromptMargin": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.margins.PromptMargin", "kind": "Gdef"}, "ScrollOffsets": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.ScrollOffsets", "kind": "Gdef"}, "ScrollablePane": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.scrollable_pane.ScrollablePane", "kind": "Gdef"}, "ScrollbarMargin": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.margins.ScrollbarMargin", "kind": "Gdef"}, "SearchBufferControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.SearchBufferControl", "kind": "Gdef"}, "UIContent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.UIContent", "kind": "Gdef"}, "UIControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.UIControl", "kind": "Gdef"}, "VSplit": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.VSplit", "kind": "Gdef"}, "VerticalAlign": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.VerticalAlign", "kind": "Gdef"}, "Window": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.Window", "kind": "Gdef"}, "WindowAlign": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.WindowAlign", "kind": "Gdef"}, "WindowRenderInfo": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.WindowRenderInfo", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "is_container": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.is_container", "kind": "Gdef"}, "is_dimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.is_dimension", "kind": "Gdef"}, "max_layout_dimensions": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.max_layout_dimensions", "kind": "Gdef"}, "sum_layout_dimensions": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.sum_layout_dimensions", "kind": "Gdef"}, "to_container": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.to_container", "kind": "Gdef"}, "to_dimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.to_dimension", "kind": "Gdef"}, "to_window": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.to_window", "kind": "Gdef"}, "walk": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.layout.walk", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\layout\\__init__.py"}