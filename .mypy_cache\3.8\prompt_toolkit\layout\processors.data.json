{".class": "MypyFile", "_fullname": "prompt_toolkit.layout.processors", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_public": false}, "AfterInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.AfterInput", "name": "AfterInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.AfterInput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.AfterInput", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.AfterInput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "style"], "arg_types": ["prompt_toolkit.layout.processors.AfterInput", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AfterInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.AfterInput.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.processors.AfterInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of AfterInput", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.AfterInput.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "arg_types": ["prompt_toolkit.layout.processors.AfterInput", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of AfterInput", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.AfterInput.style", "name": "style", "type": "builtins.str"}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.AfterInput.text", "name": "text", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.AfterInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.AfterInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AnyFormattedText": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText", "kind": "Gdef", "module_public": false}, "AppendAutoSuggestion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.AppendAutoSuggestion", "name": "AppendAutoSuggestion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.AppendAutoSuggestion", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.AppendAutoSuggestion", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.AppendAutoSuggestion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "style"], "arg_types": ["prompt_toolkit.layout.processors.AppendAutoSuggestion", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AppendAutoSuggestion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.AppendAutoSuggestion.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "arg_types": ["prompt_toolkit.layout.processors.AppendAutoSuggestion", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of AppendAutoSuggestion", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.AppendAutoSuggestion.style", "name": "style", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.AppendAutoSuggestion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.AppendAutoSuggestion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BeforeInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.BeforeInput", "name": "BeforeInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.BeforeInput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.BeforeInput", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.BeforeInput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "style"], "arg_types": ["prompt_toolkit.layout.processors.BeforeInput", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BeforeInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.BeforeInput.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.processors.BeforeInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of BeforeInput", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.BeforeInput.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "arg_types": ["prompt_toolkit.layout.processors.BeforeInput", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of BeforeInput", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.BeforeInput.style", "name": "style", "type": "builtins.str"}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.BeforeInput.text", "name": "text", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.BeforeInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.BeforeInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BufferControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.BufferControl", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ConditionalProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.ConditionalProcessor", "name": "ConditionalProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ConditionalProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.ConditionalProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "processor", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ConditionalProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "processor", "filter"], "arg_types": ["prompt_toolkit.layout.processors.ConditionalProcessor", "prompt_toolkit.layout.processors.Processor", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConditionalProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ConditionalProcessor.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.processors.ConditionalProcessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ConditionalProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ConditionalProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "arg_types": ["prompt_toolkit.layout.processors.ConditionalProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of ConditionalProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.ConditionalProcessor.filter", "name": "filter", "type": "prompt_toolkit.filters.base.Filter"}}, "processor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.ConditionalProcessor.processor", "name": "processor", "type": "prompt_toolkit.layout.processors.Processor"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.ConditionalProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.ConditionalProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DisplayMultipleCursors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.DisplayMultipleCursors", "name": "DisplayMultipleCursors", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.DisplayMultipleCursors", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.DisplayMultipleCursors", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.DisplayMultipleCursors.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "arg_types": ["prompt_toolkit.layout.processors.DisplayMultipleCursors", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of DisplayMultipleCursors", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.DisplayMultipleCursors.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.DisplayMultipleCursors", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DisplayToSource": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "prompt_toolkit.layout.processors.DisplayToSource", "line": 76, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Document": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.document.Document", "kind": "Gdef", "module_public": false}, "DummyProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.DummyProcessor", "name": "DummyProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.DummyProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.DummyProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.DummyProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "arg_types": ["prompt_toolkit.layout.processors.DummyProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of DummyProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.DummyProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.DummyProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DynamicProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.DynamicProcessor", "name": "DynamicProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.DynamicProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.DynamicProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "get_processor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.DynamicProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "get_processor"], "arg_types": ["prompt_toolkit.layout.processors.DynamicProcessor", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.layout.processors.Processor", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DynamicProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.DynamicProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "arg_types": ["prompt_toolkit.layout.processors.DynamicProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of DynamicProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_processor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.DynamicProcessor.get_processor", "name": "get_processor", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.layout.processors.Processor", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.DynamicProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.DynamicProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FilterOrBool": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.FilterOrBool", "kind": "Gdef", "module_public": false}, "Hashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef", "module_public": false}, "HighlightIncrementalSearchProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.HighlightSearchProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.HighlightIncrementalSearchProcessor", "name": "HighlightIncrementalSearchProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.HighlightIncrementalSearchProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.HighlightIncrementalSearchProcessor", "prompt_toolkit.layout.processors.HighlightSearchProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "_classname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.processors.HighlightIncrementalSearchProcessor._classname", "name": "_classname", "type": "builtins.str"}}, "_classname_current": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.processors.HighlightIncrementalSearchProcessor._classname_current", "name": "_classname_current", "type": "builtins.str"}}, "_get_search_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer_control"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.HighlightIncrementalSearchProcessor._get_search_text", "name": "_get_search_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer_control"], "arg_types": ["prompt_toolkit.layout.processors.HighlightIncrementalSearchProcessor", "prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_search_text of HighlightIncrementalSearchProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.HighlightIncrementalSearchProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.HighlightIncrementalSearchProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HighlightMatchingBracketProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor", "name": "HighlightMatchingBracketProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "chars", "max_cursor_distance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "chars", "max_cursor_distance"], "arg_types": ["prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HighlightMatchingBracketProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_closing_braces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor._closing_braces", "name": "_closing_braces", "type": "builtins.str"}}, "_get_positions_to_highlight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor._get_positions_to_highlight", "name": "_get_positions_to_highlight", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document"], "arg_types": ["prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_positions_to_highlight of HighlightMatchingBracketProcessor", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_positions_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor._positions_cache", "name": "_positions_cache", "type": {".class": "Instance", "args": ["<PERSON>.<PERSON>", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "prompt_toolkit.cache.SimpleCache"}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "arg_types": ["prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of HighlightMatchingBracketProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor.chars", "name": "chars", "type": "builtins.str"}}, "max_cursor_distance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor.max_cursor_distance", "name": "max_cursor_distance", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HighlightSearchProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.HighlightSearchProcessor", "name": "HighlightSearchProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.HighlightSearchProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.HighlightSearchProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "_classname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.processors.HighlightSearchProcessor._classname", "name": "_classname", "type": "builtins.str"}}, "_classname_current": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.processors.HighlightSearchProcessor._classname_current", "name": "_classname_current", "type": "builtins.str"}}, "_get_search_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer_control"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.HighlightSearchProcessor._get_search_text", "name": "_get_search_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer_control"], "arg_types": ["prompt_toolkit.layout.processors.HighlightSearchProcessor", "prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_search_text of HighlightSearchProcessor", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.HighlightSearchProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "arg_types": ["prompt_toolkit.layout.processors.HighlightSearchProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of HighlightSearchProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.HighlightSearchProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.HighlightSearchProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HighlightSelectionProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.HighlightSelectionProcessor", "name": "HighlightSelectionProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.HighlightSelectionProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.HighlightSelectionProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.HighlightSelectionProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "arg_types": ["prompt_toolkit.layout.processors.HighlightSelectionProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of HighlightSelectionProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.HighlightSelectionProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.HighlightSelectionProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PasswordProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.PasswordProcessor", "name": "PasswordProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.PasswordProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.PasswordProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "char"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.PasswordProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "char"], "arg_types": ["prompt_toolkit.layout.processors.PasswordProcessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PasswordProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.PasswordProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "arg_types": ["prompt_toolkit.layout.processors.PasswordProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of PasswordProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.PasswordProcessor.char", "name": "char", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.PasswordProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.PasswordProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Processor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["apply_transformation", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.Processor", "name": "Processor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "prompt_toolkit.layout.processors.Processor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "prompt_toolkit.layout.processors.Processor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "arg_types": ["prompt_toolkit.layout.processors.Processor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of Processor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.processors.Processor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transformation_input"], "arg_types": ["prompt_toolkit.layout.processors.Processor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of Processor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.Processor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.Processor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReverseSearchProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.ReverseSearchProcessor", "name": "ReverseSearchProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ReverseSearchProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.ReverseSearchProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "main_control", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ReverseSearchProcessor._content", "name": "_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "main_control", "ti"], "arg_types": ["prompt_toolkit.layout.processors.ReverseSearchProcessor", "prompt_toolkit.layout.controls.BufferControl", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_content of ReverseSearchProcessor", "ret_type": "prompt_toolkit.layout.controls.UIContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_excluded_input_processors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prompt_toolkit.layout.processors.ReverseSearchProcessor._excluded_input_processors", "name": "_excluded_input_processors", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "prompt_toolkit.layout.processors.Processor"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_get_main_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer_control"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ReverseSearchProcessor._get_main_buffer", "name": "_get_main_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer_control"], "arg_types": ["prompt_toolkit.layout.processors.ReverseSearchProcessor", "prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_main_buffer of ReverseSearchProcessor", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.layout.controls.BufferControl", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ReverseSearchProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "arg_types": ["prompt_toolkit.layout.processors.ReverseSearchProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of ReverseSearchProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.ReverseSearchProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.ReverseSearchProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SearchDirection": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.search.SearchDirection", "kind": "Gdef", "module_public": false}, "ShowArg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.BeforeInput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.ShowArg", "name": "ShowArg", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ShowArg", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.ShowArg", "prompt_toolkit.layout.processors.BeforeInput", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ShowArg.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.processors.ShowArg"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ShowArg", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ShowArg.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.processors.ShowArg"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ShowArg", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_text_fragments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ShowArg._get_text_fragments", "name": "_get_text_fragments", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.processors.ShowArg"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_text_fragments of ShowArg", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.ShowArg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.ShowArg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShowLeadingWhiteSpaceProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.ShowLeadingWhiteSpaceProcessor", "name": "ShowLeadingWhiteSpaceProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ShowLeadingWhiteSpaceProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.ShowLeadingWhiteSpaceProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "get_char", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ShowLeadingWhiteSpaceProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "get_char", "style"], "arg_types": ["prompt_toolkit.layout.processors.ShowLeadingWhiteSpaceProcessor", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ShowLeadingWhiteSpaceProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ShowLeadingWhiteSpaceProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "arg_types": ["prompt_toolkit.layout.processors.ShowLeadingWhiteSpaceProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of ShowLeadingWhiteSpaceProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.ShowLeadingWhiteSpaceProcessor.get_char", "name": "get_char", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.ShowLeadingWhiteSpaceProcessor.style", "name": "style", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.ShowLeadingWhiteSpaceProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.ShowLeadingWhiteSpaceProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShowTrailingWhiteSpaceProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.ShowTrailingWhiteSpaceProcessor", "name": "ShowTrailingWhiteSpaceProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ShowTrailingWhiteSpaceProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.ShowTrailingWhiteSpaceProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "get_char", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ShowTrailingWhiteSpaceProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "get_char", "style"], "arg_types": ["prompt_toolkit.layout.processors.ShowTrailingWhiteSpaceProcessor", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ShowTrailingWhiteSpaceProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.ShowTrailingWhiteSpaceProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "arg_types": ["prompt_toolkit.layout.processors.ShowTrailingWhiteSpaceProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of ShowTrailingWhiteSpaceProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.ShowTrailingWhiteSpaceProcessor.get_char", "name": "get_char", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.ShowTrailingWhiteSpaceProcessor.style", "name": "style", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.ShowTrailingWhiteSpaceProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.ShowTrailingWhiteSpaceProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleCache": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.cache.SimpleCache", "kind": "Gdef", "module_public": false}, "SourceToDisplay": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "prompt_toolkit.layout.processors.SourceToDisplay", "line": 75, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "StyleAndTextTuples": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TabsProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.TabsProcessor", "name": "TabsProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.TabsProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.TabsProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "tabstop", "char1", "char2", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.TabsProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "tabstop", "char1", "char2", "style"], "arg_types": ["prompt_toolkit.layout.processors.TabsProcessor", {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TabsProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.TabsProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "arg_types": ["prompt_toolkit.layout.processors.TabsProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of TabsProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "char1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.TabsProcessor.char1", "name": "char1", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "char2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.TabsProcessor.char2", "name": "char2", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.TabsProcessor.style", "name": "style", "type": "builtins.str"}}, "tabstop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.TabsProcessor.tabstop", "name": "tabstop", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.TabsProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.TabsProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Transformation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.Transformation", "name": "Transformation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.Transformation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.Transformation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "fragments", "source_to_display", "display_to_source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.Transformation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "fragments", "source_to_display", "display_to_source"], "arg_types": ["prompt_toolkit.layout.processors.Transformation", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.processors.SourceToDisplay"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.processors.DisplayToSource"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Transformation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "display_to_source": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.Transformation.display_to_source", "name": "display_to_source", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fragments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.Transformation.fragments", "name": "fragments", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}}}, "source_to_display": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.Transformation.source_to_display", "name": "source_to_display", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.Transformation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.Transformation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransformationInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors.TransformationInput", "name": "TransformationInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.TransformationInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors.TransformationInput", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "buffer_control", "document", "lineno", "source_to_display", "fragments", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.TransformationInput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "buffer_control", "document", "lineno", "source_to_display", "fragments", "width", "height"], "arg_types": ["prompt_toolkit.layout.processors.TransformationInput", "prompt_toolkit.layout.controls.BufferControl", "prompt_toolkit.document.Document", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.processors.SourceToDisplay"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TransformationInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "buffer_control": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.TransformationInput.buffer_control", "name": "buffer_control", "type": "prompt_toolkit.layout.controls.BufferControl"}}, "document": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.TransformationInput.document", "name": "document", "type": "prompt_toolkit.document.Document"}}, "fragments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.TransformationInput.fragments", "name": "fragments", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}}}, "height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.TransformationInput.height", "name": "height", "type": "builtins.int"}}, "lineno": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.TransformationInput.lineno", "name": "lineno", "type": "builtins.int"}}, "source_to_display": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.TransformationInput.source_to_display", "name": "source_to_display", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unpack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.TransformationInput.unpack", "name": "unpack", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unpack of TransformationInput", "ret_type": {".class": "TupleType", "implicit": false, "items": ["prompt_toolkit.layout.controls.BufferControl", "prompt_toolkit.document.Document", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.processors.SourceToDisplay"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors.TransformationInput.width", "name": "width", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors.TransformationInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors.TransformationInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UIContent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.UIContent", "kind": "Gdef", "module_public": false}, "_MergedProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.processors._MergedProcessor", "name": "_MergedProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors._MergedProcessor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.processors", "mro": ["prompt_toolkit.layout.processors._MergedProcessor", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "processors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors._MergedProcessor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "processors"], "arg_types": ["prompt_toolkit.layout.processors._MergedProcessor", {".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _MergedProcessor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors._MergedProcessor.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "arg_types": ["prompt_toolkit.layout.processors._MergedProcessor", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of _MergedProcessor", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "processors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.processors._MergedProcessor.processors", "name": "processors", "type": {".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.processors._MergedProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.processors._MergedProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.processors.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.processors.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.processors.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.processors.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.processors.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.processors.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.processors.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "explode_text_fragments": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.utils.explode_text_fragments", "kind": "Gdef", "module_public": false}, "fragment_list_len": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.utils.fragment_list_len", "kind": "Gdef", "module_public": false}, "fragment_list_to_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.utils.fragment_list_to_text", "kind": "Gdef", "module_public": false}, "get_app": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app", "kind": "Gdef", "module_public": false}, "merge_processors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["processors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.processors.merge_processors", "name": "merge_processors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["processors"], "arg_types": [{".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_processors", "ret_type": "prompt_toolkit.layout.processors.Processor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "to_filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.to_filter", "kind": "Gdef", "module_public": false}, "to_formatted_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.to_formatted_text", "kind": "Gdef", "module_public": false}, "to_int": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.to_int", "kind": "Gdef", "module_public": false}, "to_str": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.to_str", "kind": "Gdef", "module_public": false}, "vi_insert_multiple_mode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.vi_insert_multiple_mode", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\layout\\processors.py"}