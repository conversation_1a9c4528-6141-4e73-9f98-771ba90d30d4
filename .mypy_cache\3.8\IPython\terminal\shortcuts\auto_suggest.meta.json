{"data_mtime": 1753839580, "dep_lines": [9, 23, 9, 14, 20, 21, 7, 8, 10, 11, 12, 13, 1, 2, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["prompt_toolkit.key_binding.bindings.named_commands", "IPython.terminal.shortcuts.filters", "prompt_toolkit.key_binding.bindings", "prompt_toolkit.layout.processors", "IPython.core.getipython", "IPython.utils.tokenutil", "prompt_toolkit.buffer", "prompt_toolkit.key_binding", "prompt_toolkit.auto_suggest", "prompt_toolkit.document", "prompt_toolkit.history", "prompt_toolkit.shortcuts", "re", "tokenize", "io", "typing", "warnings", "builtins", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "sys", "logging", "_frozen_importlib", "abc", "prompt_toolkit", "prompt_toolkit.key_binding.key_processor", "prompt_toolkit.layout", "prompt_toolkit.shortcuts.prompt", "typing_extensions"], "hash": "50471959377cb7d8198d47dc5f9e0dd897df6474", "id": "IPython.terminal.shortcuts.auto_suggest", "ignore_all": true, "interface_hash": "74b75c8c60b8d7466db2a5f9a9c49811bf658f80", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\terminal\\shortcuts\\auto_suggest.py", "plugin_data": null, "size": 13449, "suppressed": [], "version_id": "1.15.0"}