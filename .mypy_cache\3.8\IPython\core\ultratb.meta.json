{"data_mtime": 1753839580, "dep_lines": [108, 111, 112, 113, 114, 115, 116, 117, 92, 108, 111, 798, 93, 94, 95, 96, 97, 98, 99, 100, 102, 104, 108, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 105, 106], "dep_prios": [10, 10, 5, 5, 10, 10, 10, 5, 5, 20, 20, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["IPython.utils.colorable", "IPython.core.debugger", "IPython.core.display_trap", "IPython.core.excolors", "IPython.utils.PyColorize", "IPython.utils.path", "IPython.utils.py3compat", "IPython.utils.terminal", "collections.abc", "IPython.utils", "IPython.core", "executing.executing", "functools", "inspect", "linecache", "pydoc", "sys", "time", "traceback", "types", "typing", "stack_data", "IPython", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "os", "logging", "IPython.core.getipython", "_frozen_importlib", "_typeshed", "abc", "bdb", "cmd", "pdb", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "typing_extensions"], "hash": "fb0c13656960e99462e62a9dc78e150d7f75e22d", "id": "IPython.core.ultratb", "ignore_all": true, "interface_hash": "65e5072ea3e8921ff04c4c95afddb7471e3b7952", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\ultratb.py", "plugin_data": null, "size": 56456, "suppressed": ["pygments.formatters.terminal256", "pygments.styles"], "version_id": "1.15.0"}