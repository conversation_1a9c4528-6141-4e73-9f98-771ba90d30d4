{".class": "MypyFile", "_fullname": "numpy.polynomial.cheb<PERSON><PERSON>v", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABCPolyBase": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polybase.ABCPolyBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Chebyshev": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.polynomial._polybase.ABCPolyBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.polynomial.chebyshev.Chebyshev", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.Chebyshev", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.polynomial.cheb<PERSON><PERSON>v", "mro": ["numpy.polynomial.chebyshev.Chebyshev", "numpy.polynomial._polybase.ABCPolyBase", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "basis_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.basis_name", "name": "basis_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.domain", "name": "domain", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "interpolate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["cls", "func", "deg", "domain", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "name": "interpolate", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.interpolate", "name": "interpolate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["cls", "func", "deg", "domain", "args"], "arg_types": [{".class": "TypeType", "item": "numpy.polynomial.chebyshev.Chebyshev"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interpolate of Chebyshev", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.polynomial.chebyshev.Chebyshev.window", "name": "window", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.chebyshev.Chebyshev.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.polynomial.chebyshev.Chebyshev", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cheb2poly": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.cheb2poly", "name": "cheb2poly", "type": null}}, "chebadd": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebadd", "name": "cheb<PERSON><PERSON>", "type": null}}, "chebcompanion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebcompanion", "name": "chebcompanion", "type": null}}, "chebder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["c", "m", "scl", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebder", "name": "chebder", "type": null}}, "chebdiv": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebdiv", "name": "chebdiv", "type": null}}, "chebdomain": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebdomain", "name": "cheb<PERSON>ain", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "chebfit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["x", "y", "deg", "rcond", "full", "w"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebfit", "name": "chebfit", "type": null}}, "chebfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["roots"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebfromroots", "name": "chebfromroots", "type": null}}, "chebgauss": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebgauss", "name": "cheb<PERSON>ss", "type": null}}, "chebgrid2d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "y", "c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebgrid2d", "name": "chebgrid2d", "type": null}}, "chebgrid3d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["x", "y", "z", "c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebgrid3d", "name": "chebgrid3d", "type": null}}, "chebint": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["c", "m", "k", "lbnd", "scl", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebint", "name": "chebint", "type": null}}, "chebinterpolate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["func", "deg", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebinterpolate", "name": "chebinterpolate", "type": null}}, "chebline": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["off", "scl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebline", "name": "chebline", "type": null}}, "chebmul": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebmul", "name": "cheb<PERSON>l", "type": null}}, "chebmulx": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebmulx", "name": "chebmulx", "type": null}}, "chebone": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebone", "name": "chebone", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "chebpow": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["c", "pow", "maxpower"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebpow", "name": "cheb<PERSON>w", "type": null}}, "chebpts1": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["npts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebpts1", "name": "chebpts1", "type": null}}, "chebpts2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["npts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebpts2", "name": "chebpts2", "type": null}}, "chebroots": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebroots", "name": "chebroots", "type": null}}, "chebsub": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebsub", "name": "cheb<PERSON>b", "type": null}}, "chebtrim": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.polynomial.chebyshev.chebtrim", "name": "chebtrim", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chebval": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "c", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebval", "name": "cheb<PERSON>", "type": null}}, "chebval2d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "y", "c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebval2d", "name": "chebval2d", "type": null}}, "chebval3d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["x", "y", "z", "c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebval3d", "name": "chebval3d", "type": null}}, "chebvander": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x", "deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebvander", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": null}}, "chebvander2d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "y", "deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebvander2d", "name": "chebvander2d", "type": null}}, "chebvander3d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["x", "y", "z", "deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebvander3d", "name": "chebvander3d", "type": null}}, "chebweight": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.chebweight", "name": "chebweight", "type": null}}, "chebx": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebx", "name": "chebx", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "chebzero": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.chebyshev.chebzero", "name": "chebzero", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int_": {".class": "SymbolTableNode", "cross_ref": "numpy.int_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ndarray": {".class": "SymbolTableNode", "cross_ref": "numpy.n<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "poly2cheb": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pol"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.chebyshev.poly2cheb", "name": "poly2cheb", "type": null}}, "trimcoef": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.polyutils.trimcoef", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\numpy\\polynomial\\chebyshev.pyi"}