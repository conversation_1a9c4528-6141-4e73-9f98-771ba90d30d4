{".class": "MypyFile", "_fullname": "IPython.terminal.ptutils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Completer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.Completer", "kind": "Gdef"}, "Completion": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.Completion", "kind": "Gdef"}, "IPythonPTCompleter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.completion.base.Completer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.ptutils.IPythonPTCompleter", "name": "IPythonPTCompleter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.ptutils.IPythonPTCompleter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "IPython.terminal.ptutils", "mro": ["IPython.terminal.ptutils.IPythonPTCompleter", "prompt_toolkit.completion.base.Completer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "ipy_completer", "shell"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ptutils.IPythonPTCompleter.__init__", "name": "__init__", "type": null}}, "_get_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["body", "offset", "cursor_position", "ipyc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.terminal.ptutils.IPythonPTCompleter._get_completions", "name": "_get_completions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "IPython.terminal.ptutils.IPythonPTCompleter._get_completions", "name": "_get_completions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["body", "offset", "cursor_position", "ipyc"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_completions of IPythonPTCompleter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_ipy_completer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.ptutils.IPythonPTCompleter._ipy_completer", "name": "_ipy_completer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "complete_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ptutils.IPythonPTCompleter.get_completions", "name": "get_completions", "type": null}}, "ipy_completer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.terminal.ptutils.IPythonPTCompleter.ipy_completer", "name": "ipy_completer", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.terminal.ptutils.IPythonPTCompleter.ipy_completer", "name": "ipy_completer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.terminal.ptutils.IPythonPTCompleter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ipy_completer of IPythonPTCompleter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "shell": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.ptutils.IPythonPTCompleter.shell", "name": "shell", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.ptutils.IPythonPTCompleter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.ptutils.IPythonPTCompleter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IPythonPTLexer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.lexers.base.Lexer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.ptutils.IPythonPTLexer", "name": "IPythonPTLexer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.ptutils.IPythonPTLexer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "IPython.terminal.ptutils", "mro": ["IPython.terminal.ptutils.IPythonPTLexer", "prompt_toolkit.lexers.base.Lexer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ptutils.IPythonPTLexer.__init__", "name": "__init__", "type": null}}, "lex_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ptutils.IPythonPTLexer.lex_document", "name": "lex_document", "type": null}}, "magic_lexers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.ptutils.IPythonPTLexer.magic_lexers", "name": "magic_lexers", "type": {".class": "Instance", "args": ["builtins.str", "prompt_toolkit.lexers.pygments.PygmentsLexer"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "python_lexer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.ptutils.IPythonPTLexer.python_lexer", "name": "python_lexer", "type": "prompt_toolkit.lexers.pygments.PygmentsLexer"}}, "shell_lexer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.ptutils.IPythonPTLexer.shell_lexer", "name": "shell_lexer", "type": "prompt_toolkit.lexers.pygments.PygmentsLexer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.ptutils.IPythonPTLexer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.ptutils.IPythonPTLexer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Lexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.base.Lexer", "kind": "Gdef"}, "PygmentsLexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.pygments.PygmentsLexer", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ptutils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ptutils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ptutils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ptutils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ptutils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ptutils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_adjust_completion_text_based_on_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["text", "body", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ptutils._adjust_completion_text_based_on_context", "name": "_adjust_completion_text_based_on_context", "type": null}}, "_completion_sentinel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ptutils._completion_sentinel", "name": "_completion_sentinel", "type": "builtins.object"}}, "_deduplicate_completions": {".class": "SymbolTableNode", "cross_ref": "IPython.core.completer._deduplicate_completions", "kind": "Gdef"}, "_elide": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["string", "typed", "min_elide"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ptutils._elide", "name": "_elide", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["string", "typed", "min_elide"], "arg_types": ["builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_elide", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_elide_point": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["string", "min_elide"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ptutils._elide_point", "name": "_elide_point", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["string", "min_elide"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_elide_point", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_elide_typed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["string", "typed", "min_elide"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ptutils._elide_typed", "name": "_elide_typed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["string", "typed", "min_elide"], "arg_types": ["builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_elide_typed", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cursor_to_position": {".class": "SymbolTableNode", "cross_ref": "IPython.core.completer.cursor_to_position", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "patch_stdout": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.patch_stdout.patch_stdout", "kind": "Gdef"}, "provisionalcompleter": {".class": "SymbolTableNode", "cross_ref": "IPython.core.completer.provisionalcompleter", "kind": "Gdef"}, "pygments_lexers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.terminal.ptutils.pygments_lexers", "name": "pygments_lexers", "type": {".class": "AnyType", "missing_import_name": "IPython.terminal.ptutils.pygments_lexers", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "unicodedata": {".class": "SymbolTableNode", "cross_ref": "unicodedata", "kind": "Gdef"}, "wcwidth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.terminal.ptutils.wcwidth", "name": "wcwidth", "type": {".class": "AnyType", "missing_import_name": "IPython.terminal.ptutils.wcwidth", "source_any": null, "type_of_any": 3}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\terminal\\ptutils.py"}