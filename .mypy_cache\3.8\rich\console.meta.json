{"data_mtime": 1753839578, "dep_lines": [36, 47, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 73, 74, 75, 1577, 1752, 1860, 2005, 2020, 2021, 2305, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 47, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 20, 20, 20, 20, 20, 20, 20, 20, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich._null_file", "rich.errors", "rich.themes", "rich._emoji_replace", "rich._export_format", "rich._fileno", "rich._log_render", "rich.align", "rich.color", "rich.control", "rich.emoji", "rich.highlighter", "rich.markup", "rich.measure", "rich.pager", "rich.pretty", "rich.protocol", "rich.region", "rich.scope", "rich.screen", "rich.segment", "rich.style", "rich.styled", "rich.terminal_theme", "rich.text", "rich.theme", "rich._windows", "rich.live", "rich.status", "rich.rule", "rich.json", "rich.traceback", "rich.jupyter", "rich._win32_console", "rich._windows_renderer", "rich.cells", "inspect", "os", "platform", "sys", "threading", "zlib", "abc", "dataclasses", "datetime", "functools", "getpass", "html", "itertools", "math", "time", "types", "typing", "rich", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "logging", "_collections_abc", "_frozen_importlib", "_io", "_thread", "_typeshed", "enum", "io", "typing_extensions"], "hash": "8fdcb3af44dcf73d1b03cc18b72ff8aa1357bba8", "id": "rich.console", "ignore_all": true, "interface_hash": "be99f623f2ac99f671abb438702ed4d202e5b3ce", "mtime": 1723156628, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\rich\\console.py", "plugin_data": null, "size": 99101, "suppressed": [], "version_id": "1.15.0"}