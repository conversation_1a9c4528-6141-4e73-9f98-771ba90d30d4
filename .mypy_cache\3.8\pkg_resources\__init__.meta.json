{"data_mtime": 1753839571, "dep_lines": [29, 33, 34, 53, 75, 85, 86, 87, 88, 89, 95, 20, 22, 27, 28, 29, 30, 31, 32, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 55, 85, 91, 94, 96, 646, 1740, 1, 1, 1, 1, 1, 1, 1, 1, 90], "dep_prios": [10, 10, 10, 5, 5, 10, 10, 10, 10, 10, 25, 5, 10, 10, 10, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 5, 25, 25, 20, 20, 5, 20, 20, 20, 20, 30, 30, 30, 5], "dependencies": ["email.parser", "importlib.abc", "importlib.machinery", "collections.abc", "os.path", "packaging.markers", "packaging.requirements", "packaging.specifiers", "packaging.utils", "packaging.version", "_typeshed.importlib", "__future__", "sys", "_imp", "collections", "email", "errno", "functools", "importlib", "inspect", "io", "ntpath", "operator", "os", "pkgu<PERSON>", "platform", "plistlib", "posixpath", "re", "stat", "tempfile", "textwrap", "time", "types", "warnings", "zipfile", "zipimport", "typing", "packaging", "platformdirs", "_typeshed", "typing_extensions", "__main__", "linecache", "builtins", "json", "traitlets.utils.warnings", "pprint", "logging", "_frozen_importlib", "_io", "abc"], "hash": "a59476dc9429a2d8a8e27f3287fa379dc09502cc", "id": "pkg_resources", "ignore_all": true, "interface_hash": "036b97cbe72379501486e939f144f7aea70586d6", "mtime": 1750470699, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pkg_resources\\__init__.py", "plugin_data": null, "size": 126219, "suppressed": ["jaraco.text"], "version_id": "1.15.0"}