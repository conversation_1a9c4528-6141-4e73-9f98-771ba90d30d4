{".class": "MypyFile", "_fullname": "rich._windows", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ENABLE_VIRTUAL_TERMINAL_PROCESSING": {".class": "SymbolTableNode", "cross_ref": "rich._win32_console.ENABLE_VIRTUAL_TERMINAL_PROCESSING", "kind": "Gdef"}, "GetConsoleMode": {".class": "SymbolTableNode", "cross_ref": "rich._win32_console.GetConsoleMode", "kind": "Gdef"}, "GetStdHandle": {".class": "SymbolTableNode", "cross_ref": "rich._win32_console.GetStdHandle", "kind": "Gdef"}, "LegacyWindowsError": {".class": "SymbolTableNode", "cross_ref": "rich._win32_console.LegacyWindowsError", "kind": "Gdef"}, "LibraryLoader": {".class": "SymbolTableNode", "cross_ref": "ctypes.LibraryLoader", "kind": "Gdef"}, "WindowsConsoleFeatures": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rich._windows.WindowsConsoleFeatures", "name": "WindowsConsoleFeatures", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rich._windows.WindowsConsoleFeatures", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 9, "name": "vt", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 11, "name": "truecolor", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "rich._windows", "mro": ["rich._windows.WindowsConsoleFeatures", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "rich._windows.WindowsConsoleFeatures.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "vt", "truecolor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich._windows.WindowsConsoleFeatures.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "vt", "truecolor"], "arg_types": ["rich._windows.WindowsConsoleFeatures", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WindowsConsoleFeatures", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["vt", "truecolor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "rich._windows.WindowsConsoleFeatures.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["vt", "truecolor"], "arg_types": ["builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of WindowsConsoleFeatures", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "rich._windows.WindowsConsoleFeatures.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["vt", "truecolor"], "arg_types": ["builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of WindowsConsoleFeatures", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "truecolor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich._windows.WindowsConsoleFeatures.truecolor", "name": "truecolor", "type": "builtins.bool"}}, "vt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "rich._windows.WindowsConsoleFeatures.vt", "name": "vt", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rich._windows.WindowsConsoleFeatures.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rich._windows.WindowsConsoleFeatures", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rich._windows.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "ctypes": {".class": "SymbolTableNode", "cross_ref": "ctypes", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "features": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich._windows.features", "name": "features", "type": "rich._windows.WindowsConsoleFeatures"}}, "get_windows_console_features": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rich._windows.get_windows_console_features", "name": "get_windows_console_features", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_windows_console_features", "ret_type": "rich._windows.WindowsConsoleFeatures", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "platform": {".class": "SymbolTableNode", "cross_ref": "platform", "kind": "Gdef"}, "print": {".class": "SymbolTableNode", "cross_ref": "rich.print", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "windll": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rich._windows.windll", "name": "windll", "type": {".class": "Instance", "args": ["ctypes.WinDLL"], "extra_attrs": null, "type_ref": "ctypes.LibraryLoader"}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\rich\\_windows.py"}