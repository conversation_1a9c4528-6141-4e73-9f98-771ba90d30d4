{".class": "MypyFile", "_fullname": "prompt_toolkit.key_binding.key_bindings", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Binding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.key_binding.key_bindings.Binding", "name": "Binding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.Binding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.key_binding.key_bindings", "mro": ["prompt_toolkit.key_binding.key_bindings.Binding", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "keys", "handler", "filter", "eager", "is_global", "save_before", "record_in_macro"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.Binding.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "keys", "handler", "filter", "eager", "is_global", "save_before", "record_in_macro"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.Binding", {".class": "Instance", "args": [{".class": "UnionType", "items": ["prompt_toolkit.keys.Keys", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Binding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.Binding.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_bindings.Binding"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Binding", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.Binding.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.Binding", "prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of Binding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.Binding.eager", "name": "eager", "type": "prompt_toolkit.filters.base.Filter"}}, "filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.Binding.filter", "name": "filter", "type": "prompt_toolkit.filters.base.Filter"}}, "handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.Binding.handler", "name": "handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.object"], "extra_attrs": null, "type_ref": "typing.Coroutine"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_global": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.Binding.is_global", "name": "is_global", "type": "prompt_toolkit.filters.base.Filter"}}, "keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.Binding.keys", "name": "keys", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["prompt_toolkit.keys.Keys", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "record_in_macro": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.Binding.record_in_macro", "name": "record_in_macro", "type": "prompt_toolkit.filters.base.Filter"}}, "save_before": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.Binding.save_before", "name": "save_before", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.Binding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.key_binding.key_bindings.Binding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ConditionalKeyBindings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.key_binding.key_bindings._Proxy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings", "name": "ConditionalKeyBindings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.key_binding.key_bindings", "mro": ["prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings", "prompt_toolkit.key_binding.key_bindings._Proxy", "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key_bindings", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key_bindings", "filter"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings", "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConditionalKeyBindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings._update_cache", "name": "_update_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_cache of ConditionalKeyBindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings.filter", "name": "filter", "type": "prompt_toolkit.filters.base.Filter"}}, "key_bindings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings.key_bindings", "name": "key_bindings", "type": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.key_binding.key_bindings.ConditionalKeyBindings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Coroutine": {".class": "SymbolTableNode", "cross_ref": "typing.Coroutine", "kind": "Gdef", "module_public": false}, "DynamicKeyBindings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.key_binding.key_bindings._Proxy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings", "name": "DynamicKeyBindings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.key_binding.key_bindings", "mro": ["prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings", "prompt_toolkit.key_binding.key_bindings._Proxy", "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "get_key_bindings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "get_key_bindings"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DynamicKeyBindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings.__version", "name": "__version", "type": "builtins.int"}}, "_dummy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings._dummy", "name": "_dummy", "type": "prompt_toolkit.key_binding.key_bindings.KeyBindings"}}, "_last_child_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings._last_child_version", "name": "_last_child_version", "type": {".class": "NoneType"}}}, "_update_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings._update_cache", "name": "_update_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_cache of DynamicKeyBindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_key_bindings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings.get_key_bindings", "name": "get_key_bindings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.key_binding.key_bindings.DynamicKeyBindings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FilterOrBool": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.FilterOrBool", "kind": "Gdef", "module_public": false}, "GlobalOnlyKeyBindings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.key_binding.key_bindings._Proxy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.key_binding.key_bindings.GlobalOnlyKeyBindings", "name": "GlobalOnlyKeyBindings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.GlobalOnlyKeyBindings", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.key_binding.key_bindings", "mro": ["prompt_toolkit.key_binding.key_bindings.GlobalOnlyKeyBindings", "prompt_toolkit.key_binding.key_bindings._Proxy", "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key_bindings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.GlobalOnlyKeyBindings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key_bindings"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.GlobalOnlyKeyBindings", "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GlobalOnlyKeyBindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.GlobalOnlyKeyBindings._update_cache", "name": "_update_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.GlobalOnlyKeyBindings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_cache of GlobalOnlyKeyBindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_bindings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.GlobalOnlyKeyBindings.key_bindings", "name": "key_bindings", "type": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.GlobalOnlyKeyBindings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.key_binding.key_bindings.GlobalOnlyKeyBindings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Hashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef", "module_public": false}, "KEY_ALIASES": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.keys.KEY_ALIASES", "kind": "Gdef", "module_public": false}, "KeyBindings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "name": "KeyBindings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.key_binding.key_bindings", "mro": ["prompt_toolkit.key_binding.key_bindings.KeyBindings", "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of KeyBindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings.__version", "name": "__version", "type": "builtins.int"}}, "_bindings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings._bindings", "name": "_bindings", "type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_clear_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings._clear_cache", "name": "_clear_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear_cache of KeyBindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_bindings_for_keys_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings._get_bindings_for_keys_cache", "name": "_get_bindings_for_keys_cache", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeysTuple"}, {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "prompt_toolkit.cache.SimpleCache"}}}, "_get_bindings_starting_with_keys_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings._get_bindings_starting_with_keys_cache", "name": "_get_bindings_starting_with_keys_cache", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeysTuple"}, {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "prompt_toolkit.cache.SimpleCache"}}}, "_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings._version", "name": "_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_version of KeyBindings", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings._version", "name": "_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_version of KeyBindings", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5], "arg_names": ["self", "keys", "filter", "eager", "is_global", "save_before", "record_in_macro"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5], "arg_names": ["self", "keys", "filter", "eager", "is_global", "save_before", "record_in_macro"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings", {".class": "UnionType", "items": ["prompt_toolkit.keys.Keys", "builtins.str"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of KeyBindings", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.T", "id": -1, "name": "T", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable"}, "prompt_toolkit.key_binding.key_bindings.Binding"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.T", "id": -1, "name": "T", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable"}, "prompt_toolkit.key_binding.key_bindings.Binding"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.T", "id": -1, "name": "T", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable"}, "prompt_toolkit.key_binding.key_bindings.Binding"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_binding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings.add_binding", "name": "add_binding", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5], "arg_names": ["self", "keys", "filter", "eager", "is_global", "save_before", "record_in_macro"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings", {".class": "UnionType", "items": ["prompt_toolkit.keys.Keys", "builtins.str"], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.T", "id": -1, "name": "T", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable"}, "prompt_toolkit.key_binding.key_bindings.Binding"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.T", "id": -1, "name": "T", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable"}, "prompt_toolkit.key_binding.key_bindings.Binding"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.T", "id": -1, "name": "T", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable"}, "prompt_toolkit.key_binding.key_bindings.Binding"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings.bindings", "name": "bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bindings of KeyBindings", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings.bindings", "name": "bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bindings of KeyBindings", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_bindings_for_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings.get_bindings_for_keys", "name": "get_bindings_for_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeysTuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bindings_for_keys of KeyBindings", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_bindings_starting_with_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings.get_bindings_starting_with_keys", "name": "get_bindings_starting_with_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeysTuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bindings_starting_with_keys of KeyBindings", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings", {".class": "UnionType", "items": ["prompt_toolkit.keys.Keys", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of KeyBindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_binding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings.remove_binding", "name": "remove_binding", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings", {".class": "UnionType", "items": ["prompt_toolkit.keys.Keys", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KeyBindingsBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_version", 1], ["bindings", 1], ["get_bindings_for_keys", 1], ["get_bindings_starting_with_keys", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "name": "KeyBindingsBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.key_binding.key_bindings", "mro": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "builtins.object"], "names": {".class": "SymbolTable", "_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase._version", "name": "_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_version of KeyBindingsBase", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase._version", "name": "_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_version of KeyBindingsBase", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase.bindings", "name": "bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bindings of KeyBindingsBase", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase.bindings", "name": "bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bindings of KeyBindingsBase", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_bindings_for_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase.get_bindings_for_keys", "name": "get_bindings_for_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeysTuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bindings_for_keys of KeyBindingsBase", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase.get_bindings_for_keys", "name": "get_bindings_for_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeysTuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bindings_for_keys of KeyBindingsBase", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_bindings_starting_with_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase.get_bindings_starting_with_keys", "name": "get_bindings_starting_with_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeysTuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bindings_starting_with_keys of KeyBindingsBase", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase.get_bindings_starting_with_keys", "name": "get_bindings_starting_with_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeysTuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bindings_starting_with_keys of KeyBindingsBase", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KeyHandlerCallable": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable", "line": 92, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.object"], "extra_attrs": null, "type_ref": "typing.Coroutine"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "KeyPressEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_processor.KeyPressEvent", "kind": "Gdef", "module_public": false}, "Keys": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.keys.Keys", "kind": "Gdef", "module_public": false}, "KeysTuple": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "prompt_toolkit.key_binding.key_bindings.KeysTuple", "line": 151, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": ["prompt_toolkit.keys.Keys", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "Never": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.Never", "kind": "Gdef", "module_public": false}, "NotImplementedOrNone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "prompt_toolkit.key_binding.key_bindings.NotImplementedOrNone", "line": 66, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.object"}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "SimpleCache": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.cache.SimpleCache", "kind": "Gdef", "module_public": false}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings.T", "name": "T", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable"}, "prompt_toolkit.key_binding.key_bindings.Binding"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_MergedKeyBindings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.key_binding.key_bindings._Proxy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.key_binding.key_bindings._MergedKeyBindings", "name": "_MergedKeyBindings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings._MergedKeyBindings", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.key_binding.key_bindings", "mro": ["prompt_toolkit.key_binding.key_bindings._MergedKeyBindings", "prompt_toolkit.key_binding.key_bindings._Proxy", "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "registries"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings._MergedKeyBindings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "registries"], "arg_types": ["prompt_toolkit.key_binding.key_bindings._MergedKeyBindings", {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _MergedKeyBindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings._MergedKeyBindings._update_cache", "name": "_update_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings._MergedKeyBindings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_cache of _MergedKeyBindings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "registries": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings._MergedKeyBindings.registries", "name": "registries", "type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings._MergedKeyBindings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.key_binding.key_bindings._MergedKeyBindings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Proxy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy", "name": "_Proxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.key_binding.key_bindings", "mro": ["prompt_toolkit.key_binding.key_bindings._Proxy", "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings._Proxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Proxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bindings2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy._bindings2", "name": "_bindings2", "type": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"}}, "_last_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy._last_version", "name": "_last_version", "type": "<PERSON>.<PERSON>"}}, "_update_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy._update_cache", "name": "_update_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings._Proxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_cache of _Proxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy._version", "name": "_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings._Proxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_version of _Proxy", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy._version", "name": "_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings._Proxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_version of _Proxy", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy.bindings", "name": "bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings._Proxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bindings of _Proxy", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy.bindings", "name": "bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.key_binding.key_bindings._Proxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bindings of _Proxy", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_bindings_for_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy.get_bindings_for_keys", "name": "get_bindings_for_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["prompt_toolkit.key_binding.key_bindings._Proxy", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeysTuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bindings_for_keys of _Proxy", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_bindings_starting_with_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy.get_bindings_starting_with_keys", "name": "get_bindings_starting_with_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "keys"], "arg_types": ["prompt_toolkit.key_binding.key_bindings._Proxy", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeysTuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bindings_starting_with_keys of _Proxy", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.key_binding.key_bindings._Proxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.key_binding.key_bindings._Proxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.key_bindings.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.key_bindings.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.key_bindings.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.key_bindings.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.key_bindings.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.key_bindings.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.key_bindings.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_parse_key": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings._parse_key", "name": "_parse_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["key"], "arg_types": [{".class": "UnionType", "items": ["prompt_toolkit.keys.Keys", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_key", "ret_type": {".class": "UnionType", "items": ["builtins.str", "prompt_toolkit.keys.Keys"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "abstractproperty": {".class": "SymbolTableNode", "cross_ref": "abc.abstractproperty", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "isawaitable": {".class": "SymbolTableNode", "cross_ref": "inspect.isawaitable", "kind": "Gdef", "module_public": false}, "key_binding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1], "arg_names": ["filter", "eager", "is_global", "save_before", "record_in_macro"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.key_binding", "name": "key_binding", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1], "arg_names": ["filter", "eager", "is_global", "save_before", "record_in_macro"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_binding", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.key_binding.key_bindings.KeyHandlerCallable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "prompt_toolkit.key_binding.key_bindings.Binding", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge_key_bindings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["bindings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.key_bindings.merge_key_bindings", "name": "merge_key_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["bindings"], "arg_types": [{".class": "Instance", "args": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_key_bindings", "ret_type": "prompt_toolkit.key_binding.key_bindings._MergedKeyBindings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.to_filter", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\key_binding\\key_bindings.py"}