{"data_mtime": 1753839575, "dep_lines": [8, 22, 23, 24, 25, 328, 9, 10, 19, 20, 21, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["httpcore._backends.base", "httpcore._sync.connection", "httpcore._sync.connection_pool", "httpcore._sync.http11", "httpcore._sync.interfaces", "httpcore._sync.http2", "httpcore._exceptions", "httpcore._models", "httpcore._ssl", "httpcore._synchronization", "httpcore._trace", "__future__", "base64", "logging", "ssl", "typing", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "sys", "_frozen_importlib", "_ssl", "abc", "httpcore._backends", "typing_extensions"], "hash": "7fdff2222f3e9b44e6a3554a282d2db744a5657a", "id": "httpcore._sync.http_proxy", "ignore_all": true, "interface_hash": "5039d34e36437348c38cac37e57bb8de73f6baff", "mtime": 1750258716, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\httpcore\\_sync\\http_proxy.py", "plugin_data": null, "size": 14463, "suppressed": [], "version_id": "1.15.0"}