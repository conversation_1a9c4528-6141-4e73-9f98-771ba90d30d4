{".class": "MypyFile", "_fullname": "numpy.testing._private.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AnyStr": {".class": "SymbolTableNode", "cross_ref": "typing.AnyStr", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like.DTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HAS_LAPACK64": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.testing._private.utils.HAS_LAPACK64", "name": "HAS_LAPACK64", "type": "builtins.bool"}}, "HAS_REFCOUNT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.testing._private.utils.HAS_REFCOUNT", "name": "HAS_REFCOUNT", "type": "builtins.bool"}}, "IS_PYPY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.testing._private.utils.IS_PYPY", "name": "IS_PYPY", "type": "builtins.bool"}}, "IS_PYSTON": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "numpy.testing._private.utils.IS_PYSTON", "name": "IS_PYSTON", "type": "builtins.bool"}}, "IgnoreException": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.testing._private.utils.IgnoreException", "name": "IgnoreException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.IgnoreException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.testing._private.utils", "mro": ["numpy.testing._private.utils.IgnoreException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils.IgnoreException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.testing._private.utils.IgnoreException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "KnownFailureException": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.testing._private.utils.KnownFailureException", "name": "KnownFailureException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.KnownFailureException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.testing._private.utils", "mro": ["numpy.testing._private.utils.KnownFailureException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils.KnownFailureException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.testing._private.utils.KnownFailureException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.ParamSpec", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SkipTest": {".class": "SymbolTableNode", "cross_ref": "unittest.case.SkipTest", "kind": "Gdef", "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeDT64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeDT64_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeNumber_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeNumber_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeObject_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeObject_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLikeTD64_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLikeTD64_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ComparisonFunc": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "numpy.testing._private.utils._ComparisonFunc", "line": 50, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "type_ref": "numpy._typing._array_like.NDArray"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.bool", "numpy.bool_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.number"}, {".class": "TypeAliasType", "args": [{".class": "UnionType", "items": ["numpy.bool_", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "numpy.number"}, "numpy.object_"], "uses_pep604_syntax": false}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ET": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "name": "_ET", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, "_FT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._FT", "name": "_FT", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}}, "_FloatValue": {".class": "SymbolTableNode", "cross_ref": "numpy._FloatValue", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_P": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._P", "name": "_P", "upper_bound": "builtins.object", "variance": 0}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing._private.utils.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing._private.utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing._private.utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing._private.utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing._private.utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing._private.utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing._private.utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_clear_and_catch_warnings_with_records": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.testing._private.utils.clear_and_catch_warnings"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.testing._private.utils._clear_and_catch_warnings_with_records", "name": "_clear_and_catch_warnings_with_records", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils._clear_and_catch_warnings_with_records", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.testing._private.utils", "mro": ["numpy.testing._private.utils._clear_and_catch_warnings_with_records", "numpy.testing._private.utils.clear_and_catch_warnings", "warnings.catch_warnings", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils._clear_and_catch_warnings_with_records.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.testing._private.utils._clear_and_catch_warnings_with_records"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of _clear_and_catch_warnings_with_records", "ret_type": {".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._clear_and_catch_warnings_with_records.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.testing._private.utils._clear_and_catch_warnings_with_records", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_clear_and_catch_warnings_without_records": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.testing._private.utils.clear_and_catch_warnings"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.testing._private.utils._clear_and_catch_warnings_without_records", "name": "_clear_and_catch_warnings_without_records", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils._clear_and_catch_warnings_without_records", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.testing._private.utils", "mro": ["numpy.testing._private.utils._clear_and_catch_warnings_without_records", "numpy.testing._private.utils.clear_and_catch_warnings", "warnings.catch_warnings", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils._clear_and_catch_warnings_without_records.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.testing._private.utils._clear_and_catch_warnings_without_records"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of _clear_and_catch_warnings_without_records", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._clear_and_catch_warnings_without_records.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.testing._private.utils._clear_and_catch_warnings_without_records", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "assert_": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["val", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_", "name": "assert_", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["val", "msg"], "arg_types": ["builtins.object", {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_allclose": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_allclose", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["actual", "desired", "rtol", "atol", "equal_nan", "err_msg", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_allclose", "name": "assert_allclose", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["actual", "desired", "rtol", "atol", "equal_nan", "err_msg", "verbose"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.float", "builtins.bool", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_allclose", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_allclose", "name": "assert_allclose", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["actual", "desired", "rtol", "atol", "equal_nan", "err_msg", "verbose"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.float", "builtins.bool", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_allclose", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["actual", "desired", "rtol", "atol", "equal_nan", "err_msg", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_allclose", "name": "assert_allclose", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["actual", "desired", "rtol", "atol", "equal_nan", "err_msg", "verbose"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, "builtins.float", "builtins.float", "builtins.bool", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_allclose", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_allclose", "name": "assert_allclose", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["actual", "desired", "rtol", "atol", "equal_nan", "err_msg", "verbose"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, "builtins.float", "builtins.float", "builtins.bool", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_allclose", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["actual", "desired", "rtol", "atol", "equal_nan", "err_msg", "verbose"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.float", "builtins.bool", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_allclose", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["actual", "desired", "rtol", "atol", "equal_nan", "err_msg", "verbose"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, "builtins.float", "builtins.float", "builtins.bool", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_allclose", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "assert_almost_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["actual", "desired", "decimal", "err_msg", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_almost_equal", "name": "assert_almost_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["actual", "desired", "decimal", "err_msg", "verbose"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_almost_equal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_approx_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["actual", "desired", "significant", "err_msg", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_approx_equal", "name": "assert_approx_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["actual", "desired", "significant", "err_msg", "verbose"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._FloatValue"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._FloatValue"}, "builtins.int", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_approx_equal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_array_almost_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["x", "y", "decimal", "err_msg", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_array_almost_equal", "name": "assert_array_almost_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["x", "y", "decimal", "err_msg", "verbose"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_almost_equal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_array_almost_equal_nulp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "y", "nulp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_array_almost_equal_nulp", "name": "assert_array_almost_equal_nulp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["x", "y", "nulp"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_almost_equal_nulp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_array_compare": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["comparison", "x", "y", "err_msg", "verbose", "header", "precision", "equal_nan", "equal_inf", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_array_compare", "name": "assert_array_compare", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["comparison", "x", "y", "err_msg", "verbose", "header", "precision", "equal_nan", "equal_inf", "strict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.testing._private.utils._ComparisonFunc"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.str", "builtins.bool", "builtins.str", "typing.SupportsIndex", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_compare", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_array_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["x", "y", "err_msg", "verbose", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_array_equal", "name": "assert_array_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["x", "y", "err_msg", "verbose", "strict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_equal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_array_less": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_array_less", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_array_less", "name": "assert_array_less", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_less", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_array_less", "name": "assert_array_less", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_less", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_array_less", "name": "assert_array_less", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_less", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_array_less", "name": "assert_array_less", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_less", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_array_less", "name": "assert_array_less", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_less", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_array_less", "name": "assert_array_less", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_less", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeObject_co"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_less", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeTD64_co"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_less", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["x", "y", "err_msg", "verbose"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeDT64_co"}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_less", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "assert_array_max_ulp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "b", "maxulp", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_array_max_ulp", "name": "assert_array_max_ulp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["a", "b", "maxulp", "dtype"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like._ArrayLikeNumber_co"}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_array_max_ulp", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["actual", "desired", "err_msg", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_equal", "name": "assert_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["actual", "desired", "err_msg", "verbose"], "arg_types": ["builtins.object", "builtins.object", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_equal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_no_gc_cycles": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_no_gc_cycles", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_no_gc_cycles", "name": "assert_no_gc_cycles", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_gc_cycles", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_no_gc_cycles", "name": "assert_no_gc_cycles", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_gc_cycles", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_no_gc_cycles", "name": "assert_no_gc_cycles", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "kwargs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_gc_cycles", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_no_gc_cycles", "name": "assert_no_gc_cycles", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "kwargs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_gc_cycles", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_gc_cycles", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "kwargs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_gc_cycles", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_gc_cycles", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}]}]}}}, "assert_no_warnings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_no_warnings", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_no_warnings", "name": "assert_no_warnings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_warnings", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_no_warnings", "name": "assert_no_warnings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_warnings", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_no_warnings", "name": "assert_no_warnings", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "kwargs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_no_warnings", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_warnings", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_no_warnings", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_no_warnings", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_no_warnings", "name": "assert_no_warnings", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "kwargs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_no_warnings", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_warnings", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_no_warnings", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_no_warnings", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_warnings", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "kwargs"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_no_warnings", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_no_warnings", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_no_warnings", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_no_warnings", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_no_warnings", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "assert_raises": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_raises", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": [null, null, "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_raises", "name": "assert_raises", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": [null, null, "args", "kwargs"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_raises", "name": "assert_raises", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": [null, null, "args", "kwargs"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["expected_exception", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_raises", "name": "assert_raises", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["expected_exception", "msg"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "unittest.case._AssertRaisesContext"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_raises", "name": "assert_raises", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["expected_exception", "msg"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "unittest.case._AssertRaisesContext"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": [null, null, "args", "kwargs"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}]}, {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["expected_exception", "msg"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "unittest.case._AssertRaisesContext"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}]}}}, "assert_raises_regex": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_raises_regex", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": [null, null, null, "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_raises_regex", "name": "assert_raises_regex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": [null, null, null, "args", "kwargs"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises_regex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_raises_regex", "name": "assert_raises_regex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": [null, null, null, "args", "kwargs"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises_regex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["expected_exception", "expected_regex", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_raises_regex", "name": "assert_raises_regex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["expected_exception", "expected_regex", "msg"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises_regex", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "unittest.case._AssertRaisesContext"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_raises_regex", "name": "assert_raises_regex", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["expected_exception", "expected_regex", "msg"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises_regex", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "unittest.case._AssertRaisesContext"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": [null, null, null, "args", "kwargs"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises_regex", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_raises_regex#0", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}]}, {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["expected_exception", "expected_regex", "msg"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_raises_regex", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "unittest.case._AssertRaisesContext"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._ET", "id": -1, "name": "_ET", "namespace": "numpy.testing._private.utils.assert_raises_regex", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}]}}}, "assert_string_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["actual", "desired"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_string_equal", "name": "assert_string_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["actual", "desired"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_string_equal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_warns": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.assert_warns", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["warning_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_warns", "name": "assert_warns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["warning_class"], "arg_types": [{".class": "TypeType", "item": "builtins.Warning"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_warns", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_warns", "name": "assert_warns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["warning_class"], "arg_types": [{".class": "TypeType", "item": "builtins.Warning"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_warns", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": [null, null, "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.assert_warns", "name": "assert_warns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": [null, null, "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "builtins.Warning"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_warns", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_warns", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_warns", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_warns", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.assert_warns", "name": "assert_warns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": [null, null, "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "builtins.Warning"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_warns", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_warns", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_warns", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_warns", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["warning_class"], "arg_types": [{".class": "TypeType", "item": "builtins.Warning"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_warns", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": [null, null, "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "builtins.Warning"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_warns", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_warns", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_warns", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "numpy.testing._private.utils._P", "id": -1, "name": "_P", "namespace": "numpy.testing._private.utils.assert_warns", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -2, "name": "_T", "namespace": "numpy.testing._private.utils.assert_warns", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "ast": {".class": "SymbolTableNode", "cross_ref": "ast", "kind": "Gdef", "module_hidden": true, "module_public": false}, "bool_": {".class": "SymbolTableNode", "cross_ref": "numpy.bool_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "break_cycles": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.break_cycles", "name": "break_cycles", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "break_cycles", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_err_msg": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["arrays", "err_msg", "header", "verbose", "names", "precision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.build_err_msg", "name": "build_err_msg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["arrays", "err_msg", "header", "verbose", "names", "precision"], "arg_types": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str", "builtins.str", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "typing.SupportsIndex"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_err_msg", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_and_catch_warnings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "warnings.catch_warnings"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.testing._private.utils.clear_and_catch_warnings", "name": "clear_and_catch_warnings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.testing._private.utils", "mro": ["numpy.testing._private.utils.clear_and_catch_warnings", "warnings.catch_warnings", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["numpy.testing._private.utils.clear_and_catch_warnings"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of clear_and_catch_warnings", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": [null, null, null, null], "arg_types": ["numpy.testing._private.utils.clear_and_catch_warnings", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": "builtins.BaseException"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "types.TracebackType"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of clear_and_catch_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_static"], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.__new__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cls", "record", "modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "record", "modules"], "arg_types": [{".class": "TypeType", "item": "numpy.testing._private.utils.clear_and_catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of clear_and_catch_warnings", "ret_type": "numpy.testing._private.utils._clear_and_catch_warnings_without_records", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "record", "modules"], "arg_types": [{".class": "TypeType", "item": "numpy.testing._private.utils.clear_and_catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of clear_and_catch_warnings", "ret_type": "numpy.testing._private.utils._clear_and_catch_warnings_without_records", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "record", "modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "record", "modules"], "arg_types": [{".class": "TypeType", "item": "numpy.testing._private.utils.clear_and_catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of clear_and_catch_warnings", "ret_type": "numpy.testing._private.utils._clear_and_catch_warnings_with_records", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "record", "modules"], "arg_types": [{".class": "TypeType", "item": "numpy.testing._private.utils.clear_and_catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of clear_and_catch_warnings", "ret_type": "numpy.testing._private.utils._clear_and_catch_warnings_with_records", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "record", "modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "record", "modules"], "arg_types": [{".class": "TypeType", "item": "numpy.testing._private.utils.clear_and_catch_warnings"}, "builtins.bool", {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of clear_and_catch_warnings", "ret_type": "numpy.testing._private.utils.clear_and_catch_warnings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "record", "modules"], "arg_types": [{".class": "TypeType", "item": "numpy.testing._private.utils.clear_and_catch_warnings"}, "builtins.bool", {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of clear_and_catch_warnings", "ret_type": "numpy.testing._private.utils.clear_and_catch_warnings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "record", "modules"], "arg_types": [{".class": "TypeType", "item": "numpy.testing._private.utils.clear_and_catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of clear_and_catch_warnings", "ret_type": "numpy.testing._private.utils._clear_and_catch_warnings_without_records", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "record", "modules"], "arg_types": [{".class": "TypeType", "item": "numpy.testing._private.utils.clear_and_catch_warnings"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of clear_and_catch_warnings", "ret_type": "numpy.testing._private.utils._clear_and_catch_warnings_with_records", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "record", "modules"], "arg_types": [{".class": "TypeType", "item": "numpy.testing._private.utils.clear_and_catch_warnings"}, "builtins.bool", {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of clear_and_catch_warnings", "ret_type": "numpy.testing._private.utils.clear_and_catch_warnings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "class_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.class_modules", "name": "class_modules", "type": {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.modules", "name": "modules", "type": {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils.clear_and_catch_warnings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.testing._private.utils.clear_and_catch_warnings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_hidden": true, "module_public": false}, "decorate_methods": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "decorator", "testmatch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.decorate_methods", "name": "decorate_methods", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "decorator", "testmatch"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decorate_methods", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "jiffies": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["_load_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.jiffies", "name": "jiffies", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["_load_time"], "arg_types": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "jiffies", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "measure": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["code_str", "times", "label"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.measure", "name": "measure", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["code_str", "times", "label"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "ast.mod", "ast.AST"], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "measure", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "memusage": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["processName", "instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.memusage", "name": "memusage", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["processName", "instance"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "memusage", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "number": {".class": "SymbolTableNode", "cross_ref": "numpy.number", "kind": "Gdef", "module_hidden": true, "module_public": false}, "object_": {".class": "SymbolTableNode", "cross_ref": "numpy.object_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "print_assert_equal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["test_string", "actual", "desired"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.print_assert_equal", "name": "print_assert_equal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["test_string", "actual", "desired"], "arg_types": ["builtins.str", "builtins.object", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_assert_equal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "raises": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.raises", "name": "raises", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "TypeType", "item": "builtins.BaseException"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._FT", "id": -1, "name": "_FT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._FT", "id": -1, "name": "_FT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._FT", "id": -1, "name": "_FT", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rundocs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["filename", "raise_on_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.rundocs", "name": "rundocs", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["filename", "raise_on_error"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rundocs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runstring": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["astr", "dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.runstring", "name": "runstring", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["astr", "dict"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "types.CodeType"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "runstring", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "suppress_warnings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.testing._private.utils.suppress_warnings", "name": "suppress_warnings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.suppress_warnings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "numpy.testing._private.utils", "mro": ["numpy.testing._private.utils.suppress_warnings", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.suppress_warnings.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "func"], "arg_types": ["numpy.testing._private.utils.suppress_warnings", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._FT", "id": -1, "name": "_FT", "namespace": "numpy.testing._private.utils.suppress_warnings.__call__", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of suppress_warnings", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._FT", "id": -1, "name": "_FT", "namespace": "numpy.testing._private.utils.suppress_warnings.__call__", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._FT", "id": -1, "name": "_FT", "namespace": "numpy.testing._private.utils.suppress_warnings.__call__", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.suppress_warnings.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -1, "name": "_T", "namespace": "numpy.testing._private.utils.suppress_warnings.__enter__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of suppress_warnings", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -1, "name": "_T", "namespace": "numpy.testing._private.utils.suppress_warnings.__enter__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils._T", "id": -1, "name": "_T", "namespace": "numpy.testing._private.utils.suppress_warnings.__enter__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.suppress_warnings.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": [null, null, null, null], "arg_types": ["numpy.testing._private.utils.suppress_warnings", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": "builtins.BaseException"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "types.TracebackType"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of suppress_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "forwarding_rule"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.suppress_warnings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "forwarding_rule"], "arg_types": ["numpy.testing._private.utils.suppress_warnings", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "module"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "once"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "location"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of suppress_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "category", "message", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.suppress_warnings.filter", "name": "filter", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "category", "message", "module"], "arg_types": ["numpy.testing._private.utils.suppress_warnings", {".class": "TypeType", "item": "builtins.Warning"}, "builtins.str", {".class": "UnionType", "items": [{".class": "NoneType"}, "types.ModuleType"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter of suppress_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.testing._private.utils.suppress_warnings.log", "name": "log", "type": {".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "record": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "category", "message", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.suppress_warnings.record", "name": "record", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "category", "message", "module"], "arg_types": ["numpy.testing._private.utils.suppress_warnings", {".class": "TypeType", "item": "builtins.Warning"}, "builtins.str", {".class": "UnionType", "items": [{".class": "NoneType"}, "types.ModuleType"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "record of suppress_warnings", "ret_type": {".class": "Instance", "args": ["warnings.WarningMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.testing._private.utils.suppress_warnings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.testing._private.utils.suppress_warnings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "tempdir": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.tempdir", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["suffix", "prefix", "dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.tempdir", "name": "tempdir", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["suffix", "prefix", "dir"], "arg_types": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tempdir", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.tempdir", "name": "tempdir", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["suffix", "prefix", "dir"], "arg_types": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tempdir", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["suffix", "prefix", "dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.tempdir", "name": "tempdir", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["suffix", "prefix", "dir"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tempdir", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.tempdir", "name": "tempdir", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["suffix", "prefix", "dir"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tempdir", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["suffix", "prefix", "dir"], "arg_types": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tempdir", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["suffix", "prefix", "dir"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tempdir", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.tempdir", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}]}}}, "temppath": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.testing._private.utils.temppath", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1], "arg_names": ["suffix", "prefix", "dir", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.temppath", "name": "temp<PERSON>", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["suffix", "prefix", "dir", "text"], "arg_types": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "temp<PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.temppath", "name": "temp<PERSON>", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["suffix", "prefix", "dir", "text"], "arg_types": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "temp<PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1], "arg_names": ["suffix", "prefix", "dir", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.testing._private.utils.temppath", "name": "temp<PERSON>", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["suffix", "prefix", "dir", "text"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "temp<PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.testing._private.utils.temppath", "name": "temp<PERSON>", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["suffix", "prefix", "dir", "text"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "temp<PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["suffix", "prefix", "dir", "text"], "arg_types": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "temp<PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["suffix", "prefix", "dir", "text"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "temp<PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "AnyStr", "namespace": "numpy.testing._private.utils.temppath", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}]}}}, "type_check_only": {".class": "SymbolTableNode", "cross_ref": "typing.type_check_only", "kind": "Gdef", "module_hidden": true, "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_hidden": true, "module_public": false}, "unittest": {".class": "SymbolTableNode", "cross_ref": "unittest", "kind": "Gdef", "module_hidden": true, "module_public": false}, "verbose": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.testing._private.utils.verbose", "name": "verbose", "type": "builtins.int"}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\numpy\\testing\\_private\\utils.pyi"}