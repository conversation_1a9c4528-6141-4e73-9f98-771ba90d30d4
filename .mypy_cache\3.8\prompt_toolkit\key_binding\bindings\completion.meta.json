{"data_mtime": 1753839575, "dep_lines": [10, 17, 18, 11, 16, 19, 20, 23, 24, 4, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 25, 25, 5, 10, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["prompt_toolkit.application.run_in_terminal", "prompt_toolkit.key_binding.key_bindings", "prompt_toolkit.key_binding.key_processor", "prompt_toolkit.completion", "prompt_toolkit.formatted_text", "prompt_toolkit.keys", "prompt_toolkit.utils", "prompt_toolkit.application", "prompt_toolkit.shortcuts", "__future__", "asyncio", "math", "typing", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "sys", "logging", "_asyncio", "_frozen_importlib", "abc", "prompt_toolkit.application.application", "prompt_toolkit.completion.base", "prompt_toolkit.shortcuts.prompt", "weakref"], "hash": "4e5c01b1211dd32ae3ed15f587922395eb79f27b", "id": "prompt_toolkit.key_binding.bindings.completion", "ignore_all": true, "interface_hash": "8375259e45e2ae123e562dc643ce8d4ef36919c1", "mtime": 1708667605, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\key_binding\\bindings\\completion.py", "plugin_data": null, "size": 6902, "suppressed": [], "version_id": "1.15.0"}