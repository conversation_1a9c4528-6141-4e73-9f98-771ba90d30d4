{"data_mtime": 1753839575, "dep_lines": [20, 21, 22, 23, 24, 25, 32, 33, 34, 35, 36, 37, 38, 39, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["prompt_toolkit.application.current", "prompt_toolkit.application.run_in_terminal", "prompt_toolkit.auto_suggest", "prompt_toolkit.cache", "prompt_toolkit.clipboard", "prompt_toolkit.completion", "prompt_toolkit.document", "prompt_toolkit.eventloop", "prompt_toolkit.filters", "prompt_toolkit.history", "prompt_toolkit.search", "prompt_toolkit.selection", "prompt_toolkit.utils", "prompt_toolkit.validation", "__future__", "asyncio", "logging", "os", "re", "shlex", "shutil", "subprocess", "tempfile", "collections", "enum", "functools", "typing", "builtins", "json", "traitlets.utils.warnings", "pprint", "sys", "_asyncio", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "prompt_toolkit.application", "prompt_toolkit.application.application", "prompt_toolkit.clipboard.base", "prompt_toolkit.completion.base", "prompt_toolkit.filters.base", "prompt_toolkit.filters.utils", "typing_extensions"], "hash": "9cd829cd80c03af25452df5ce605f1fe1b594763", "id": "prompt_toolkit.buffer", "ignore_all": true, "interface_hash": "531acab6697052b333caac6cdf8f27a8ad9495d9", "mtime": 1708667605, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\buffer.py", "plugin_data": null, "size": 74072, "suppressed": [], "version_id": "1.15.0"}