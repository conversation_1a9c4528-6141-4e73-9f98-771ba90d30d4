"""Pydantic models for LLM Proxy Server"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field


class ChatMessage(BaseModel):
    role: str = Field(..., description="Role of the message sender")
    content: str = Field(..., description="Content of the message")
    images: Optional[List[str]] = Field(None, description="Base64 encoded images")


class ChatRequest(BaseModel):
    model: str = Field(..., description="Model name to use")
    messages: List[ChatMessage] = Field(..., description="List of messages")
    stream: Optional[bool] = Field(False, description="Enable streaming")
    format: Optional[str] = Field(None, description="Response format")
    options: Optional[Dict[str, Any]] = Field(None, description="Model options")
    keep_alive: Optional[Union[int, str]] = Field(None, description="Keep alive duration")
    think: Optional[bool] = Field(None, description="Enable thinking for reasoning models")


class ChatResponse(BaseModel):
    model: str
    created_at: str
    message: ChatMessage
    done: bool
    total_duration: Optional[int] = None
    load_duration: Optional[int] = None
    prompt_eval_count: Optional[int] = None
    prompt_eval_duration: Optional[int] = None
    eval_count: Optional[int] = None
    eval_duration: Optional[int] = None
    # Fields for reasoning models (like o1)
    thinking: Optional[str] = Field(None, description="Thinking/reasoning process for reasoning models")
    reasoning_tokens: Optional[int] = Field(None, description="Number of tokens used in reasoning")
    reasoning_duration: Optional[int] = Field(None, description="Time spent on reasoning in nanoseconds")
    # Custom field to track which server handled the request
    proxy_server: Optional[str] = Field(None, description="Server that handled the request")


class GenerateRequest(BaseModel):
    model: str = Field(..., description="Model name to use")
    prompt: str = Field(..., description="Prompt to generate from")
    images: Optional[List[str]] = Field(None, description="Base64 encoded images")
    format: Optional[str] = Field(None, description="Response format")
    options: Optional[Dict[str, Any]] = Field(None, description="Model options")
    system: Optional[str] = Field(None, description="System message")
    template: Optional[str] = Field(None, description="Prompt template")
    context: Optional[List[int]] = Field(None, description="Context from previous request")
    stream: Optional[bool] = Field(False, description="Enable streaming")
    raw: Optional[bool] = Field(False, description="Raw mode")
    think: Optional[bool] = Field(None, description="Enable thinking for reasoning models")
    keep_alive: Optional[Union[int, str]] = Field(None, description="Keep alive duration")
    host: Optional[str] = Field(None, description="Specific host to use for this request")


class GenerateResponse(BaseModel):
    model: str
    created_at: str
    response: str
    done: bool
    context: Optional[List[int]] = None
    total_duration: Optional[int] = None
    load_duration: Optional[int] = None
    prompt_eval_count: Optional[int] = None
    prompt_eval_duration: Optional[int] = None
    eval_count: Optional[int] = None
    eval_duration: Optional[int] = None
    # Fields for reasoning models (like o1)
    thinking: Optional[str] = Field(None, description="Thinking/reasoning process for reasoning models")
    reasoning_tokens: Optional[int] = Field(None, description="Number of tokens used in reasoning")
    reasoning_duration: Optional[int] = Field(None, description="Time spent on reasoning in nanoseconds")
    # Custom field to track which server handled the request
    proxy_server: Optional[str] = Field(None, description="Server that handled the request")


class EmbedRequest(BaseModel):
    model: str = Field(..., description="Model name to use")
    prompt: str = Field(..., description="Text to embed")
    options: Optional[Dict[str, Any]] = Field(None, description="Model options")
    keep_alive: Optional[Union[int, str]] = Field(None, description="Keep alive duration")


class EmbedResponse(BaseModel):
    embedding: List[float]
    # Custom field to track which server handled the request
    proxy_server: Optional[str] = Field(None, description="Server that handled the request")

# Model management requests
class PullModelRequest(BaseModel):
    name: str = Field(..., description="Model name to pull (e.g., 'llama3.1:latest')")
    target_servers: Optional[List[str]] = Field(None, description="Specific servers to pull to (if None, pulls to all)")
    insecure: Optional[bool] = Field(False, description="Allow insecure connections")

class PullModelResponse(BaseModel):
    model: str
    status: str  # "success", "partial", "failed"
    results: Dict[str, Dict] = Field(default_factory=dict)  # server -> result
    message: str

class DeleteModelRequest(BaseModel):
    name: str = Field(..., description="Model name to delete")
    target_servers: Optional[List[str]] = Field(None, description="Specific servers to delete from (if None, deletes from all)")

class DeleteModelResponse(BaseModel):
    model: str
    status: str  # "success", "partial", "failed"
    results: Dict[str, Dict] = Field(default_factory=dict)  # server -> result
    message: str

class ModelManagementStatus(BaseModel):
    model: str
    servers: Dict[str, bool] = Field(default_factory=dict)  # server -> has_model
    total_servers: int
    servers_with_model: int


class ShowRequest(BaseModel):
    model: str = Field(..., description="Model name to show")


class ShowResponse(BaseModel):
    modelfile: str
    parameters: str
    template: str
    details: Dict[str, Any]


class ModelInfo(BaseModel):
    name: str
    model: str
    modified_at: str
    size: int
    digest: str
    details: Dict[str, Any]


class ModelsResponse(BaseModel):
    models: List[ModelInfo]


class HostConfig(BaseModel):
    id: str
    host: str
    enabled: bool = True
    weight: float = 1.0
    max_connections: int = 10
    timeout: int = 300


class ProxyStatus(BaseModel):
    total_hosts: int
    active_hosts: int
    total_requests: int
    failed_requests: int
    average_response_time: float
    hosts: List[Dict[str, Any]]


class HealthResponse(BaseModel):
    status: str
    timestamp: datetime
    version: str
    uptime: float