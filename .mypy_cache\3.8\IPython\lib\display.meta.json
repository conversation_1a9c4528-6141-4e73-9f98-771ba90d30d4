{"data_mtime": 1753839571, "dep_lines": [9, 6, 290, 339, 5, 7, 11, 134, 148, 149, 170, 192, 193, 237, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 655, 669, 668], "dep_prios": [5, 5, 20, 20, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 20, 20, 20], "dependencies": ["IPython.core.display", "os.path", "urllib.parse", "urllib.request", "html", "os", "typing", "mimetypes", "io", "wave", "numpy", "array", "sys", "base64", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "logging", "IPython.core", "_frozen_importlib", "_typeshed", "abc", "genericpath", "typing_extensions"], "hash": "b304f344e3af7879f3f7e471d97cbdf3ecf72ae7", "id": "IPython.lib.display", "ignore_all": true, "interface_hash": "0eb104c040402daa26bc7e46c923513c51e1e275", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\lib\\display.py", "plugin_data": null, "size": 24566, "suppressed": ["pygments.lexers", "pygments.formatters", "pygments"], "version_id": "1.15.0"}