{".class": "MypyFile", "_fullname": "prompt_toolkit.renderer", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AnyFormattedText": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText", "kind": "Gdef", "module_public": false}, "Application": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.application.Application", "kind": "Gdef", "module_public": false}, "Attrs": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.Attrs", "kind": "Gdef", "module_public": false}, "BaseStyle": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.BaseStyle", "kind": "Gdef", "module_public": false}, "CPR_Support": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.renderer.CPR_Support", "name": "CPR_Support", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "prompt_toolkit.renderer.CPR_Support", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "prompt_toolkit.renderer", "mro": ["prompt_toolkit.renderer.CPR_Support", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "NOT_SUPPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.renderer.CPR_Support.NOT_SUPPORTED", "name": "NOT_SUPPORTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "NOT_SUPPORTED"}, "type_ref": "builtins.str"}}}, "SUPPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.renderer.CPR_Support.SUPPORTED", "name": "SUPPORTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "SUPPORTED"}, "type_ref": "builtins.str"}}}, "UNKNOWN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.renderer.CPR_Support.UNKNOWN", "name": "UNKNOWN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "UNKNOWN"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.renderer.CPR_Support.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.renderer.CPR_Support", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Char": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.screen.Char", "kind": "Gdef", "module_public": false}, "ColorDepth": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.output.color_depth.ColorDepth", "kind": "Gdef", "module_public": false}, "CursorShape": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.cursor_shapes.CursorShape", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "DummyStyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.DummyStyleTransformation", "kind": "Gdef", "module_public": false}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_public": false}, "FIRST_COMPLETED": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.FIRST_COMPLETED", "kind": "Gdef", "module_public": false}, "FilterOrBool": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.FilterOrBool", "kind": "Gdef", "module_public": false}, "Future": {".class": "SymbolTableNode", "cross_ref": "_asyncio.Future", "kind": "Gdef", "module_public": false}, "Hashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef", "module_public": false}, "HeightIsUnknownError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.renderer.HeightIsUnknownError", "name": "HeightIsUnknownError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer.HeightIsUnknownError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.renderer", "mro": ["prompt_toolkit.renderer.HeightIsUnknownError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.renderer.HeightIsUnknownError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.renderer.HeightIsUnknownError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Layout": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.layout.Layout", "kind": "Gdef", "module_public": false}, "MouseHandlers": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "kind": "Gdef", "module_public": false}, "Output": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.output.base.Output", "kind": "Gdef", "module_public": false}, "Point": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.data_structures.Point", "kind": "Gdef", "module_public": false}, "Renderer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.renderer.Renderer", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer.Renderer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.renderer", "mro": ["prompt_toolkit.renderer.Renderer", "builtins.object"], "names": {".class": "SymbolTable", "CPR_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.renderer.Renderer.CPR_TIMEOUT", "name": "CPR_TIMEOUT", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "style", "output", "full_screen", "mouse_support", "cpr_not_supported_callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer.Renderer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "style", "output", "full_screen", "mouse_support", "cpr_not_supported_callback"], "arg_types": ["prompt_toolkit.renderer.Renderer", "prompt_toolkit.styles.base.BaseStyle", "prompt_toolkit.output.base.Output", "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Renderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_attrs_for_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._attrs_for_style", "name": "_attrs_for_style", "type": {".class": "UnionType", "items": ["prompt_toolkit.renderer._StyleStringToAttrsCache", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_bracketed_paste_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._bracketed_paste_enabled", "name": "_bracketed_paste_enabled", "type": "builtins.bool"}}, "_cursor_key_mode_reset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._cursor_key_mode_reset", "name": "_cursor_key_mode_reset", "type": "builtins.bool"}}, "_cursor_pos": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._cursor_pos", "name": "_cursor_pos", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": "prompt_toolkit.data_structures.Point"}}}, "_in_alternate_screen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._in_alternate_screen", "name": "_in_alternate_screen", "type": "builtins.bool"}}, "_last_color_depth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._last_color_depth", "name": "_last_color_depth", "type": {".class": "UnionType", "items": ["prompt_toolkit.output.color_depth.ColorDepth", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_last_cursor_shape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._last_cursor_shape", "name": "_last_cursor_shape", "type": {".class": "UnionType", "items": ["prompt_toolkit.cursor_shapes.CursorShape", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_last_screen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._last_screen", "name": "_last_screen", "type": {".class": "UnionType", "items": ["prompt_toolkit.layout.screen.Screen", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_last_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._last_size", "name": "_last_size", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Size"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_last_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._last_style", "name": "_last_style", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_last_style_hash": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._last_style_hash", "name": "_last_style_hash", "type": {".class": "UnionType", "items": ["<PERSON>.<PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_last_transformation_hash": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._last_transformation_hash", "name": "_last_transformation_hash", "type": {".class": "UnionType", "items": ["<PERSON>.<PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_min_available_height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._min_available_height", "name": "_min_available_height", "type": "builtins.int"}}, "_mouse_support_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._mouse_support_enabled", "name": "_mouse_support_enabled", "type": "builtins.bool"}}, "_style_string_has_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._style_string_has_style", "name": "_style_string_has_style", "type": {".class": "UnionType", "items": ["prompt_toolkit.renderer._StyleStringHasStyleCache", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_waiting_for_cpr_futures": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer._waiting_for_cpr_futures", "name": "_waiting_for_cpr_futures", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}], "extra_attrs": null, "type_ref": "collections.deque"}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer.Renderer.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.renderer.Renderer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cpr_not_supported_callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer.cpr_not_supported_callback", "name": "cpr_not_supported_callback", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "cpr_support": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer.cpr_support", "name": "cpr_support", "type": "prompt_toolkit.renderer.CPR_Support"}}, "erase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "leave_alternate_screen"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer.Renderer.erase", "name": "erase", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "leave_alternate_screen"], "arg_types": ["prompt_toolkit.renderer.Renderer", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "erase of Renderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "full_screen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer.full_screen", "name": "full_screen", "type": "builtins.bool"}}, "height_is_known": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.renderer.Renderer.height_is_known", "name": "height_is_known", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.renderer.Renderer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "height_is_known of <PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer.height_is_known", "name": "height_is_known", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.renderer.Renderer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "height_is_known of <PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "last_rendered_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.renderer.Renderer.last_rendered_screen", "name": "last_rendered_screen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.renderer.Renderer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "last_rendered_screen of Renderer", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.layout.screen.Screen", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer.last_rendered_screen", "name": "last_rendered_screen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.renderer.Renderer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "last_rendered_screen of Renderer", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.layout.screen.Screen", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mouse_handlers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer.mouse_handlers", "name": "mouse_handlers", "type": "prompt_toolkit.layout.mouse_handlers.MouseHandlers"}}, "mouse_support": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer.mouse_support", "name": "mouse_support", "type": "prompt_toolkit.filters.base.Filter"}}, "output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer.output", "name": "output", "type": "prompt_toolkit.output.base.Output"}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "app", "layout", "is_done"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer.Renderer.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "app", "layout", "is_done"], "arg_types": ["prompt_toolkit.renderer.Renderer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, "prompt_toolkit.layout.layout.Layout", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of Renderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "report_absolute_cursor_row": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "row"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer.Renderer.report_absolute_cursor_row", "name": "report_absolute_cursor_row", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "row"], "arg_types": ["prompt_toolkit.renderer.Renderer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "report_absolute_cursor_row of Renderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "request_absolute_cursor_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer.Renderer.request_absolute_cursor_position", "name": "request_absolute_cursor_position", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.renderer.Renderer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request_absolute_cursor_position of <PERSON><PERSON>er", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "_scroll", "leave_alternate_screen"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer.Renderer.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "_scroll", "leave_alternate_screen"], "arg_types": ["prompt_toolkit.renderer.Renderer", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of Renderer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rows_above_layout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.renderer.Renderer.rows_above_layout", "name": "rows_above_layout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.renderer.Renderer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rows_above_layout of Renderer", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer.rows_above_layout", "name": "rows_above_layout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.renderer.Renderer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rows_above_layout of Renderer", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer.style", "name": "style", "type": "prompt_toolkit.styles.base.BaseStyle"}}, "wait_for_cpr_responses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "prompt_toolkit.renderer.Renderer.wait_for_cpr_responses", "name": "wait_for_cpr_responses", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "arg_types": ["prompt_toolkit.renderer.Renderer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_for_cpr_responses of Renderer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "waiting_for_cpr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.renderer.Renderer.waiting_for_cpr", "name": "waiting_for_cpr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.renderer.Renderer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "waiting_for_cpr of Renderer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.renderer.Renderer.waiting_for_cpr", "name": "waiting_for_cpr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.renderer.Renderer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "waiting_for_cpr of Renderer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.renderer.Renderer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.renderer.Renderer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Screen": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.screen.Screen", "kind": "Gdef", "module_public": false}, "Size": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.data_structures.Size", "kind": "Gdef", "module_public": false}, "StyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.StyleTransformation", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "WritePosition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.screen.WritePosition", "kind": "Gdef", "module_public": false}, "_StyleStringHasStyleCache": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.renderer._StyleStringHasStyleCache", "name": "_StyleStringHasStyleCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer._StyleStringHasStyleCache", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.renderer", "mro": ["prompt_toolkit.renderer._StyleStringHasStyleCache", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "style_string_to_attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer._StyleStringHasStyleCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "style_string_to_attrs"], "arg_types": ["prompt_toolkit.renderer._StyleStringHasStyleCache", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _StyleStringHasStyleCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__missing__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "style_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer._StyleStringHasStyleCache.__missing__", "name": "__missing__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "style_str"], "arg_types": ["prompt_toolkit.renderer._StyleStringHasStyleCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__missing__ of _StyleStringHasStyleCache", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style_string_to_attrs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer._StyleStringHasStyleCache.style_string_to_attrs", "name": "style_string_to_attrs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.renderer._StyleStringHasStyleCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.renderer._StyleStringHasStyleCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_StyleStringToAttrsCache": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.renderer._StyleStringToAttrsCache", "name": "_StyleStringToAttrsCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer._StyleStringToAttrsCache", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.renderer", "mro": ["prompt_toolkit.renderer._StyleStringToAttrsCache", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "get_attrs_for_style_str", "style_transformation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer._StyleStringToAttrsCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "get_attrs_for_style_str", "style_transformation"], "arg_types": ["prompt_toolkit.renderer._StyleStringToAttrsCache", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "prompt_toolkit.styles.style_transformation.StyleTransformation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _StyleStringToAttrsCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__missing__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "style_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer._StyleStringToAttrsCache.__missing__", "name": "__missing__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "style_str"], "arg_types": ["prompt_toolkit.renderer._StyleStringToAttrsCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__missing__ of _StyleStringToAttrsCache", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_attrs_for_style_str": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer._StyleStringToAttrsCache.get_attrs_for_style_str", "name": "get_attrs_for_style_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.styles.base.Attrs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style_transformation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.renderer._StyleStringToAttrsCache.style_transformation", "name": "style_transformation", "type": "prompt_toolkit.styles.style_transformation.StyleTransformation"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.renderer._StyleStringToAttrsCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.renderer._StyleStringToAttrsCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.renderer.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.renderer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.renderer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.renderer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.renderer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.renderer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.renderer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_output_screen_diff": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["app", "output", "screen", "current_pos", "color_depth", "previous_screen", "last_style", "is_done", "full_screen", "attrs_for_style_string", "style_string_has_style", "size", "previous_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer._output_screen_diff", "name": "_output_screen_diff", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["app", "output", "screen", "current_pos", "color_depth", "previous_screen", "last_style", "is_done", "full_screen", "attrs_for_style_string", "style_string_has_style", "size", "previous_width"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, "prompt_toolkit.output.base.Output", "prompt_toolkit.layout.screen.Screen", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, "prompt_toolkit.output.color_depth.ColorDepth", {".class": "UnionType", "items": ["prompt_toolkit.layout.screen.Screen", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "prompt_toolkit.renderer._StyleStringToAttrsCache", "prompt_toolkit.renderer._StyleStringHasStyleCache", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Size"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_output_screen_diff", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef", "module_public": false}, "ensure_future": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.ensure_future", "kind": "Gdef", "module_public": false}, "get_app": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app", "kind": "Gdef", "module_public": false}, "print_formatted_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["output", "formatted_text", "style", "style_transformation", "color_depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.renderer.print_formatted_text", "name": "print_formatted_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["output", "formatted_text", "style", "style_transformation", "color_depth"], "arg_types": ["prompt_toolkit.output.base.Output", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "prompt_toolkit.styles.base.BaseStyle", {".class": "UnionType", "items": ["prompt_toolkit.styles.style_transformation.StyleTransformation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.output.color_depth.ColorDepth", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_formatted_text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sleep": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.sleep", "kind": "Gdef", "module_public": false}, "to_filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.to_filter", "kind": "Gdef", "module_public": false}, "to_formatted_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.to_formatted_text", "kind": "Gdef", "module_public": false}, "wait": {".class": "SymbolTableNode", "cross_ref": "asyncio.tasks.wait", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\renderer.py"}