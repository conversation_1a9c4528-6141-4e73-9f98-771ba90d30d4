{".class": "MypyFile", "_fullname": "structlog.stdlib", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AsyncBoundLogger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.stdlib.AsyncBoundLogger", "name": "AsyncBoundLogger", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "structlog.stdlib.AsyncBoundLogger", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.stdlib", "mro": ["structlog.stdlib.AsyncBoundLogger", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5], "arg_names": ["self", "logger", "processors", "context", "_sync_bl", "_loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.AsyncBoundLogger.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5], "arg_names": ["self", "logger", "processors", "context", "_sync_bl", "_loop"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", "logging.Logger", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Context"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncBoundLogger", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "structlog.stdlib.AsyncBoundLogger.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_bound_logger_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.stdlib.AsyncBoundLogger._bound_logger_factory", "name": "_bound_logger_factory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["logger", "processors", "context"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Context"}], "bound_args": ["structlog.stdlib.BoundLogger"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "structlog.stdlib.BoundLogger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.stdlib.AsyncBoundLogger._context", "name": "_context", "type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Context"}}}, "_dispatch_to_sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "meth", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.AsyncBoundLogger._dispatch_to_sync", "name": "_dispatch_to_sync", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "meth", "event", "args", "kw"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dispatch_to_sync of AsyncBoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_executor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.stdlib.AsyncBoundLogger._executor", "name": "_executor", "type": {".class": "NoneType"}}}, "_loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.stdlib.AsyncBoundLogger._loop", "name": "_loop", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.AsyncBoundLogger.bind", "name": "bind", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind of AsyncBoundLogger", "ret_type": "structlog.stdlib.AsyncBoundLogger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "critical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.AsyncBoundLogger.critical", "name": "critical", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "critical of AsyncBoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.AsyncBoundLogger.debug", "name": "debug", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug of AsyncBoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.AsyncBoundLogger.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "error of AsyncBoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.AsyncBoundLogger.exception", "name": "exception", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exception of AsyncBoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fatal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.AsyncBoundLogger.fatal", "name": "fatal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fatal of <PERSON><PERSON><PERSON><PERSON>nd<PERSON><PERSON>ger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.AsyncBoundLogger.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of AsyncBoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "level", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.AsyncBoundLogger.log", "name": "log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "level", "event", "args", "kw"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log of AsyncBoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.AsyncBoundLogger.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of AsyncBoundLog<PERSON>", "ret_type": "structlog.stdlib.AsyncBoundLogger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sync_bl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.stdlib.AsyncBoundLogger.sync_bl", "name": "sync_bl", "type": "structlog.stdlib.BoundLogger"}}, "try_unbind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.AsyncBoundLogger.try_unbind", "name": "try_unbind", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "try_unbind of AsyncBoundLogger", "ret_type": "structlog.stdlib.AsyncBoundLogger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unbind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.AsyncBoundLogger.unbind", "name": "unbind", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unbind of AsyncBoundLogger", "ret_type": "structlog.stdlib.AsyncBoundLogger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.AsyncBoundLogger.warn", "name": "warn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warn of AsyncBoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.AsyncBoundLogger.warning", "name": "warning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.AsyncBoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warning of AsyncBoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.AsyncBoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.AsyncBoundLogger", "values": [], "variance": 0}, "slots": ["_loop", "sync_bl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BoundLogger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["structlog._base.BoundLoggerBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.stdlib.BoundLogger", "name": "BoundLogger", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.stdlib", "mro": ["structlog.stdlib.BoundLogger", "structlog._base.BoundLoggerBase", "builtins.object"], "names": {".class": "SymbolTable", "_dispatch_to_sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "meth", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.BoundLogger._dispatch_to_sync", "name": "_dispatch_to_sync", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "meth", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dispatch_to_sync of BoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_logger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "structlog.stdlib.BoundLogger._logger", "name": "_logger", "type": "logging.Logger"}}, "_proxy_to_logger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "method_name", "event", "event_args", "event_kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger._proxy_to_logger", "name": "_proxy_to_logger", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "method_name", "event", "event_args", "event_kw"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_proxy_to_logger of BoundLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "acritical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.BoundLogger.acritical", "name": "acritical", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "acritical of BoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "addHandler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hdlr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.addHandler", "name": "add<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hdlr"], "arg_types": ["structlog.stdlib.BoundLogger", "logging.Handler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add<PERSON><PERSON><PERSON> of BoundLogger", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adebug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.BoundLogger.adebug", "name": "adeb<PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adebug of BoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aerror": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.BoundLogger.aerror", "name": "aerror", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aerror of BoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aexception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.BoundLogger.aexception", "name": "aexception", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aexception of BoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "afatal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.BoundLogger.afatal", "name": "a<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "afatal of BoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ainfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.BoundLogger.ainfo", "name": "ainfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ainfo of BoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alog": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "level", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.BoundLogger.alog", "name": "alog", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "level", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alog of BoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "awarning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "structlog.stdlib.BoundLogger.awarning", "name": "awarning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "awarning of BoundLogger", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.bind", "name": "bind", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind of BoundLogger", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}]}}}, "callHandlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.callHandlers", "name": "callHandlers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["structlog.stdlib.BoundLogger", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "callHandlers of BoundLogger", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "critical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.critical", "name": "critical", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "critical of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.debug", "name": "debug", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug of BoundLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "disabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "structlog.stdlib.BoundLogger.disabled", "name": "disabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disabled of BoundLogger", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.BoundLogger.disabled", "name": "disabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disabled of BoundLogger", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "error of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.exception", "name": "exception", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exception of BoundLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fatal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.fatal", "name": "fatal", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fatal of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "findCaller": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "stack_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.findCaller", "name": "findCaller", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "stack_info"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findCaller of BoundLogger", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getChild": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.getChild", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "suffix"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON> of BoundLogger", "ret_type": "logging.Logger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getEffectiveLevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.getEffectiveLevel", "name": "getEffectiveLevel", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getEffectiveLevel of BoundLogger", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.handle", "name": "handle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["structlog.stdlib.BoundLogger", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle of BoundLogger", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "structlog.stdlib.BoundLogger.handlers", "name": "handlers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handlers of BoundLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.BoundLogger.handlers", "name": "handlers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handlers of BoundLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hasHandlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.hasHandlers", "name": "hasHandlers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hasHandlers of BoundLogger", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of BoundLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isEnabledFor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.isEnabledFor", "name": "isEnabledFor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "level"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isEnabledFor of BoundLogger", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "structlog.stdlib.BoundLogger.level", "name": "level", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "level of BoundLogger", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.BoundLogger.level", "name": "level", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "level of BoundLogger", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "level", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.log", "name": "log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "level", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log of BoundLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "makeRecord": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "name", "level", "fn", "lno", "msg", "args", "exc_info", "func", "extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.makeRecord", "name": "makeRecord", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "name", "level", "fn", "lno", "msg", "args", "exc_info", "func", "extra"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.str", "builtins.int", "builtins.str", "builtins.int", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.ExcInfo"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makeRecord of BoundLogger", "ret_type": "logging.LogRecord", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "structlog.stdlib.BoundLogger.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.BoundLogger.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "new_values"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of BoundLogger", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}]}}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "structlog.stdlib.BoundLogger.parent", "name": "parent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parent of BoundLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.BoundLogger.parent", "name": "parent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parent of BoundLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "propagate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "structlog.stdlib.BoundLogger.propagate", "name": "propagate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "propagate of BoundLogger", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.BoundLogger.propagate", "name": "propagate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["structlog.stdlib.BoundLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "propagate of BoundLogger", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "removeHandler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hdlr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.removeHandler", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hdlr"], "arg_types": ["structlog.stdlib.BoundLogger", "logging.Handler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON> of BoundLogger", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setLevel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.setLevel", "name": "setLevel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "level"], "arg_types": ["structlog.stdlib.BoundLogger", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setLevel of BoundLogger", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "try_unbind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.try_unbind", "name": "try_unbind", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "try_unbind of BoundLogger", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}]}}}, "unbind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.unbind", "name": "unbind", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unbind of BoundLogger", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}]}}}, "warn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "structlog.stdlib.BoundLogger.warn", "name": "warn", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.BoundLogger.warning", "name": "warning", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "event", "args", "kw"], "arg_types": ["structlog.stdlib.BoundLogger", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warning of <PERSON>und<PERSON>og<PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.BoundLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.BoundLogger", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BoundLoggerBase": {".class": "SymbolTableNode", "cross_ref": "structlog._base.BoundLoggerBase", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_public": false}, "Context": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.Context", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "DropEvent": {".class": "SymbolTableNode", "cross_ref": "structlog.exceptions.DropEvent", "kind": "Gdef", "module_public": false}, "EventDict": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.EventDict", "kind": "Gdef", "module_public": false}, "ExcInfo": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.ExcInfo", "kind": "Gdef", "module_public": false}, "ExtraAdder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.stdlib.ExtraAdder", "name": "ExtraAdder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "structlog.stdlib.ExtraAdder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.stdlib", "mro": ["structlog.stdlib.ExtraAdder", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.ExtraAdder.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logger", "name", "event_dict"], "arg_types": ["structlog.stdlib.ExtraAdder", "logging.Logger", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ExtraAdder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "allow"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.ExtraAdder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "allow"], "arg_types": ["structlog.stdlib.ExtraAdder", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExtraAdder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "structlog.stdlib.ExtraAdder.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_copier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.ExtraAdder._copier", "name": "_copier", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "logging.LogRecord"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "event_dict", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "structlog.stdlib.ExtraAdder._copy_all", "name": "_copy_all", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "event_dict", "record"], "arg_types": [{".class": "TypeType", "item": "structlog.stdlib.ExtraAdder"}, {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_all of ExtraAdder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.ExtraAdder._copy_all", "name": "_copy_all", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "event_dict", "record"], "arg_types": [{".class": "TypeType", "item": "structlog.stdlib.ExtraAdder"}, {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_all of ExtraAdder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_copy_allowed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "allow", "event_dict", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "structlog.stdlib.ExtraAdder._copy_allowed", "name": "_copy_allowed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "allow", "event_dict", "record"], "arg_types": [{".class": "TypeType", "item": "structlog.stdlib.ExtraAdder"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_allowed of ExtraAdder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.ExtraAdder._copy_allowed", "name": "_copy_allowed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "allow", "event_dict", "record"], "arg_types": [{".class": "TypeType", "item": "structlog.stdlib.ExtraAdder"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_allowed of ExtraAdder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.ExtraAdder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.ExtraAdder", "values": [], "variance": 0}, "slots": ["_copier"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "LEVEL_TO_NAME": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.LEVEL_TO_NAME", "kind": "Gdef", "module_public": false}, "LOG_KWARG_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.stdlib.LOG_KWARG_NAMES", "name": "LOG_KWARG_NAMES", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "LoggerFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.stdlib.LoggerFactory", "name": "LoggerFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "structlog.stdlib.LoggerFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.stdlib", "mro": ["structlog.stdlib.LoggerFactory", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.LoggerFactory.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["structlog.stdlib.LoggerFactory", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of LoggerFactory", "ret_type": "logging.Logger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "ignore_frame_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.LoggerFactory.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "ignore_frame_names"], "arg_types": ["structlog.stdlib.LoggerFactory", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LoggerFactory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ignore": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.stdlib.LoggerFactory._ignore", "name": "_ignore", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.LoggerFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.LoggerFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NAME_TO_LEVEL": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.NAME_TO_LEVEL", "kind": "Gdef", "module_public": false}, "PositionalArgumentsFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.stdlib.PositionalArgumentsFormatter", "name": "PositionalArgumentsFormatter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "structlog.stdlib.PositionalArgumentsFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.stdlib", "mro": ["structlog.stdlib.PositionalArgumentsFormatter", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_", "__", "event_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.PositionalArgumentsFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_", "__", "event_dict"], "arg_types": ["structlog.stdlib.PositionalArgumentsFormatter", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of PositionalArgumentsFormatter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "remove_positional_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.PositionalArgumentsFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "remove_positional_args"], "arg_types": ["structlog.stdlib.PositionalArgumentsFormatter", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PositionalArgumentsFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_positional_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.stdlib.PositionalArgumentsFormatter.remove_positional_args", "name": "remove_positional_args", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.PositionalArgumentsFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.PositionalArgumentsFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Processor": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.Processor", "kind": "Gdef", "module_public": false}, "ProcessorFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["logging.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.stdlib.ProcessorFormatter", "name": "ProcessorFormatter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "structlog.stdlib.ProcessorFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.stdlib", "mro": ["structlog.stdlib.ProcessorFormatter", "logging.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 2, 4], "arg_names": ["self", "processor", "processors", "foreign_pre_chain", "keep_exc_info", "keep_stack_info", "logger", "pass_foreign_args", "use_get_message", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.ProcessorFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 2, 4], "arg_names": ["self", "processor", "processors", "foreign_pre_chain", "keep_exc_info", "keep_stack_info", "logger", "pass_foreign_args", "use_get_message", "args", "kwargs"], "arg_types": ["structlog.stdlib.ProcessorFormatter", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProcessorFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "foreign_pre_chain": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.stdlib.ProcessorFormatter.foreign_pre_chain", "name": "foreign_pre_chain", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.ProcessorFormatter.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record"], "arg_types": ["structlog.stdlib.ProcessorFormatter", "logging.LogRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of ProcessorFormatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keep_exc_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.stdlib.ProcessorFormatter.keep_exc_info", "name": "keep_exc_info", "type": "builtins.bool"}}, "keep_stack_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.stdlib.ProcessorFormatter.keep_stack_info", "name": "keep_stack_info", "type": "builtins.bool"}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.stdlib.ProcessorFormatter.logger", "name": "logger", "type": {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "pass_foreign_args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.stdlib.ProcessorFormatter.pass_foreign_args", "name": "pass_foreign_args", "type": "builtins.bool"}}, "processors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.ProcessorFormatter.processors", "name": "processors", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.Processor"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "remove_processors_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_", "__", "event_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "structlog.stdlib.ProcessorFormatter.remove_processors_meta", "name": "remove_processors_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_", "__", "event_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_processors_meta of ProcessorFormatter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.ProcessorFormatter.remove_processors_meta", "name": "remove_processors_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_", "__", "event_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.WrappedLogger"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_processors_meta of ProcessorFormatter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "use_get_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "structlog.stdlib.ProcessorFormatter.use_get_message", "name": "use_get_message", "type": "builtins.bool"}}, "wrap_for_formatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["logger", "name", "event_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "structlog.stdlib.ProcessorFormatter.wrap_for_formatter", "name": "wrap_for_formatter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["logger", "name", "event_dict"], "arg_types": ["logging.Logger", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_for_formatter of ProcessorFormatter", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "structlog.stdlib.ProcessorFormatter.wrap_for_formatter", "name": "wrap_for_formatter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["logger", "name", "event_dict"], "arg_types": ["logging.Logger", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_for_formatter of ProcessorFormatter", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib.ProcessorFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib.ProcessorFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProcessorReturnValue": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.ProcessorReturnValue", "kind": "Gdef", "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "StackInfoRenderer": {".class": "SymbolTableNode", "cross_ref": "structlog.processors.StackInfoRenderer", "kind": "Gdef", "module_public": false}, "WrappedLogger": {".class": "SymbolTableNode", "cross_ref": "structlog.typing.WrappedLogger", "kind": "Gdef", "module_public": false}, "_ASYNC_CALLING_STACK": {".class": "SymbolTableNode", "cross_ref": "structlog.contextvars._ASYNC_CALLING_STACK", "kind": "Gdef", "module_public": false}, "_FixedFindCallerLogger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["logging.Logger"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "structlog.stdlib._FixedFindCallerLogger", "name": "_FixedFindCallerLogger", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "structlog.stdlib._FixedFindCallerLogger", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "structlog.stdlib", "mro": ["structlog.stdlib._FixedFindCallerLogger", "logging.Logger", "<PERSON>.Filterer", "builtins.object"], "names": {".class": "SymbolTable", "findCaller": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "stack_info", "stacklevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib._FixedFindCallerLogger.findCaller", "name": "findCaller", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "stack_info", "stacklevel"], "arg_types": ["structlog.stdlib._FixedFindCallerLogger", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "findCaller of _FixedFindCallerLogger", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "structlog.stdlib._FixedFindCallerLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "structlog.stdlib._FixedFindCallerLogger", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LOG_RECORD_KEYS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.stdlib._LOG_RECORD_KEYS", "name": "_LOG_RECORD_KEYS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_collections_abc.dict_keys"}}}, "_SENTINEL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.stdlib._SENTINEL", "name": "_SENTINEL", "type": "builtins.object"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "structlog.stdlib.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.stdlib.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.stdlib.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.stdlib.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.stdlib.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.stdlib.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "structlog.stdlib.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_config": {".class": "SymbolTableNode", "cross_ref": "structlog._config", "kind": "Gdef", "module_public": false}, "_find_first_app_frame_and_name": {".class": "SymbolTableNode", "cross_ref": "structlog._frames._find_first_app_frame_and_name", "kind": "Gdef", "module_public": false}, "_format_stack": {".class": "SymbolTableNode", "cross_ref": "structlog._frames._format_stack", "kind": "Gdef", "module_public": false}, "add_log_level": {".class": "SymbolTableNode", "cross_ref": "structlog._log_levels.add_log_level", "kind": "Gdef"}, "add_log_level_number": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["logger", "method_name", "event_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.add_log_level_number", "name": "add_log_level_number", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["logger", "method_name", "event_dict"], "arg_types": ["logging.Logger", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_log_level_number", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_logger_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["logger", "method_name", "event_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.add_logger_name", "name": "add_logger_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["logger", "method_name", "event_dict"], "arg_types": ["logging.Logger", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_logger_name", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "contextvars": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "filter_by_level": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["logger", "method_name", "event_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.filter_by_level", "name": "filter_by_level", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["logger", "method_name", "event_dict"], "arg_types": ["logging.Logger", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_by_level", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "get_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "initial_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.get_logger", "name": "get_logger", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "initial_values"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_logger", "ret_type": "structlog.stdlib.BoundLogger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "merge_contextvars": {".class": "SymbolTableNode", "cross_ref": "structlog.contextvars.merge_contextvars", "kind": "Gdef", "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_public": false}, "recreate_defaults": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["log_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.recreate_defaults", "name": "recreate_defaults", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["log_level"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recreate_defaults", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_to_log_args_and_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_", "__", "event_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.render_to_log_args_and_kwargs", "name": "render_to_log_args_and_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_", "__", "event_dict"], "arg_types": ["logging.Logger", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_to_log_args_and_kwargs", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_to_log_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_", "__", "event_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "structlog.stdlib.render_to_log_kwargs", "name": "render_to_log_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_", "__", "event_dict"], "arg_types": ["logging.Logger", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_to_log_kwargs", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "structlog.typing.EventDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\structlog\\stdlib.py"}