{"data_mtime": 1753839580, "dep_lines": [28, 29, 30, 31, 32, 33, 34, 35, 38, 23, 24, 16, 17, 18, 19, 20, 21, 22, 25, 36, 37, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.error", "IPython.core.macro", "IPython.core.magic", "IPython.core.oinspect", "IPython.core.release", "IPython.testing.skipdoctest", "IPython.utils.contexts", "IPython.utils.path", "IPython.utils.text", "urllib.request", "urllib.parse", "inspect", "io", "os", "re", "sys", "ast", "itertools", "pathlib", "warnings", "logging", "builtins", "json", "traitlets.utils.warnings", "pprint", "typing", "functools", "IPython.testing", "_frozen_importlib", "abc", "enum", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets"], "hash": "56bd319b77a62e2bd21c1aa4e881e9b07fce4145", "id": "IPython.core.magics.code", "ignore_all": true, "interface_hash": "9ed2a403e061e57fa178054b96476151a2b001cc", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\magics\\code.py", "plugin_data": null, "size": 28051, "suppressed": [], "version_id": "1.15.0"}