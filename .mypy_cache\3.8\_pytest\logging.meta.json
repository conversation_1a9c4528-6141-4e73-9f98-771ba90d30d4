{"data_mtime": 1753839573, "dep_lines": [38, 30, 31, 32, 33, 39, 40, 42, 43, 44, 4, 6, 8, 11, 13, 15, 16, 17, 18, 19, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 5, 20, 5, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.nodes", "_pytest._io", "_pytest.capture", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.main", "_pytest.stash", "_pytest.terminal", "__future__", "contextlib", "datetime", "io", "logging", "os", "pathlib", "re", "types", "typing", "_pytest", "builtins", "json", "traitlets.utils.warnings", "pprint", "functools", "sys", "_collections_abc", "_frozen_importlib", "_io", "_pytest._io.terminalwriter", "_typeshed", "abc", "<PERSON><PERSON><PERSON><PERSON>", "enum", "pluggy", "pluggy._hooks", "pluggy._manager"], "hash": "796fee4c7212de41dc1f66971473b12c67d65d2b", "id": "_pytest.logging", "ignore_all": true, "interface_hash": "47efc78debdd555713c7886c6bde496de5b6948c", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\logging.py", "plugin_data": null, "size": 35124, "suppressed": [], "version_id": "1.15.0"}