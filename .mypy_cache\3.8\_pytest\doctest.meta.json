{"data_mtime": 1753839573, "dep_lines": [26, 32, 25, 29, 30, 31, 33, 35, 39, 40, 41, 42, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 23, 25, 46, 48, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 10, 5, 10, 10, 10, 10, 5, 10, 20, 20, 25, 5, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.outcomes", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.fixtures", "_pytest.nodes", "_pytest.pathlib", "_pytest.python", "_pytest.python_api", "_pytest.warning_types", "__future__", "bdb", "contextlib", "functools", "inspect", "os", "pathlib", "platform", "sys", "traceback", "types", "typing", "warnings", "_pytest", "doctest", "typing_extensions", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "logging", "_frozen_importlib", "_pytest._code", "_pytest._io.terminalwriter", "_pytest.main", "abc"], "hash": "688d23d961c19a846e771c4b2ff55a33dfefdf0d", "id": "_pytest.doctest", "ignore_all": true, "interface_hash": "d12d4509be80f4f808573e369d01a4751f385dbe", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\doctest.py", "plugin_data": null, "size": 26255, "suppressed": [], "version_id": "1.15.0"}