{".class": "MypyFile", "_fullname": "prompt_toolkit.application", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AppSession": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.AppSession", "kind": "Gdef"}, "Application": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.application.Application", "kind": "Gdef"}, "DummyApplication": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.dummy.DummyApplication", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.application.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.application.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.application.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.application.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.application.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.application.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.application.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.application.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "create_app_session": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.create_app_session", "kind": "Gdef"}, "create_app_session_from_tty": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.create_app_session_from_tty", "kind": "Gdef"}, "get_app": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app", "kind": "Gdef"}, "get_app_or_none": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app_or_none", "kind": "Gdef"}, "get_app_session": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app_session", "kind": "Gdef"}, "in_terminal": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.run_in_terminal.in_terminal", "kind": "Gdef"}, "run_in_terminal": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.run_in_terminal.run_in_terminal", "kind": "Gdef"}, "set_app": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.set_app", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\application\\__init__.py"}