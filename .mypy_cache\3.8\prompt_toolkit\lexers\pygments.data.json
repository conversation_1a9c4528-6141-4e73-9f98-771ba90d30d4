{".class": "MypyFile", "_fullname": "prompt_toolkit.lexers.pygments", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Document": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.document.Document", "kind": "Gdef", "module_public": false}, "FilterOrBool": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.FilterOrBool", "kind": "Gdef", "module_public": false}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Lexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.base.Lexer", "kind": "Gdef", "module_public": false}, "PygmentsLexer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.lexers.base.Lexer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer", "name": "PygmentsLexer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.lexers.pygments", "mro": ["prompt_toolkit.lexers.pygments.PygmentsLexer", "prompt_toolkit.lexers.base.Lexer", "builtins.object"], "names": {".class": "SymbolTable", "MIN_LINES_BACKWARDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer.MIN_LINES_BACKWARDS", "name": "MIN_LINES_BACKWARDS", "type": "builtins.int"}}, "REUSE_GENERATOR_MAX_DISTANCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer.REUSE_GENERATOR_MAX_DISTANCE", "name": "REUSE_GENERATOR_MAX_DISTANCE", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "pygments_lexer_cls", "sync_from_start", "syntax_sync"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "pygments_lexer_cls", "sync_from_start", "syntax_sync"], "arg_types": ["prompt_toolkit.lexers.pygments.PygmentsLexer", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": "prompt_toolkit.lexers.pygments.PygmentsLexerCls", "source_any": null, "type_of_any": 3}}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": ["prompt_toolkit.lexers.pygments.SyntaxSync", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PygmentsLexer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filename", "sync_from_start"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer.from_filename", "name": "from_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filename", "sync_from_start"], "arg_types": [{".class": "TypeType", "item": "prompt_toolkit.lexers.pygments.PygmentsLexer"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_filename of PygmentsLexer", "ret_type": "prompt_toolkit.lexers.base.Lexer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer.from_filename", "name": "from_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "filename", "sync_from_start"], "arg_types": [{".class": "TypeType", "item": "prompt_toolkit.lexers.pygments.PygmentsLexer"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_filename of PygmentsLexer", "ret_type": "prompt_toolkit.lexers.base.Lexer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "lex_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer.lex_document", "name": "lex_document", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document"], "arg_types": ["prompt_toolkit.lexers.pygments.PygmentsLexer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lex_document of PygmentsLexer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pygments_lexer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer.pygments_lexer", "name": "pygments_lexer", "type": {".class": "AnyType", "missing_import_name": "prompt_toolkit.lexers.pygments.PygmentsLexerCls", "source_any": {".class": "AnyType", "missing_import_name": "prompt_toolkit.lexers.pygments.PygmentsLexerCls", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "pygments_lexer_cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer.pygments_lexer_cls", "name": "pygments_lexer_cls", "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": "prompt_toolkit.lexers.pygments.PygmentsLexerCls", "source_any": null, "type_of_any": 3}}}}, "sync_from_start": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer.sync_from_start", "name": "sync_from_start", "type": "prompt_toolkit.filters.base.Filter"}}, "syntax_sync": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer.syntax_sync", "name": "syntax_sync", "type": "prompt_toolkit.lexers.pygments.SyntaxSync"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.lexers.pygments.PygmentsLexer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PygmentsLexerCls": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.lexers.pygments.PygmentsLexerCls", "name": "PygmentsLexerCls", "type": {".class": "AnyType", "missing_import_name": "prompt_toolkit.lexers.pygments.PygmentsLexerCls", "source_any": null, "type_of_any": 3}}}, "RegexSync": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.lexers.pygments.SyntaxSync"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.lexers.pygments.RegexSync", "name": "RegexSync", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.pygments.RegexSync", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.lexers.pygments", "mro": ["prompt_toolkit.lexers.pygments.RegexSync", "prompt_toolkit.lexers.pygments.SyntaxSync", "builtins.object"], "names": {".class": "SymbolTable", "FROM_START_IF_NO_SYNC_POS_FOUND": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.lexers.pygments.RegexSync.FROM_START_IF_NO_SYNC_POS_FOUND", "name": "FROM_START_IF_NO_SYNC_POS_FOUND", "type": "builtins.int"}}, "MAX_BACKWARDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.lexers.pygments.RegexSync.MAX_BACKWARDS", "name": "MAX_BACKWARDS", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.pygments.RegexSync.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pattern"], "arg_types": ["prompt_toolkit.lexers.pygments.RegexSync", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RegexSync", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compiled_pattern": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.lexers.pygments.RegexSync._compiled_pattern", "name": "_compiled_pattern", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "from_pygments_lexer_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "lexer_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "prompt_toolkit.lexers.pygments.RegexSync.from_pygments_lexer_cls", "name": "from_pygments_lexer_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "lexer_cls"], "arg_types": [{".class": "TypeType", "item": "prompt_toolkit.lexers.pygments.RegexSync"}, {".class": "AnyType", "missing_import_name": "prompt_toolkit.lexers.pygments.PygmentsLexerCls", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_pygments_lexer_cls of RegexSync", "ret_type": "prompt_toolkit.lexers.pygments.RegexSync", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.lexers.pygments.RegexSync.from_pygments_lexer_cls", "name": "from_pygments_lexer_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "lexer_cls"], "arg_types": [{".class": "TypeType", "item": "prompt_toolkit.lexers.pygments.RegexSync"}, {".class": "AnyType", "missing_import_name": "prompt_toolkit.lexers.pygments.PygmentsLexerCls", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_pygments_lexer_cls of RegexSync", "ret_type": "prompt_toolkit.lexers.pygments.RegexSync", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_sync_start_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.pygments.RegexSync.get_sync_start_position", "name": "get_sync_start_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "lineno"], "arg_types": ["prompt_toolkit.lexers.pygments.RegexSync", "prompt_toolkit.document.Document", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sync_start_position of RegexSync", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.lexers.pygments.RegexSync.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.lexers.pygments.RegexSync", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleLexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.base.SimpleLexer", "kind": "Gdef", "module_public": false}, "StyleAndTextTuples": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples", "kind": "Gdef", "module_public": false}, "SyncFromStart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.lexers.pygments.SyntaxSync"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.lexers.pygments.SyncFromStart", "name": "SyncFromStart", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.pygments.SyncFromStart", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.lexers.pygments", "mro": ["prompt_toolkit.lexers.pygments.SyncFromStart", "prompt_toolkit.lexers.pygments.SyntaxSync", "builtins.object"], "names": {".class": "SymbolTable", "get_sync_start_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.pygments.SyncFromStart.get_sync_start_position", "name": "get_sync_start_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "lineno"], "arg_types": ["prompt_toolkit.lexers.pygments.SyncFromStart", "prompt_toolkit.document.Document", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sync_start_position of SyncFromStart", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.lexers.pygments.SyncFromStart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.lexers.pygments.SyncFromStart", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SyntaxSync": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_sync_start_position", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.lexers.pygments.SyntaxSync", "name": "SyntaxSync", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "prompt_toolkit.lexers.pygments.SyntaxSync", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.lexers.pygments", "mro": ["prompt_toolkit.lexers.pygments.SyntaxSync", "builtins.object"], "names": {".class": "SymbolTable", "get_sync_start_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "prompt_toolkit.lexers.pygments.SyntaxSync.get_sync_start_position", "name": "get_sync_start_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "lineno"], "arg_types": ["prompt_toolkit.lexers.pygments.SyntaxSync", "prompt_toolkit.document.Document", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sync_start_position of SyntaxSync", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.lexers.pygments.SyntaxSync.get_sync_start_position", "name": "get_sync_start_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "lineno"], "arg_types": ["prompt_toolkit.lexers.pygments.SyntaxSync", "prompt_toolkit.document.Document", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sync_start_position of SyntaxSync", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.lexers.pygments.SyntaxSync.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.lexers.pygments.SyntaxSync", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "_TokenCache": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.lexers.pygments._TokenCache", "name": "_TokenCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.pygments._TokenCache", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.lexers.pygments", "mro": ["prompt_toolkit.lexers.pygments._TokenCache", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__missing__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.pygments._TokenCache.__missing__", "name": "__missing__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["prompt_toolkit.lexers.pygments._TokenCache", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__missing__ of _TokenCache", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.lexers.pygments._TokenCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.lexers.pygments._TokenCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.lexers.pygments.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.pygments.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.pygments.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.pygments.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.pygments.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.pygments.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.pygments.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_token_cache": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.lexers.pygments._token_cache", "name": "_token_cache", "type": "prompt_toolkit.lexers.pygments._TokenCache"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "pygments_token_to_classname": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.pygments.pygments_token_to_classname", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "split_lines": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.utils.split_lines", "kind": "Gdef", "module_public": false}, "to_filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.to_filter", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\lexers\\pygments.py"}