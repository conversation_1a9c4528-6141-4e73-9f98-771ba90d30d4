{"data_mtime": 1753839580, "dep_lines": [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 1641, 1642, 1643, 1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 42, 1638, 1639, 1677, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["rich.filesize", "rich.console", "rich.highlighter", "rich.jupyter", "rich.live", "rich.progress_bar", "rich.spinner", "rich.style", "rich.table", "rich.text", "rich.panel", "rich.rule", "rich.syntax", "io", "sys", "typing", "warnings", "abc", "collections", "dataclasses", "datetime", "math", "mmap", "operator", "os", "threading", "types", "rich", "random", "time", "itertools", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "logging", "_frozen_importlib", "_io", "_thread", "_typeshed", "contextlib", "enum", "rich.box", "rich.theme", "typing_extensions"], "hash": "6522929b0ae03370600e68e461c9689f08f6760a", "id": "rich.progress", "ignore_all": true, "interface_hash": "5093c0fc2f71f9051b974dc93de731b606f34b52", "mtime": 1723156628, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\rich\\progress.py", "plugin_data": null, "size": 59703, "suppressed": [], "version_id": "1.15.0"}