{".class": "MypyFile", "_fullname": "pytest", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Cache": {".class": "SymbolTableNode", "cross_ref": "_pytest.cacheprovider.Cache", "kind": "Gdef"}, "CallInfo": {".class": "SymbolTableNode", "cross_ref": "_pytest.runner.CallInfo", "kind": "Gdef"}, "CaptureFixture": {".class": "SymbolTableNode", "cross_ref": "_pytest.capture.CaptureFixture", "kind": "Gdef"}, "Class": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.Class", "kind": "Gdef"}, "CollectReport": {".class": "SymbolTableNode", "cross_ref": "_pytest.reports.CollectReport", "kind": "Gdef"}, "Collector": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes.Collector", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.Config", "kind": "Gdef"}, "Dir": {".class": "SymbolTableNode", "cross_ref": "_pytest.main.Dir", "kind": "Gdef"}, "Directory": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes.Directory", "kind": "Gdef"}, "DoctestItem": {".class": "SymbolTableNode", "cross_ref": "_pytest.doctest.DoctestItem", "kind": "Gdef"}, "ExceptionInfo": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.ExceptionInfo", "kind": "Gdef"}, "ExitCode": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.ExitCode", "kind": "Gdef"}, "File": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes.File", "kind": "Gdef"}, "FixtureDef": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.FixtureDef", "kind": "Gdef"}, "FixtureLookupError": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.FixtureLookupError", "kind": "Gdef"}, "FixtureRequest": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.FixtureRequest", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.Function", "kind": "Gdef"}, "HookRecorder": {".class": "SymbolTableNode", "cross_ref": "_pytest.pytester.HookRecorder", "kind": "Gdef"}, "Item": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes.Item", "kind": "Gdef"}, "LineMatcher": {".class": "SymbolTableNode", "cross_ref": "_pytest.pytester.LineMatcher", "kind": "Gdef"}, "LogCaptureFixture": {".class": "SymbolTableNode", "cross_ref": "_pytest.logging.LogCaptureFixture", "kind": "Gdef"}, "Mark": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.Mark", "kind": "Gdef"}, "MarkDecorator": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.MarkDecorator", "kind": "Gdef"}, "MarkGenerator": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.MarkGenerator", "kind": "Gdef"}, "Metafunc": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.Metafunc", "kind": "Gdef"}, "Module": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.Module", "kind": "Gdef"}, "MonkeyPatch": {".class": "SymbolTableNode", "cross_ref": "_pytest.monkeypatch.MonkeyPatch", "kind": "Gdef"}, "OptionGroup": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.argparsing.OptionGroup", "kind": "Gdef"}, "Package": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.Package", "kind": "Gdef"}, "Parser": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.argparsing.Parser", "kind": "Gdef"}, "PytestAssertRewriteWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestAssertRewriteWarning", "kind": "Gdef"}, "PytestCacheWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestCacheWarning", "kind": "Gdef"}, "PytestCollectionWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestCollectionWarning", "kind": "Gdef"}, "PytestConfigWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestConfigWarning", "kind": "Gdef"}, "PytestDeprecationWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestDeprecationWarning", "kind": "Gdef"}, "PytestExperimentalApiWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestExperimentalApiWarning", "kind": "Gdef"}, "PytestPluginManager": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.PytestPluginManager", "kind": "Gdef"}, "PytestRemovedIn9Warning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestRemovedIn9Warning", "kind": "Gdef"}, "PytestReturnNotNoneWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestReturnNotNoneWarning", "kind": "Gdef"}, "PytestUnhandledCoroutineWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestUnhandledCoroutineWarning", "kind": "Gdef"}, "PytestUnhandledThreadExceptionWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestUnhandledThreadExceptionWarning", "kind": "Gdef"}, "PytestUnknownMarkWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestUnknownMarkWarning", "kind": "Gdef"}, "PytestUnraisableExceptionWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestUnraisableExceptionWarning", "kind": "Gdef"}, "PytestWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestWarning", "kind": "Gdef"}, "Pytester": {".class": "SymbolTableNode", "cross_ref": "_pytest.pytester.Pytester", "kind": "Gdef"}, "RecordedHookCall": {".class": "SymbolTableNode", "cross_ref": "_pytest.pytester.RecordedHookCall", "kind": "Gdef"}, "RunResult": {".class": "SymbolTableNode", "cross_ref": "_pytest.pytester.RunResult", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "_pytest.main.Session", "kind": "Gdef"}, "Stash": {".class": "SymbolTableNode", "cross_ref": "_pytest.stash.Stash", "kind": "Gdef"}, "StashKey": {".class": "SymbolTableNode", "cross_ref": "_pytest.stash.StashKey", "kind": "Gdef"}, "TempPathFactory": {".class": "SymbolTableNode", "cross_ref": "_pytest.tmpdir.TempPathFactory", "kind": "Gdef"}, "TempdirFactory": {".class": "SymbolTableNode", "cross_ref": "_pytest.legacypath.TempdirFactory", "kind": "Gdef"}, "TestReport": {".class": "SymbolTableNode", "cross_ref": "_pytest.reports.TestReport", "kind": "Gdef"}, "TestShortLogReport": {".class": "SymbolTableNode", "cross_ref": "_pytest.terminal.TestShortLogReport", "kind": "Gdef"}, "Testdir": {".class": "SymbolTableNode", "cross_ref": "_pytest.legacypath.Testdir", "kind": "Gdef"}, "UsageError": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.exceptions.UsageError", "kind": "Gdef"}, "WarningsRecorder": {".class": "SymbolTableNode", "cross_ref": "_pytest.recwarn.WarningsRecorder", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pytest.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pytest.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pytest.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pytest.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pytest.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pytest.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pytest.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__pytestPDB": {".class": "SymbolTableNode", "cross_ref": "_pytest.debugging.pytestPDB", "kind": "Gdef", "module_public": false}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pytest.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "_pytest._version.version", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "approx": {".class": "SymbolTableNode", "cross_ref": "_pytest.python_api.approx", "kind": "Gdef"}, "cmdline": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.cmdline", "kind": "Gdef"}, "console_main": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.console_main", "kind": "Gdef"}, "deprecated_call": {".class": "SymbolTableNode", "cross_ref": "_pytest.recwarn.deprecated_call", "kind": "Gdef"}, "exit": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.exit", "kind": "Gdef"}, "fail": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.fail", "kind": "Gdef"}, "fixture": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.fixture", "kind": "Gdef"}, "freeze_includes": {".class": "SymbolTableNode", "cross_ref": "_pytest.freeze_support.freeze_includes", "kind": "Gdef"}, "hookimpl": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.hookimpl", "kind": "Gdef"}, "hookspec": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.hookspec", "kind": "Gdef"}, "importorskip": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.importorskip", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.main", "kind": "Gdef"}, "mark": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.MARK_GEN", "kind": "Gdef"}, "param": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.param", "kind": "Gdef"}, "raises": {".class": "SymbolTableNode", "cross_ref": "_pytest.python_api.raises", "kind": "Gdef"}, "register_assert_rewrite": {".class": "SymbolTableNode", "cross_ref": "_pytest.assertion.register_assert_rewrite", "kind": "Gdef"}, "set_trace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pytest.set_trace", "name": "set_trace", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [{".class": "TypeType", "item": "_pytest.debugging.pytestPDB"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skip": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.skip", "kind": "Gdef"}, "version_tuple": {".class": "SymbolTableNode", "cross_ref": "_pytest._version.version_tuple", "kind": "Gdef"}, "warns": {".class": "SymbolTableNode", "cross_ref": "_pytest.recwarn.warns", "kind": "Gdef"}, "xfail": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.xfail", "kind": "Gdef"}, "yield_fixture": {".class": "SymbolTableNode", "cross_ref": "_pytest.fixtures.yield_fixture", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pytest\\__init__.py"}