{"data_mtime": 1753839580, "dep_lines": [16, 17, 18, 19, 20, 21, 22, 23, 24, 27, 28, 31, 34, 35, 18, 36, 11, 12, 13, 14, 37, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 10, 5, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["traitlets.config.loader", "traitlets.config.application", "IPython.core.release", "IPython.core.usage", "IPython.core.completer", "IPython.core.crashhandler", "IPython.core.formatters", "IPython.core.history", "IPython.core.application", "IPython.core.magic", "IPython.core.magics", "IPython.core.shellapp", "IPython.extensions.storemagic", "IPython.terminal.interactiveshell", "IPython.core", "IPython.paths", "logging", "os", "sys", "warnings", "traitlets", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "typing", "functools", "IPython.core.interactiveshell", "IPython.core.profiledir", "_frozen_importlib", "_typeshed", "abc", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel"], "hash": "71d9e3484289e26f4213c7bdb12cf024b187ab84", "id": "IPython.terminal.ipapp", "ignore_all": true, "interface_hash": "85ad4d641d6433cba964345a680f0c66c79260c4", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\terminal\\ipapp.py", "plugin_data": null, "size": 12349, "suppressed": [], "version_id": "1.15.0"}