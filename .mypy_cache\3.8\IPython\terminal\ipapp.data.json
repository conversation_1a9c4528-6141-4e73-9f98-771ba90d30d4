{".class": "MypyFile", "_fullname": "IPython.terminal.ipapp", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseIPythonApplication": {".class": "SymbolTableNode", "cross_ref": "IPython.core.application.BaseIPythonApplication", "kind": "Gdef"}, "Bool": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Bool", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "traitlets.config.loader.Config", "kind": "Gdef"}, "CrashHandler": {".class": "SymbolTableNode", "cross_ref": "IPython.core.crashhandler.CrashHandler", "kind": "Gdef"}, "HistoryManager": {".class": "SymbolTableNode", "cross_ref": "IPython.core.history.HistoryManager", "kind": "Gdef"}, "IPAppCrashHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.crashhandler.CrashHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.ipapp.IPAppCrashHandler", "name": "IPAppCrashHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.ipapp.IPAppCrashHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.terminal.ipapp", "mro": ["IPython.terminal.ipapp.IPAppCrashHandler", "IPython.core.crashhandler.CrashHandler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ipapp.IPAppCrashHandler.__init__", "name": "__init__", "type": null}}, "make_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "traceback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ipapp.IPAppCrashHandler.make_report", "name": "make_report", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.ipapp.IPAppCrashHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.ipapp.IPAppCrashHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IPCompleter": {".class": "SymbolTableNode", "cross_ref": "IPython.core.completer.IPCompleter", "kind": "Gdef"}, "InteractiveShellApp": {".class": "SymbolTableNode", "cross_ref": "IPython.core.shellapp.InteractiveShellApp", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.List", "kind": "Gdef"}, "LocateIPythonApp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.application.BaseIPythonApplication"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.ipapp.LocateIPythonApp", "name": "LocateIPythonApp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.ipapp.LocateIPythonApp", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.terminal.ipapp", "mro": ["IPython.terminal.ipapp.LocateIPythonApp", "IPython.core.application.BaseIPythonApplication", "traitlets.config.application.Application", "traitlets.config.configurable.SingletonConfigurable", "traitlets.config.configurable.LoggingConfigurable", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.LocateIPythonApp.description", "name": "description", "type": "builtins.str"}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ipapp.LocateIPythonApp.start", "name": "start", "type": null}}, "subcommands": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.LocateIPythonApp.subcommands", "name": "subcommands", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.ipapp.LocateIPythonApp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.ipapp.LocateIPythonApp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LoggingMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.logging.LoggingMagics", "kind": "Gdef"}, "MagicsManager": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.MagicsManager", "kind": "Gdef"}, "PlainTextFormatter": {".class": "SymbolTableNode", "cross_ref": "IPython.core.formatters.PlainTextFormatter", "kind": "Gdef"}, "ProfileDir": {".class": "SymbolTableNode", "cross_ref": "IPython.core.profiledir.ProfileDir", "kind": "Gdef"}, "ScriptMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.script.ScriptMagics", "kind": "Gdef"}, "StoreMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.extensions.storemagic.StoreMagics", "kind": "Gdef"}, "TerminalIPythonApp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.application.BaseIPythonApplication", "IPython.core.shellapp.InteractiveShellApp"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.ipapp.TerminalIPythonApp", "name": "TerminalIPythonApp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.terminal.ipapp", "mro": ["IPython.terminal.ipapp.TerminalIPythonApp", "IPython.core.application.BaseIPythonApplication", "traitlets.config.application.Application", "traitlets.config.configurable.SingletonConfigurable", "traitlets.config.configurable.LoggingConfigurable", "IPython.core.shellapp.InteractiveShellApp", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "_classes_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp._classes_default", "name": "_classes_default", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp._classes_default", "name": "_classes_default", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_file_to_run_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp._file_to_run_changed", "name": "_file_to_run_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp._file_to_run_changed", "name": "_file_to_run_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_force_interact_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp._force_interact_changed", "name": "_force_interact_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp._force_interact_changed", "name": "_force_interact_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_pylab_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp._pylab_changed", "name": "_pylab_changed", "type": null}}, "_quick_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp._quick_changed", "name": "_quick_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp._quick_changed", "name": "_quick_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "aliases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.aliases", "name": "aliases", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "auto_create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.auto_create", "name": "auto_create", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.classes", "name": "classes", "type": null}}, "crash_handler_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.crash_handler_class", "name": "crash_handler_class", "type": null}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.description", "name": "description", "type": "builtins.str"}}, "display_banner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.display_banner", "name": "display_banner", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.examples", "name": "examples", "type": "builtins.str"}}, "flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.flags", "name": "flags", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "force_interact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.force_interact", "name": "force_interact", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "init_banner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.init_banner", "name": "init_banner", "type": null}}, "init_shell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.init_shell", "name": "init_shell", "type": null}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "argv"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.initialize", "name": "initialize", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "argv"], "arg_types": ["IPython.terminal.ipapp.TerminalIPythonApp", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of TerminalIPythonApp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "interactive_shell_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.interactive_shell_class", "name": "interactive_shell_class", "type": {".class": "Instance", "args": ["builtins.type", "builtins.type"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Type"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.name", "name": "name", "type": "builtins.str"}}, "quick": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.quick", "name": "quick", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "something_to_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.something_to_run", "name": "something_to_run", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.start", "name": "start", "type": null}}, "subcommands": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.subcommands", "name": "subcommands", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.ipapp.TerminalIPythonApp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.ipapp.TerminalIPythonApp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TerminalInteractiveShell": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.interactiveshell.TerminalInteractiveShell", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Type", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ipapp.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ipapp.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ipapp.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ipapp.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ipapp.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.ipapp.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_examples": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp._examples", "name": "_examples", "type": "builtins.str"}}, "addflag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.addflag", "name": "addflag", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aliases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.aliases", "name": "aliases", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "base_aliases": {".class": "SymbolTableNode", "cross_ref": "IPython.core.application.base_aliases", "kind": "Gdef"}, "base_flags": {".class": "SymbolTableNode", "cross_ref": "IPython.core.application.base_flags", "kind": "Gdef"}, "boolean_flag": {".class": "SymbolTableNode", "cross_ref": "traitlets.config.application.boolean_flag", "kind": "Gdef"}, "catch_config_error": {".class": "SymbolTableNode", "cross_ref": "traitlets.config.application.catch_config_error", "kind": "Gdef"}, "classic_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.classic_config", "name": "classic_config", "type": "traitlets.config.loader.Config"}}, "default": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.default", "kind": "Gdef"}, "flags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.flags", "name": "flags", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "frontend_flags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.frontend_flags", "name": "frontend_flags", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_ipython_dir": {".class": "SymbolTableNode", "cross_ref": "IPython.paths.get_ipython_dir", "kind": "Gdef"}, "launch_new_instance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.ipapp.launch_new_instance", "name": "launch_new_instance", "type": {".class": "CallableType", "arg_kinds": [1, 4], "arg_names": ["argv", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "traitlets.config.application.ArgvType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [{".class": "TypeType", "item": "IPython.terminal.ipapp.TerminalIPythonApp"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_default_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["ipython_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.ipapp.load_default_config", "name": "load_default_config", "type": null}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "observe": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.observe", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "release": {".class": "SymbolTableNode", "cross_ref": "IPython.core.release", "kind": "Gdef"}, "shell_aliases": {".class": "SymbolTableNode", "cross_ref": "IPython.core.shellapp.shell_aliases", "kind": "Gdef"}, "shell_flags": {".class": "SymbolTableNode", "cross_ref": "IPython.core.shellapp.shell_flags", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "usage": {".class": "SymbolTableNode", "cross_ref": "IPython.core.usage", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\terminal\\ipapp.py"}