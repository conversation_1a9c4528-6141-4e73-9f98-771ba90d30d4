{".class": "MypyFile", "_fullname": "prompt_toolkit.layout.controls", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_public": false}, "AnyFormattedText": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText", "kind": "Gdef", "module_public": false}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.buffer.Buffer", "kind": "Gdef", "module_public": false}, "BufferControl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.controls.UIControl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.controls.BufferControl", "name": "BufferControl", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.controls", "mro": ["prompt_toolkit.layout.controls.BufferControl", "prompt_toolkit.layout.controls.UIControl", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "buffer", "input_processors", "include_default_input_processors", "lexer", "preview_search", "focusable", "search_buffer_control", "menu_position", "focus_on_click", "key_bindings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "buffer", "input_processors", "include_default_input_processors", "lexer", "preview_search", "focusable", "search_buffer_control", "menu_position", "focus_on_click", "key_bindings"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl", {".class": "UnionType", "items": ["prompt_toolkit.buffer.Buffer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["prompt_toolkit.lexers.base.Lexer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": [{".class": "NoneType"}, "prompt_toolkit.layout.controls.SearchBufferControl", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "prompt_toolkit.layout.controls.SearchBufferControl", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BufferControl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of BufferControl", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_get_processed_line_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "document", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl._create_get_processed_line_func", "name": "_create_get_processed_line_func", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "document", "width", "height"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl", "prompt_toolkit.document.Document", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_get_processed_line_func of BufferControl", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.controls._ProcessedLine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fragment_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl._fragment_cache", "name": "_fragment_cache", "type": {".class": "Instance", "args": ["<PERSON>.<PERSON>", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "prompt_toolkit.cache.SimpleCache"}}}, "_get_formatted_text_for_line_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl._get_formatted_text_for_line_func", "name": "_get_formatted_text_for_line_func", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_formatted_text_for_line_func of BufferControl", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_last_click_timestamp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl._last_click_timestamp", "name": "_last_click_timestamp", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_last_get_processed_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl._last_get_processed_line", "name": "_last_get_processed_line", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.controls._ProcessedLine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_search_buffer_control": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl._search_buffer_control", "name": "_search_buffer_control", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "prompt_toolkit.layout.controls.SearchBufferControl", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "prompt_toolkit.layout.controls.SearchBufferControl", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.buffer", "name": "buffer", "type": "prompt_toolkit.buffer.Buffer"}}, "create_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "width", "height", "preview_search"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl.create_content", "name": "create_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "width", "height", "preview_search"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_content of BufferControl", "ret_type": "prompt_toolkit.layout.controls.UIContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_input_processors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.default_input_processors", "name": "default_input_processors", "type": {".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "focus_on_click": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.focus_on_click", "name": "focus_on_click", "type": "prompt_toolkit.filters.base.Filter"}}, "focusable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.focusable", "name": "focusable", "type": "prompt_toolkit.filters.base.Filter"}}, "get_invalidate_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl.get_invalidate_events", "name": "get_invalidate_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_invalidate_events of BufferControl", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.utils.Event"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_key_bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl.get_key_bindings", "name": "get_key_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_key_bindings of BufferControl", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "include_default_input_processors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.include_default_input_processors", "name": "include_default_input_processors", "type": "builtins.bool"}}, "input_processors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.input_processors", "name": "input_processors", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "is_focusable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl.is_focusable", "name": "is_focusable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_focusable of BufferControl", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_bindings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.key_bindings", "name": "key_bindings", "type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "lexer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.lexer", "name": "lexer", "type": "prompt_toolkit.lexers.base.Lexer"}}, "menu_position": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.menu_position", "name": "menu_position", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "mouse_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mouse_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl.mouse_handler", "name": "mouse_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mouse_event"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl", "prompt_toolkit.mouse_events.MouseEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouse_handler of BufferControl", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "move_cursor_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl.move_cursor_down", "name": "move_cursor_down", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move_cursor_down of BufferControl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "move_cursor_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl.move_cursor_up", "name": "move_cursor_up", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move_cursor_up of BufferControl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "width", "max_available_height", "wrap_lines", "get_line_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "width", "max_available_height", "wrap_lines", "get_line_prefix"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl", "builtins.int", "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.controls.GetLinePrefixCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of BufferControl", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.BufferControl.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of BufferControl", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preview_search": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.preview_search", "name": "preview_search", "type": "prompt_toolkit.filters.base.Filter"}}, "search_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.controls.BufferControl.search_buffer", "name": "search_buffer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_buffer of BufferControl", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.buffer.Buffer", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.search_buffer", "name": "search_buffer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_buffer of BufferControl", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.buffer.Buffer", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "search_buffer_control": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.controls.BufferControl.search_buffer_control", "name": "search_buffer_control", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_buffer_control of BufferControl", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.layout.controls.SearchBufferControl", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.search_buffer_control", "name": "search_buffer_control", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_buffer_control of BufferControl", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.layout.controls.SearchBufferControl", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "search_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.controls.BufferControl.search_state", "name": "search_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_state of BufferControl", "ret_type": "prompt_toolkit.search.SearchState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.controls.BufferControl.search_state", "name": "search_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.BufferControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_state of BufferControl", "ret_type": "prompt_toolkit.search.SearchState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls.BufferControl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.controls.BufferControl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DisplayMultipleCursors": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.DisplayMultipleCursors", "kind": "Gdef", "module_public": false}, "Document": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.document.Document", "kind": "Gdef", "module_public": false}, "DummyControl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.controls.UIControl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.controls.DummyControl", "name": "DummyControl", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.DummyControl", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.controls", "mro": ["prompt_toolkit.layout.controls.DummyControl", "prompt_toolkit.layout.controls.UIControl", "builtins.object"], "names": {".class": "SymbolTable", "create_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.DummyControl.create_content", "name": "create_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "arg_types": ["prompt_toolkit.layout.controls.DummyControl", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_content of DummyControl", "ret_type": "prompt_toolkit.layout.controls.UIContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_focusable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.DummyControl.is_focusable", "name": "is_focusable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.DummyControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_focusable of DummyControl", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls.DummyControl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.controls.DummyControl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Event": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.Event", "kind": "Gdef", "module_public": false}, "FilterOrBool": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.FilterOrBool", "kind": "Gdef", "module_public": false}, "FormattedTextControl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.controls.UIControl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.controls.FormattedTextControl", "name": "FormattedTextControl", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.controls", "mro": ["prompt_toolkit.layout.controls.FormattedTextControl", "prompt_toolkit.layout.controls.UIControl", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "text", "style", "focusable", "key_bindings", "show_cursor", "modal", "get_cursor_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "text", "style", "focusable", "key_bindings", "show_cursor", "modal", "get_cursor_position"], "arg_types": ["prompt_toolkit.layout.controls.FormattedTextControl", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FormattedTextControl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.controls.FormattedTextControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of FormattedTextControl", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_content_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl._content_cache", "name": "_content_cache", "type": {".class": "Instance", "args": ["<PERSON>.<PERSON>", "prompt_toolkit.layout.controls.UIContent"], "extra_attrs": null, "type_ref": "prompt_toolkit.cache.SimpleCache"}}}, "_fragment_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl._fragment_cache", "name": "_fragment_cache", "type": {".class": "Instance", "args": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}], "extra_attrs": null, "type_ref": "prompt_toolkit.cache.SimpleCache"}}}, "_fragments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl._fragments", "name": "_fragments", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_get_formatted_text_cached": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl._get_formatted_text_cached", "name": "_get_formatted_text_cached", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.FormattedTextControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_formatted_text_cached of FormattedTextControl", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.create_content", "name": "create_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "arg_types": ["prompt_toolkit.layout.controls.FormattedTextControl", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_content of FormattedTextControl", "ret_type": "prompt_toolkit.layout.controls.UIContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "focusable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.focusable", "name": "focusable", "type": "prompt_toolkit.filters.base.Filter"}}, "get_cursor_position": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.get_cursor_position", "name": "get_cursor_position", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_key_bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.get_key_bindings", "name": "get_key_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.FormattedTextControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_key_bindings of FormattedTextControl", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_focusable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.is_focusable", "name": "is_focusable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.FormattedTextControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_focusable of FormattedTextControl", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_modal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.is_modal", "name": "is_modal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.FormattedTextControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_modal of FormattedTextControl", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_bindings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.key_bindings", "name": "key_bindings", "type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "modal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.modal", "name": "modal", "type": "builtins.bool"}}, "mouse_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mouse_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.mouse_handler", "name": "mouse_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mouse_event"], "arg_types": ["prompt_toolkit.layout.controls.FormattedTextControl", "prompt_toolkit.mouse_events.MouseEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouse_handler of FormattedTextControl", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "width", "max_available_height", "wrap_lines", "get_line_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "width", "max_available_height", "wrap_lines", "get_line_prefix"], "arg_types": ["prompt_toolkit.layout.controls.FormattedTextControl", "builtins.int", "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.controls.GetLinePrefixCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of FormattedTextControl", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.controls.FormattedTextControl", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of FormattedTextControl", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.FormattedTextControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of FormattedTextControl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "show_cursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.show_cursor", "name": "show_cursor", "type": "builtins.bool"}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.style", "name": "style", "type": "builtins.str"}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.text", "name": "text", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls.FormattedTextControl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.controls.FormattedTextControl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetLinePrefixCallable": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "prompt_toolkit.layout.controls.GetLinePrefixCallable", "line": 59, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Hashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef", "module_public": false}, "HighlightIncrementalSearchProcessor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.HighlightIncrementalSearchProcessor", "kind": "Gdef", "module_public": false}, "HighlightSearchProcessor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.HighlightSearchProcessor", "kind": "Gdef", "module_public": false}, "HighlightSelectionProcessor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.HighlightSelectionProcessor", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "KeyBindingsBase": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "kind": "Gdef", "module_public": false}, "Lexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.base.Lexer", "kind": "Gdef", "module_public": false}, "MouseButton": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.mouse_events.MouseButton", "kind": "Gdef", "module_public": false}, "MouseEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.mouse_events.MouseEvent", "kind": "Gdef", "module_public": false}, "MouseEventType": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.mouse_events.MouseEventType", "kind": "Gdef", "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_public": false}, "NotImplementedOrNone": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.NotImplementedOrNone", "kind": "Gdef", "module_public": false}, "Point": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.data_structures.Point", "kind": "Gdef", "module_public": false}, "Processor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.Processor", "kind": "Gdef", "module_public": false}, "SearchBufferControl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.controls.BufferControl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.controls.SearchBufferControl", "name": "SearchBufferControl", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.SearchBufferControl", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.controls", "mro": ["prompt_toolkit.layout.controls.SearchBufferControl", "prompt_toolkit.layout.controls.BufferControl", "prompt_toolkit.layout.controls.UIControl", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "buffer", "input_processors", "lexer", "focus_on_click", "key_bindings", "ignore_case"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.SearchBufferControl.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "buffer", "input_processors", "lexer", "focus_on_click", "key_bindings", "ignore_case"], "arg_types": ["prompt_toolkit.layout.controls.SearchBufferControl", {".class": "UnionType", "items": ["prompt_toolkit.buffer.Buffer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.layout.processors.Processor"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.lexers.base.Lexer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SearchBufferControl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "searcher_search_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.SearchBufferControl.searcher_search_state", "name": "searcher_search_state", "type": "prompt_toolkit.search.SearchState"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls.SearchBufferControl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.controls.SearchBufferControl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SearchState": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.search.SearchState", "kind": "Gdef", "module_public": false}, "SelectionType": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.selection.SelectionType", "kind": "Gdef", "module_public": false}, "SimpleCache": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.cache.SimpleCache", "kind": "Gdef", "module_public": false}, "SimpleLexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.base.SimpleLexer", "kind": "Gdef", "module_public": false}, "StyleAndTextTuples": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TransformationInput": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.TransformationInput", "kind": "Gdef", "module_public": false}, "UIContent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.controls.UIContent", "name": "UIContent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIContent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.layout.controls", "mro": ["prompt_toolkit.layout.controls.UIContent", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIContent.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["prompt_toolkit.layout.controls.UIContent", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of UIContent", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "get_line", "line_count", "cursor_position", "menu_position", "show_cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIContent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "get_line", "line_count", "cursor_position", "menu_position", "show_cursor"], "arg_types": ["prompt_toolkit.layout.controls.UIContent", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UIContent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_line_heights_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.controls.UIContent._line_heights_cache", "name": "_line_heights_cache", "type": {".class": "Instance", "args": ["<PERSON>.<PERSON>", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "cursor_position": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.UIContent.cursor_position", "name": "cursor_position", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": "prompt_toolkit.data_structures.Point"}}}, "get_height_for_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "lineno", "width", "get_line_prefix", "slice_stop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIContent.get_height_for_line", "name": "get_height_for_line", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "lineno", "width", "get_line_prefix", "slice_stop"], "arg_types": ["prompt_toolkit.layout.controls.UIContent", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.controls.GetLinePrefixCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_height_for_line of UIContent", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.UIContent.get_line", "name": "get_line", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "line_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.UIContent.line_count", "name": "line_count", "type": "builtins.int"}}, "menu_position": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.UIContent.menu_position", "name": "menu_position", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "show_cursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.controls.UIContent.show_cursor", "name": "show_cursor", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls.UIContent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.controls.UIContent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UIControl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["create_content", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.controls.UIControl", "name": "UIControl", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "prompt_toolkit.layout.controls.UIControl", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.controls", "mro": ["prompt_toolkit.layout.controls.UIControl", "builtins.object"], "names": {".class": "SymbolTable", "create_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "prompt_toolkit.layout.controls.UIControl.create_content", "name": "create_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "arg_types": ["prompt_toolkit.layout.controls.UIControl", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_content of UIControl", "ret_type": "prompt_toolkit.layout.controls.UIContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.controls.UIControl.create_content", "name": "create_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "arg_types": ["prompt_toolkit.layout.controls.UIControl", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_content of UIControl", "ret_type": "prompt_toolkit.layout.controls.UIContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_invalidate_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIControl.get_invalidate_events", "name": "get_invalidate_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.UIControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_invalidate_events of UIControl", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.utils.Event"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_key_bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIControl.get_key_bindings", "name": "get_key_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.UIControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_key_bindings of UIControl", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_focusable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIControl.is_focusable", "name": "is_focusable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.UIControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_focusable of UIControl", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mouse_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mouse_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIControl.mouse_handler", "name": "mouse_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mouse_event"], "arg_types": ["prompt_toolkit.layout.controls.UIControl", "prompt_toolkit.mouse_events.MouseEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mouse_handler of UIControl", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "move_cursor_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIControl.move_cursor_down", "name": "move_cursor_down", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.UIControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move_cursor_down of UIControl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "move_cursor_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIControl.move_cursor_up", "name": "move_cursor_up", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.UIControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "move_cursor_up of UIControl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "width", "max_available_height", "wrap_lines", "get_line_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIControl.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "width", "max_available_height", "wrap_lines", "get_line_prefix"], "arg_types": ["prompt_toolkit.layout.controls.UIControl", "builtins.int", "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.controls.GetLinePrefixCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of UIControl", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIControl.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.controls.UIControl", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of UIControl", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls.UIControl.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.controls.UIControl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of UIControl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls.UIControl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.controls.UIControl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProcessedLine": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.controls._ProcessedLine", "name": "_ProcessedLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["fragments", "source_to_display", "display_to_source"]}}, "module_name": "prompt_toolkit.layout.controls", "mro": ["prompt_toolkit.layout.controls._ProcessedLine", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "fragments", "source_to_display", "display_to_source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_cls", "fragments", "source_to_display", "display_to_source"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _ProcessedLine", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls._ProcessedLine._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _ProcessedLine", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _ProcessedLine", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _ProcessedLine", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "fragments", "source_to_display", "display_to_source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.controls._ProcessedLine._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["_self", "fragments", "source_to_display", "display_to_source"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _ProcessedLine", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine._NT", "id": -1, "name": "_NT", "namespace": "prompt_toolkit.layout.controls._ProcessedLine._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine._source", "name": "_source", "type": "builtins.str"}}, "display_to_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine.display_to_source", "name": "display_to_source", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "display_to_source-redefinition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls._ProcessedLine.display_to_source", "kind": "<PERSON><PERSON><PERSON>"}, "fragments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine.fragments", "name": "fragments", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}}}, "fragments-redefinition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls._ProcessedLine.fragments", "kind": "<PERSON><PERSON><PERSON>"}, "source_to_display": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "prompt_toolkit.layout.controls._ProcessedLine.source_to_display", "name": "source_to_display", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "source_to_display-redefinition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls._ProcessedLine.source_to_display", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.controls._ProcessedLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": "prompt_toolkit.layout.controls._ProcessedLine"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.controls.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.controls.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.controls.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.controls.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.controls.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.controls.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.controls.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "fragment_list_to_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.utils.fragment_list_to_text", "kind": "Gdef", "module_public": false}, "fragment_list_width": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.utils.fragment_list_width", "kind": "Gdef", "module_public": false}, "get_app": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app", "kind": "Gdef", "module_public": false}, "get_cwidth": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.get_cwidth", "kind": "Gdef", "module_public": false}, "merge_processors": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.merge_processors", "kind": "Gdef", "module_public": false}, "split_lines": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.utils.split_lines", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "to_filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.to_filter", "kind": "Gdef", "module_public": false}, "to_formatted_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.to_formatted_text", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\layout\\controls.py"}