{"data_mtime": 1753839580, "dep_lines": [19, 20, 21, 22, 7, 19, 6, 8, 9, 10, 11, 12, 13, 14, 15, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.magic_arguments", "IPython.core.async_helpers", "IPython.core.magic", "IPython.utils.process", "asyncio.exceptions", "IPython.core", "asyncio", "atexit", "errno", "os", "signal", "sys", "time", "subprocess", "threading", "traitlets", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "typing", "functools", "logging", "IPython.utils", "IPython.utils._process_win32", "_asyncio", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "asyncio.subprocess", "asyncio.tasks", "asyncio.windows_events", "enum", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel"], "hash": "2c8c322f5e8e99703eae7ab6fe9817dc85a226c9", "id": "IPython.core.magics.script", "ignore_all": true, "interface_hash": "a4759736f658c4f9d01dea8cac228605a552a62a", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\magics\\script.py", "plugin_data": null, "size": 12550, "suppressed": [], "version_id": "1.15.0"}