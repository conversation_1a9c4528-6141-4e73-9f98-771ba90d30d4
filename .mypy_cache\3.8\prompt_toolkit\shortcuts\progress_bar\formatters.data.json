{".class": "MypyFile", "_fullname": "prompt_toolkit.shortcuts.progress_bar.formatters", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_public": false}, "AnyDimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.AnyDimension", "kind": "Gdef", "module_public": false}, "AnyFormattedText": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText", "kind": "Gdef", "module_public": false}, "Bar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar", "name": "Bar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.progress_bar.formatters", "mro": ["prompt_toolkit.shortcuts.progress_bar.formatters.Bar", "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "start", "end", "sym_a", "sym_b", "sym_c", "unknown"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "start", "end", "sym_a", "sym_b", "sym_c", "unknown"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Bar", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Bar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar.end", "name": "end", "type": "builtins.str"}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Bar", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of Bar", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Bar", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of Bar", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar.start", "name": "start", "type": "builtins.str"}}, "sym_a": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar.sym_a", "name": "sym_a", "type": "builtins.str"}}, "sym_b": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar.sym_b", "name": "sym_b", "type": "builtins.str"}}, "sym_c": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar.sym_c", "name": "sym_c", "type": "builtins.str"}}, "template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar.template", "name": "template", "type": "builtins.str"}}, "unknown": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar.unknown", "name": "unknown", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.progress_bar.formatters.Bar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "D": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.D", "kind": "Gdef", "module_public": false}, "Formatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["format", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.progress_bar.formatters", "mro": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of Formatter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of Formatter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of Formatter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTML": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.html.HTML", "kind": "Gdef", "module_public": false}, "IterationsPerSecond": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.IterationsPerSecond", "name": "IterationsPerSecond", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.IterationsPerSecond", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.progress_bar.formatters", "mro": ["prompt_toolkit.shortcuts.progress_bar.formatters.IterationsPerSecond", "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.IterationsPerSecond.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.IterationsPerSecond", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of IterationsPerSecond", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.IterationsPerSecond.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.IterationsPerSecond", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of IterationsPerSecond", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.IterationsPerSecond.template", "name": "template", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.IterationsPerSecond.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.progress_bar.formatters.IterationsPerSecond", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Label": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Label", "name": "Label", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Label", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.progress_bar.formatters", "mro": ["prompt_toolkit.shortcuts.progress_bar.formatters.Label", "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "width", "suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Label.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "width", "suffix"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Label", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Label", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "label"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Label._add_suffix", "name": "_add_suffix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "label"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Label", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_suffix of Label", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Label.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Label", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of Label", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Label.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Label", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of Label", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "suffix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Label.suffix", "name": "suffix", "type": "builtins.str"}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Label.width", "name": "width", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Label.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.progress_bar.formatters.Label", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Percentage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Percentage", "name": "Percentage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Percentage", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.progress_bar.formatters", "mro": ["prompt_toolkit.shortcuts.progress_bar.formatters.Percentage", "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Percentage.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Percentage", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of Percentage", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Percentage.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Percentage", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of Percentage", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Percentage.template", "name": "template", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Percentage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.progress_bar.formatters.Percentage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Progress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Progress", "name": "Progress", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Progress", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.progress_bar.formatters", "mro": ["prompt_toolkit.shortcuts.progress_bar.formatters.Progress", "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Progress.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Progress", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of Progress", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Progress.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Progress", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of Progress", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Progress.template", "name": "template", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Progress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.progress_bar.formatters.Progress", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProgressBar": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", "kind": "Gdef", "module_public": false}, "ProgressBarCounter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter", "kind": "Gdef", "module_public": false}, "Rainbow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow", "name": "Rainbow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.progress_bar.formatters", "mro": ["prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow", "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "formatter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "formatter"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow", "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Rainbow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "colors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow.colors", "name": "colors", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of Rainbow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow.formatter", "name": "formatter", "type": "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of Rainbow", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.progress_bar.formatters.Rainbow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SpinningWheel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.SpinningWheel", "name": "SpinningWheel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.SpinningWheel", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.progress_bar.formatters", "mro": ["prompt_toolkit.shortcuts.progress_bar.formatters.SpinningWheel", "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "characters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.SpinningWheel.characters", "name": "characters", "type": "builtins.str"}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.SpinningWheel.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.SpinningWheel", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of SpinningWheel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.SpinningWheel.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.SpinningWheel", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of SpinningWheel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.SpinningWheel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.progress_bar.formatters.SpinningWheel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StyleAndTextTuples": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Text", "name": "Text", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Text", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.progress_bar.formatters", "mro": ["prompt_toolkit.shortcuts.progress_bar.formatters.Text", "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Text.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "style"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Text", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Text.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Text", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of Text", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Text.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.Text", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of Text", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Text.text", "name": "text", "type": "prompt_toolkit.formatted_text.base.FormattedText"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.Text.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.progress_bar.formatters.Text", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeElapsed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeElapsed", "name": "TimeElapsed", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeElapsed", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.progress_bar.formatters", "mro": ["prompt_toolkit.shortcuts.progress_bar.formatters.TimeElapsed", "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeElapsed.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.TimeElapsed", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of TimeElapsed", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeElapsed.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.TimeElapsed", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of TimeElapsed", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeElapsed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeElapsed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeLeft": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeLeft", "name": "TimeLeft", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeLeft", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.shortcuts.progress_bar.formatters", "mro": ["prompt_toolkit.shortcuts.progress_bar.formatters.TimeLeft", "prompt_toolkit.shortcuts.progress_bar.formatters.Formatter", "builtins.object"], "names": {".class": "SymbolTable", "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeLeft.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "progress_bar", "progress", "width"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.TimeLeft", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.progress_bar.base.ProgressBarCounter"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format of TimeLeft", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeLeft.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "progress_bar"], "arg_types": ["prompt_toolkit.shortcuts.progress_bar.formatters.TimeLeft", "prompt_toolkit.shortcuts.progress_bar.base.ProgressBar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of TimeLeft", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeLeft.template", "name": "template", "type": "builtins.str"}}, "unknown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeLeft.unknown", "name": "unknown", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeLeft.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.shortcuts.progress_bar.formatters.TimeLeft", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_format_timedelta": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters._format_timedelta", "name": "_format_<PERSON><PERSON>ta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["<PERSON><PERSON><PERSON>"], "arg_types": ["datetime.<PERSON><PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_<PERSON><PERSON>ta", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_hue_to_rgb": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["hue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters._hue_to_rgb", "name": "_hue_to_rgb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["hue"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_hue_to_rgb", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "create_default_formatters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.progress_bar.formatters.create_default_formatters", "name": "create_default_formatters", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_default_formatters", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.shortcuts.progress_bar.formatters.Formatter"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef", "module_public": false}, "explode_text_fragments": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.utils.explode_text_fragments", "kind": "Gdef", "module_public": false}, "fragment_list_width": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.utils.fragment_list_width", "kind": "Gdef", "module_public": false}, "get_cwidth": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.get_cwidth", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "to_formatted_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.to_formatted_text", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\formatters.py"}