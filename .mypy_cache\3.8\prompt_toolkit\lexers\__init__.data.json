{".class": "MypyFile", "_fullname": "prompt_toolkit.lexers", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DynamicLexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.base.DynamicLexer", "kind": "Gdef"}, "Lexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.base.Lexer", "kind": "Gdef"}, "PygmentsLexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.pygments.PygmentsLexer", "kind": "Gdef"}, "RegexSync": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.pygments.RegexSync", "kind": "Gdef"}, "SimpleLexer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.base.SimpleLexer", "kind": "Gdef"}, "SyncFromStart": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.pygments.SyncFromStart", "kind": "Gdef"}, "SyntaxSync": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.lexers.pygments.SyntaxSync", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.lexers.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\lexers\\__init__.py"}