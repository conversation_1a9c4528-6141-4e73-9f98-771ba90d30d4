{".class": "MypyFile", "_fullname": "numpy.polynomial.polynomial", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABCPolyBase": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial._polybase.ABCPolyBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Polynomial": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numpy.polynomial._polybase.ABCPolyBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.polynomial.polynomial.Polynomial", "name": "Polynomial", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.Polynomial", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.polynomial.polynomial", "mro": ["numpy.polynomial.polynomial.Polynomial", "numpy.polynomial._polybase.ABCPolyBase", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "basis_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.polynomial.polynomial.Polynomial.basis_name", "name": "basis_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.polynomial.polynomial.Polynomial.domain", "name": "domain", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "numpy.polynomial.polynomial.Polynomial.window", "name": "window", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.polynomial.polynomial.Polynomial.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "numpy.polynomial.polynomial.Polynomial", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "int_": {".class": "SymbolTableNode", "cross_ref": "numpy.int_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ndarray": {".class": "SymbolTableNode", "cross_ref": "numpy.n<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "polyadd": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyadd", "name": "polyadd", "type": null}}, "polyder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["c", "m", "scl", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyder", "name": "polyder", "type": null}}, "polydiv": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polydiv", "name": "polydiv", "type": null}}, "polydomain": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polydomain", "name": "polydomain", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "polyfit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["x", "y", "deg", "rcond", "full", "w"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyfit", "name": "polyfit", "type": null}}, "polyfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["roots"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyfromroots", "name": "polyfromroots", "type": null}}, "polygrid2d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "y", "c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polygrid2d", "name": "polygrid2d", "type": null}}, "polygrid3d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["x", "y", "z", "c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polygrid3d", "name": "polygrid3d", "type": null}}, "polyint": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["c", "m", "k", "lbnd", "scl", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyint", "name": "polyint", "type": null}}, "polyline": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["off", "scl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyline", "name": "polyline", "type": null}}, "polymul": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polymul", "name": "polymul", "type": null}}, "polymulx": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polymulx", "name": "polymulx", "type": null}}, "polyone": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyone", "name": "polyone", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "polypow": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["c", "pow", "maxpower"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polypow", "name": "polypow", "type": null}}, "polyroots": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyroots", "name": "polyroots", "type": null}}, "polysub": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["c1", "c2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polysub", "name": "polysub", "type": null}}, "polytrim": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "numpy.polynomial.polynomial.polytrim", "name": "polytrim", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["c", "tol"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "polyval": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "c", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyval", "name": "polyval", "type": null}}, "polyval2d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "y", "c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyval2d", "name": "polyval2d", "type": null}}, "polyval3d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["x", "y", "z", "c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyval3d", "name": "polyval3d", "type": null}}, "polyvalfromroots": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["x", "r", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyvalfromroots", "name": "polyvalfromroots", "type": null}}, "polyvander": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x", "deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyvander", "name": "polyvander", "type": null}}, "polyvander2d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "y", "deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyvander2d", "name": "polyvander2d", "type": null}}, "polyvander3d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["x", "y", "z", "deg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.polynomial.polyvander3d", "name": "polyvander3d", "type": null}}, "polyx": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyx", "name": "polyx", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "polyzero": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.polynomial.polyzero", "name": "polyzero", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.int_"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "trimcoef": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.polyutils.trimcoef", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\numpy\\polynomial\\polynomial.pyi"}