{"data_mtime": 1753839575, "dep_lines": [20, 22, 23, 24, 27, 1, 2, 3, 4, 5, 6, 14, 15, 16, 17, 18, 21, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.types", "pydantic.color", "pydantic.networks", "pydantic.types", "fastapi._compat", "dataclasses", "datetime", "collections", "decimal", "enum", "ipaddress", "pathlib", "re", "types", "typing", "uuid", "pydantic", "typing_extensions", "builtins", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "sys", "logging", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal", "pydantic._internal._repr", "pydantic_core", "pydantic_core._pydantic_core"], "hash": "cf14073db7680b3bd1b0bcb0ed3e80a21af9a10d", "id": "fastapi.encoders", "ignore_all": true, "interface_hash": "87905c2fcd4b74eaca1e2a7c4e80740c88c4f313", "mtime": 1750471027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\fastapi\\encoders.py", "plugin_data": null, "size": 11068, "suppressed": [], "version_id": "1.15.0"}