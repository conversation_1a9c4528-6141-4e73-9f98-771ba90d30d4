{"data_mtime": 1753839581, "dep_lines": [54, 56, 57, 58, 22, 23, 49, 50, 55, 59, 61, 62, 63, 64, 71, 72, 73, 1, 2, 3, 4, 5, 21, 22, 60, 74, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 20, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30], "dependencies": ["fastapi.dependencies.models", "fastapi.security.base", "fastapi.security.oauth2", "fastapi.security.open_id_connect_url", "fastapi.params", "fastapi._compat", "fastapi.background", "fastapi.concurrency", "fastapi.logger", "fastapi.utils", "pydantic.fields", "starlette.background", "starlette.concurrency", "starlette.datastructures", "starlette.requests", "starlette.responses", "starlette.websockets", "inspect", "contextlib", "copy", "dataclasses", "typing", "anyio", "<PERSON><PERSON><PERSON>", "pydantic", "typing_extensions", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "sys", "logging", "_frozen_importlib", "abc", "starlette"], "hash": "4cd032deea88a2c0b5fc96aa9c026bc2948ebefe", "id": "fastapi.dependencies.utils", "ignore_all": true, "interface_hash": "6f12642c4b01b7ecfce1fda1334f28633ea14d47", "mtime": 1750471027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\fastapi\\dependencies\\utils.py", "plugin_data": null, "size": 35579, "suppressed": [], "version_id": "1.15.0"}