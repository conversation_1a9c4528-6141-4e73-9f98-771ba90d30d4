{"data_mtime": 1753839580, "dep_lines": [219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 241, 225, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 231, 243, 261, 2822, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 252, 253, 252, 250], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 25, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 20, 10], "dependencies": ["IPython.core.guarded_eval", "IPython.core.error", "IPython.core.inputtransformer2", "IPython.core.latex_symbols", "IPython.core.oinspect", "IPython.testing.skipdoctest", "IPython.utils.generics", "IPython.utils.decorators", "IPython.utils.dir2", "IPython.utils.docs", "IPython.utils.path", "IPython.utils.process", "traitlets.config.configurable", "IPython.utils", "__future__", "builtins", "enum", "glob", "inspect", "itertools", "keyword", "os", "re", "string", "sys", "tokenize", "time", "unicodedata", "uuid", "warnings", "ast", "collections", "contextlib", "dataclasses", "functools", "types", "typing", "traitlets", "__main__", "typing_extensions", "cProfile", "json", "traitlets.utils.warnings", "pprint", "logging", "IPython.testing", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "traitlets.config", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel"], "hash": "b4fc4689ee6a780cceeb061a3ee4bf3e37eacb41", "id": "IPython.core.completer", "ignore_all": true, "interface_hash": "1d2d2a3859d9dfa98350c6ae8e9c808a3b6ae087", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\completer.py", "plugin_data": null, "size": 117868, "suppressed": ["jedi.api.helpers", "jedi.api.classes", "jedi.api", "jedi"], "version_id": "1.15.0"}