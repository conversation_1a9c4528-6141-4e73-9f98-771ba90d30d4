{".class": "MypyFile", "_fullname": "_pytest.nodes", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Collector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["collect", 1]], "alt_promote": null, "bases": ["_pytest.nodes.Node", "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.nodes.Collector", "name": "Collector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "_pytest.nodes.Collector", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.nodes", "mro": ["_pytest.nodes.Collector", "_pytest.nodes.Node", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "CollectError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.nodes.Collector.CollectError", "name": "CollectError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Collector.CollectError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.nodes", "mro": ["_pytest.nodes.Collector.CollectError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.Collector.CollectError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.Collector.CollectError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_traceback_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Collector._traceback_filter", "name": "_traceback_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "arg_types": ["_pytest.nodes.Collector", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traceback_filter of Collector", "ret_type": "_pytest._code.code.Traceback", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "_pytest.nodes.Collector.collect", "name": "collect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Collector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect of Collector", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Collector.collect", "name": "collect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Collector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect of Collector", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["_pytest.nodes.Item", "_pytest.nodes.Collector"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "repr_failure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Collector.repr_failure", "name": "repr_failure", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "arg_types": ["_pytest.nodes.Collector", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repr_failure of Collector", "ret_type": {".class": "UnionType", "items": ["builtins.str", "_pytest._code.code.TerminalRepr"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.Collector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.Collector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Config": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.Config", "kind": "Gdef"}, "ConftestImportFailure": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.ConftestImportFailure", "kind": "Gdef"}, "Directory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["collect", 1]], "alt_promote": null, "bases": ["_pytest.nodes.FSCollector", "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.nodes.Directory", "name": "Directory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "_pytest.nodes.Directory", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.nodes", "mro": ["_pytest.nodes.Directory", "_pytest.nodes.FSCollector", "_pytest.nodes.Collector", "_pytest.nodes.Node", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.Directory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.Directory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExceptionInfo": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.ExceptionInfo", "kind": "Gdef"}, "FSCollector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["collect", 1]], "alt_promote": null, "bases": ["_pytest.nodes.Collector", "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.nodes.FSCollector", "name": "FSCollector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "_pytest.nodes.FSCollector", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.nodes", "mro": ["_pytest.nodes.FSCollector", "_pytest.nodes.Collector", "_pytest.nodes.Node", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "fspath", "path_or_parent", "path", "name", "parent", "config", "session", "nodeid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.FSCollector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "fspath", "path_or_parent", "path", "name", "parent", "config", "session", "nodeid"], "arg_types": ["_pytest.nodes.FSCollector", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pathlib.Path", "_pytest.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_pytest.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_pytest.main.Session", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FSCollector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["cls", "parent", "fspath", "path", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.nodes.FSCollector.from_parent", "name": "from_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["cls", "parent", "fspath", "path", "kw"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.FSCollector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.FSCollector", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_parent of FSCollector", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.FSCollector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.FSCollector", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.FSCollector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.FSCollector", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.FSCollector.from_parent", "name": "from_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["cls", "parent", "fspath", "path", "kw"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.FSCollector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.FSCollector", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_parent of FSCollector", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.FSCollector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.FSCollector", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.FSCollector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.FSCollector", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.FSCollector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.FSCollector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "File": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["collect", 1]], "alt_promote": null, "bases": ["_pytest.nodes.FSCollector", "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.nodes.File", "name": "File", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "_pytest.nodes.File", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.nodes", "mro": ["_pytest.nodes.File", "_pytest.nodes.FSCollector", "_pytest.nodes.Collector", "_pytest.nodes.Node", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.File.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.File", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Item": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["runtest", 1]], "alt_promote": null, "bases": ["_pytest.nodes.Node", "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.nodes.Item", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "_pytest.nodes.Item", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.nodes", "mro": ["_pytest.nodes.Item", "_pytest.nodes.Node", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "name", "parent", "config", "session", "nodeid", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Item.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "name", "parent", "config", "session", "nodeid", "kw"], "arg_types": ["_pytest.nodes.Item", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_pytest.main.Session", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Item", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_item_and_collector_diamond_inheritance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Item._check_item_and_collector_diamond_inheritance", "name": "_check_item_and_collector_diamond_inheritance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_item_and_collector_diamond_inheritance of Item", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_report_sections": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Item._report_sections", "name": "_report_sections", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "add_report_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "when", "key", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Item.add_report_section", "name": "add_report_section", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "when", "key", "content"], "arg_types": ["_pytest.nodes.Item", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_report_section of Item", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.nodes.Item.location", "name": "location", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "location of Item", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Item.location", "name": "location", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "location of Item", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nextitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.nodes.Item.nextitem", "name": "nextitem", "type": {".class": "NoneType"}}}, "reportinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Item.reportinfo", "name": "reportinfo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reportinfo of Item", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runtest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "_pytest.nodes.Item.runtest", "name": "runtest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "runtest of Item", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Item.runtest", "name": "runtest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "runtest of Item", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_properties": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Item.user_properties", "name": "user_properties", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.object"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.Item.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.Item", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LEGACY_PATH": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.LEGACY_PATH", "kind": "Gdef"}, "Mark": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.Mark", "kind": "Gdef"}, "MarkDecorator": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.MarkDecorator", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NODE_CTOR_FSPATH_ARG": {".class": "SymbolTableNode", "cross_ref": "_pytest.deprecated.NODE_CTOR_FSPATH_ARG", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "Node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": "_pytest.nodes.NodeMeta", "defn": {".class": "ClassDef", "fullname": "_pytest.nodes.Node", "name": "Node", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node", "has_param_spec_type": false, "metaclass_type": "_pytest.nodes.NodeMeta", "metadata": {}, "module_name": "_pytest.nodes", "mro": ["_pytest.nodes.Node", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of Node", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "parent", "config", "session", "fspath", "path", "nodeid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "parent", "config", "session", "fspath", "path", "nodeid"], "arg_types": ["_pytest.nodes.Node", "builtins.str", {".class": "UnionType", "items": ["_pytest.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_pytest.main.Session", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Node", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_pytest.nodes.Node.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_nodeid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.nodes.Node._nodeid", "name": "_nodeid", "type": "builtins.str"}}, "_repr_failure_py": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "excinfo", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node._repr_failure_py", "name": "_repr_failure_py", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "excinfo", "style"], "arg_types": ["_pytest.nodes.Node", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest._code.code.TracebackStyle"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr_failure_py of Node", "ret_type": "_pytest._code.code.TerminalRepr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_store": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.nodes.Node._store", "name": "_store", "type": "_pytest.stash.Stash"}}, "_traceback_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node._traceback_filter", "name": "_traceback_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "arg_types": ["_pytest.nodes.Node", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traceback_filter of Node", "ret_type": "_pytest._code.code.Traceback", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_marker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "marker", "append"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.add_marker", "name": "add_marker", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "marker", "append"], "arg_types": ["_pytest.nodes.Node", {".class": "UnionType", "items": ["builtins.str", "_pytest.mark.structures.MarkDecorator"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_marker of Node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "addfinalizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.addfinalizer", "name": "addfinalizer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fin"], "arg_types": ["_pytest.nodes.Node", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addfinalizer of Node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.config", "name": "config", "type": "_pytest.config.Config"}}, "extra_keyword_matches": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.extra_keyword_matches", "name": "extra_keyword_matches", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "from_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "parent", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.nodes.Node.from_parent", "name": "from_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "parent", "kw"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.Node.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.Node", "values": [], "variance": 0}}, "_pytest.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_parent of Node", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.Node.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.Node", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.Node.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.Node", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.from_parent", "name": "from_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "parent", "kw"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.Node.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.Node", "values": [], "variance": 0}}, "_pytest.nodes.Node", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_parent of Node", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.Node.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.Node", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.Node.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.Node", "values": [], "variance": 0}]}}}}, "fspath": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_pytest.nodes.Node.fspath", "name": "fspath", "type": {".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}}}, "get_closest_marker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.get_closest_marker", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "_pytest.nodes.Node.get_closest_marker", "name": "get_closest_marker", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "default"], "arg_types": ["_pytest.nodes.Node", "builtins.str", {".class": "UnionType", "items": ["_pytest.mark.structures.Mark", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_closest_marker of Node", "ret_type": {".class": "UnionType", "items": ["_pytest.mark.structures.Mark", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.nodes.Node.get_closest_marker", "name": "get_closest_marker", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["_pytest.nodes.Node", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_closest_marker of Node", "ret_type": {".class": "UnionType", "items": ["_pytest.mark.structures.Mark", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.get_closest_marker", "name": "get_closest_marker", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["_pytest.nodes.Node", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_closest_marker of Node", "ret_type": {".class": "UnionType", "items": ["_pytest.mark.structures.Mark", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.nodes.Node.get_closest_marker", "name": "get_closest_marker", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "arg_types": ["_pytest.nodes.Node", "builtins.str", "_pytest.mark.structures.Mark"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_closest_marker of Node", "ret_type": "_pytest.mark.structures.Mark", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.get_closest_marker", "name": "get_closest_marker", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "arg_types": ["_pytest.nodes.Node", "builtins.str", "_pytest.mark.structures.Mark"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_closest_marker of Node", "ret_type": "_pytest.mark.structures.Mark", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["_pytest.nodes.Node", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_closest_marker of Node", "ret_type": {".class": "UnionType", "items": ["_pytest.mark.structures.Mark", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "arg_types": ["_pytest.nodes.Node", "builtins.str", "_pytest.mark.structures.Mark"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_closest_marker of Node", "ret_type": "_pytest.mark.structures.Mark", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "getparent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.getparent", "name": "getparent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "arg_types": ["_pytest.nodes.Node", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes._NodeType", "id": -1, "name": "_NodeType", "namespace": "_pytest.nodes.Node.getparent", "upper_bound": "_pytest.nodes.Node", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getparent of Node", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes._NodeType", "id": -1, "name": "_NodeType", "namespace": "_pytest.nodes.Node.getparent", "upper_bound": "_pytest.nodes.Node", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes._NodeType", "id": -1, "name": "_NodeType", "namespace": "_pytest.nodes.Node.getparent", "upper_bound": "_pytest.nodes.Node", "values": [], "variance": 0}]}}}, "ihook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.nodes.Node.ihook", "name": "ihook", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ihook of Node", "ret_type": "pluggy._hooks.<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.ihook", "name": "ihook", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ihook of Node", "ret_type": "pluggy._hooks.<PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "iter_markers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.iter_markers", "name": "iter_markers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "name"], "arg_types": ["_pytest.nodes.Node", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_markers of Node", "ret_type": {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_markers_with_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.iter_markers_with_node", "name": "iter_markers_with_node", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "name"], "arg_types": ["_pytest.nodes.Node", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_markers_with_node of Node", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["_pytest.nodes.Node", "_pytest.mark.structures.Mark"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_parents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.iter_parents", "name": "iter_parents", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_parents of <PERSON>de", "ret_type": {".class": "Instance", "args": ["_pytest.nodes.Node"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keywords": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.keywords", "name": "keywords", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "listchain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.listchain", "name": "listchain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "listchain of Node", "ret_type": {".class": "Instance", "args": ["_pytest.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "listextrakeywords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.listextrakeywords", "name": "listextrakeywords", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "listextrakeywords of Node", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "listnames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.listnames", "name": "listnames", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "listnames of Node", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.name", "name": "name", "type": "builtins.str"}}, "nodeid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.nodes.Node.nodeid", "name": "nodeid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nodeid of Node", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.nodeid", "name": "nodeid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nodeid of Node", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "own_markers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.own_markers", "name": "own_markers", "type": {".class": "Instance", "args": ["_pytest.mark.structures.Mark"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.nodes.Node.parent", "name": "parent", "type": {".class": "UnionType", "items": ["_pytest.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.path", "name": "path", "type": "pathlib.Path"}}, "repr_failure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "excinfo", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.repr_failure", "name": "repr_failure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "excinfo", "style"], "arg_types": ["_pytest.nodes.Node", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest._code.code.TracebackStyle"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repr_failure of Node", "ret_type": {".class": "UnionType", "items": ["builtins.str", "_pytest._code.code.TerminalRepr"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.session", "name": "session", "type": "_pytest.main.Session"}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup of Node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stash": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.nodes.Node.stash", "name": "stash", "type": "_pytest.stash.Stash"}}, "teardown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.teardown", "name": "teardown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "teardown of Node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "warning"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.Node.warn", "name": "warn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "warning"], "arg_types": ["_pytest.nodes.Node", "builtins.Warning"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warn of Node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.Node.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.Node", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NodeKeywords": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.NodeKeywords", "kind": "Gdef"}, "NodeMeta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["abc.ABCMeta"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.nodes.NodeMeta", "name": "NodeMeta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.nodes.NodeMeta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.nodes", "mro": ["_pytest.nodes.NodeMeta", "abc.ABCMeta", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "k", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.NodeMeta.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "k", "kw"], "arg_types": ["_pytest.nodes.NodeMeta", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of NodeMeta", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "k", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.NodeMeta._create", "name": "_create", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "k", "kw"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes._T", "id": -1, "name": "_T", "namespace": "_pytest.nodes.NodeMeta._create", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create of NodeMeta", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes._T", "id": -1, "name": "_T", "namespace": "_pytest.nodes.NodeMeta._create", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes._T", "id": -1, "name": "_T", "namespace": "_pytest.nodes.NodeMeta._create", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes.NodeMeta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.nodes.NodeMeta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PytestWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestWarning", "kind": "Gdef"}, "SEP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "_pytest.nodes.SEP", "name": "SEP", "type": "builtins.str"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "_pytest.main.Session", "kind": "Gdef"}, "Stash": {".class": "SymbolTableNode", "cross_ref": "_pytest.stash.Stash", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TerminalRepr": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.TerminalRepr", "kind": "Gdef"}, "Traceback": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.Traceback", "kind": "Gdef"}, "TracebackStyle": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.TracebackStyle", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_NodeType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes._NodeType", "name": "_NodeType", "upper_bound": "_pytest.nodes.Node", "values": [], "variance": 0}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.nodes._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.nodes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.nodes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.nodes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.nodes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.nodes.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.nodes.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_check_initialpaths_for_relpath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["session", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes._check_initialpaths_for_relpath", "name": "_check_initialpaths_for_relpath", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["session", "path"], "arg_types": ["_pytest.main.Session", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_initialpaths_for_relpath", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_path": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.compat._check_path", "kind": "Gdef"}, "_imply_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["node_type", "path", "fspath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes._imply_path", "name": "_imply_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["node_type", "path", "fspath"], "arg_types": [{".class": "TypeType", "item": "_pytest.nodes.Node"}, {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "_pytest.compat.py", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_imply_path", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pytest": {".class": "SymbolTableNode", "cross_ref": "_pytest", "kind": "Gdef"}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "absolutepath": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.absolutepath", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "commonpath": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.commonpath", "kind": "Gdef"}, "fail": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.fail", "kind": "Gdef"}, "get_fslocation_from_item": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.nodes.get_fslocation_from_item", "name": "get_fslocation_from_item", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fslocation_from_item", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getfslineno": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.getfslineno", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "pathlib": {".class": "SymbolTableNode", "cross_ref": "pathlib", "kind": "Gdef"}, "pluggy": {".class": "SymbolTableNode", "cross_ref": "pluggy", "kind": "Gdef"}, "signature": {".class": "SymbolTableNode", "cross_ref": "inspect.signature", "kind": "Gdef"}, "tracebackcutdir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.nodes.tracebackcutdir", "name": "tracebackcutdir", "type": "pathlib.Path"}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\nodes.py"}