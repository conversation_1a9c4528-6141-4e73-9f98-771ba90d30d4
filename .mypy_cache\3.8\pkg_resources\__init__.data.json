{".class": "MypyFile", "_fullname": "pkg_resources", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AvailableDistributions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pkg_resources.AvailableDistributions", "line": 1331, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "pkg_resources.Environment"}}, "BINARY_DIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.BINARY_DIST", "name": "BINARY_DIST", "type": "builtins.int"}}, "BinaryIO": {".class": "SymbolTableNode", "cross_ref": "typing.BinaryIO", "kind": "Gdef", "module_public": false}, "BytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.BytesPath", "kind": "Gdef", "module_public": false}, "CHECKOUT_DIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.CHECKOUT_DIST", "name": "CHECKOUT_DIST", "type": "builtins.int"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ContextualVersionConflict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.VersionConflict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.ContextualVersionConflict", "name": "ContextualVersionConflict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.ContextualVersionConflict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.ContextualVersionConflict", "pkg_resources.VersionConflict", "pkg_resources.ResolutionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.ContextualVersionConflict._template", "name": "_template", "type": null}}, "required_by": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.ContextualVersionConflict.required_by", "name": "required_by", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.ContextualVersionConflict"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "required_by of ContextualVersionConflict", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.ContextualVersionConflict.required_by", "name": "required_by", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.ContextualVersionConflict"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "required_by of ContextualVersionConflict", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.ContextualVersionConflict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.ContextualVersionConflict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DEVELOP_DIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.DEVELOP_DIST", "name": "DEVELOP_DIST", "type": "builtins.int"}}, "DefaultProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.EggProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.DefaultProvider", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.DefaultProvider", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.DefaultProvider", "pkg_resources.EggProvider", "pkg_resources.NullProvider", "builtins.object"], "names": {".class": "SymbolTable", "_get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.DefaultProvider._get", "name": "_get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.DefaultProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get of DefaultProvider", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.DefaultProvider._has", "name": "_has", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.DefaultProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has of DefaultProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_isdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.DefaultProvider._isdir", "name": "_isdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.DefaultProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_isdir of DefaultProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_listdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.DefaultProvider._listdir", "name": "_listdir", "type": null}}, "_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pkg_resources.DefaultProvider._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pkg_resources.DefaultProvider"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of DefaultProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_inferred"], "fullname": "pkg_resources.DefaultProvider._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pkg_resources.DefaultProvider"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of DefaultProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_resource_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.DefaultProvider.get_resource_stream", "name": "get_resource_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "arg_types": ["pkg_resources.DefaultProvider", "builtins.object", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_stream of DefaultProvider", "ret_type": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.DefaultProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.DefaultProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DistInfoDistribution": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.Distribution"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.DistInfoDistribution", "name": "DistInfoDistribution", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.DistInfoDistribution", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.DistInfoDistribution", "pkg_resources.Distribution", "builtins.object"], "names": {".class": "SymbolTable", "EQEQ": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.DistInfoDistribution.EQEQ", "name": "EQEQ", "type": null}}, "PKG_INFO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.DistInfoDistribution.PKG_INFO", "name": "PKG_INFO", "type": null}}, "__dep_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pkg_resources.DistInfoDistribution.__dep_map", "name": "__dep_map", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_compute_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.DistInfoDistribution._compute_dependencies", "name": "_compute_dependencies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.DistInfoDistribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compute_dependencies of DistInfoDistribution", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dep_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.DistInfoDistribution._dep_map", "name": "_dep_map", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.DistInfoDistribution._dep_map", "name": "_dep_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dep_map", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_parsed_pkg_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.DistInfoDistribution._parsed_pkg_info", "name": "_parsed_pkg_info", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.DistInfoDistribution._parsed_pkg_info", "name": "_parsed_pkg_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parsed_pkg_info", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_pkg_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.DistInfoDistribution._pkg_info", "name": "_pkg_info", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.DistInfoDistribution.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.DistInfoDistribution", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Distribution": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.Distribution", "name": "Distribution", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.Distribution", "builtins.object"], "names": {".class": "SymbolTable", "PKG_INFO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.Distribution.PKG_INFO", "name": "PKG_INFO", "type": "builtins.str"}}, "__dep_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution.__dep_map", "name": "__dep_map", "type": null}}, "__dir__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__dir__", "name": "__dir__", "type": null}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.Distribution", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Distribution", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.Distribution", "pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of Distribution", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.Distribution", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of Distribution", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.Distribution", "pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of Distribution", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of Distribution", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "location", "metadata", "project_name", "version", "py_version", "platform", "precedence"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "location", "metadata", "project_name", "version", "py_version", "platform", "precedence"], "arg_types": ["pkg_resources.Distribution", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._MetadataType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Distribution", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.Distribution", "pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of Distribution", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.Distribution", "pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of Distribution", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__ne__", "name": "__ne__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.Distribution", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ne__ of Distribution", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Distribution", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Distribution", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_dep_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution._build_dep_map", "name": "_build_dep_map", "type": null}}, "_dep_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.Distribution._dep_map", "name": "_dep_map", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.Distribution._dep_map", "name": "_dep_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dep_map", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_ep_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution._ep_map", "name": "_ep_map", "type": null}}, "_filter_extras": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dm"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pkg_resources.Distribution._filter_extras", "name": "_filter_extras", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dm"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_extras of Distribution", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_inferred"], "fullname": "pkg_resources.Distribution._filter_extras", "name": "_filter_extras", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dm"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_extras of Distribution", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_forgiving_parsed_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.Distribution._forgiving_parsed_version", "name": "_forgiving_parsed_version", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.Distribution._forgiving_parsed_version", "name": "_forgiving_parsed_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_forgiving_parsed_version", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution._get_metadata", "name": "_get_metadata", "type": null}}, "_get_metadata_path_for_display": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution._get_metadata_path_for_display", "name": "_get_metadata_path_for_display", "type": null}}, "_get_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution._get_version", "name": "_get_version", "type": null}}, "_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution._key", "name": "_key", "type": null}}, "_parsed_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution._parsed_version", "name": "_parsed_version", "type": null}}, "_provider": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution._provider", "name": "_provider", "type": null}}, "_reload_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution._reload_version", "name": "_reload_version", "type": null}}, "_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution._version", "name": "_version", "type": null}}, "activate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "path", "replace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.activate", "name": "activate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "path", "replace"], "arg_types": ["pkg_resources.Distribution", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "activate of Distribution", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "as_requirement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.as_requirement", "name": "as_requirement", "type": null}}, "check_version_conflict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.check_version_conflict", "name": "check_version_conflict", "type": null}}, "clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.clone", "name": "clone", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Distribution.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", "pkg_resources.IResourceProvider", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clone of Distribution", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Distribution.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Distribution.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}}, "egg_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.egg_name", "name": "egg_name", "type": null}}, "extras": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.Distribution.extras", "name": "extras", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.Distribution.extras", "name": "extras", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extras", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["cls", "filename", "metadata", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pkg_resources.Distribution.from_filename", "name": "from_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["cls", "filename", "metadata", "kw"], "arg_types": [{".class": "TypeType", "item": "pkg_resources.Distribution"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._MetadataType"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_filename of Distribution", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_inferred"], "fullname": "pkg_resources.Distribution.from_filename", "name": "from_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["cls", "filename", "metadata", "kw"], "arg_types": [{".class": "TypeType", "item": "pkg_resources.Distribution"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._MetadataType"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_filename of Distribution", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["cls", "location", "basename", "metadata", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pkg_resources.Distribution.from_location", "name": "from_location", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["cls", "location", "basename", "metadata", "kw"], "arg_types": [{".class": "TypeType", "item": "pkg_resources.Distribution"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._MetadataType"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_location of Distribution", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_inferred"], "fullname": "pkg_resources.Distribution.from_location", "name": "from_location", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["cls", "location", "basename", "metadata", "kw"], "arg_types": [{".class": "TypeType", "item": "pkg_resources.Distribution"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._MetadataType"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_location of Distribution", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_entry_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "group", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.get_entry_info", "name": "get_entry_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "group", "name"], "arg_types": ["pkg_resources.Distribution", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_info of Distribution", "ret_type": {".class": "UnionType", "items": ["pkg_resources.EntryPoint", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_entry_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.get_entry_map", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pkg_resources.Distribution.get_entry_map", "name": "get_entry_map", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "group"], "arg_types": ["pkg_resources.Distribution", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_map of Distribution", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.Distribution.get_entry_map", "name": "get_entry_map", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "group"], "arg_types": ["pkg_resources.Distribution", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_map of Distribution", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "pkg_resources.EntryPoint"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution.get_entry_map", "name": "get_entry_map", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.Distribution.get_entry_map", "name": "get_entry_map", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "group"], "arg_types": ["pkg_resources.Distribution", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_map of Distribution", "ret_type": {".class": "Instance", "args": ["builtins.str", "pkg_resources.EntryPoint"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution.get_entry_map", "name": "get_entry_map", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "group"], "arg_types": ["pkg_resources.Distribution", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_map of Distribution", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "pkg_resources.EntryPoint"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "group"], "arg_types": ["pkg_resources.Distribution", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_map of Distribution", "ret_type": {".class": "Instance", "args": ["builtins.str", "pkg_resources.EntryPoint"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "has_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.has_version", "name": "has_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_version of Distribution", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hashcmp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.Distribution.hashcmp", "name": "hashcmp", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.Distribution.hashcmp", "name": "hashcmp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hashcmp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "insert_on": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "loc", "replace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.insert_on", "name": "insert_on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "loc", "replace"], "arg_types": ["pkg_resources.Distribution", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert_on of Distribution", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.Distribution.key", "name": "key", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.Distribution.key", "name": "key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "load_entry_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "group", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.load_entry_point", "name": "load_entry_point", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "group", "name"], "arg_types": ["pkg_resources.Distribution", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_entry_point of Distribution", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ResolvedEntryPoint"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "location": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution.location", "name": "location", "type": null}}, "parsed_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.Distribution.parsed_version", "name": "parsed_version", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.Distribution.parsed_version", "name": "parsed_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parsed_version", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "platform": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution.platform", "name": "platform", "type": null}}, "precedence": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution.precedence", "name": "precedence", "type": null}}, "project_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution.project_name", "name": "project_name", "type": null}}, "py_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Distribution.py_version", "name": "py_version", "type": null}}, "requires": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "extras"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Distribution.requires", "name": "requires", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "extras"], "arg_types": ["pkg_resources.Distribution", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "requires of Distribution", "ret_type": {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.Distribution.version", "name": "version", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.Distribution.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Distribution.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DistributionNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.ResolutionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.DistributionNotFound", "name": "DistributionNotFound", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.DistributionNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.DistributionNotFound", "pkg_resources.ResolutionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.DistributionNotFound.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.DistributionNotFound"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of DistributionNotFound", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.DistributionNotFound._template", "name": "_template", "type": "builtins.str"}}, "report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.DistributionNotFound.report", "name": "report", "type": null}}, "req": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.DistributionNotFound.req", "name": "req", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.DistributionNotFound"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "req of DistributionNotFound", "ret_type": "pkg_resources.Requirement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.DistributionNotFound.req", "name": "req", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.DistributionNotFound"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "req of DistributionNotFound", "ret_type": "pkg_resources.Requirement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "requirers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.DistributionNotFound.requirers", "name": "requirers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.DistributionNotFound"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "requirers of DistributionNotFound", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.DistributionNotFound.requirers", "name": "requirers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.DistributionNotFound"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "requirers of DistributionNotFound", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "requirers_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.DistributionNotFound.requirers_str", "name": "requirers_str", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.DistributionNotFound.requirers_str", "name": "requirers_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "requirers_str", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.DistributionNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.DistributionNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EGG_DIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.EGG_DIST", "name": "EGG_DIST", "type": "builtins.int"}}, "EGG_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.EGG_NAME", "name": "EGG_NAME", "type": null}}, "EggInfoDistribution": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.Distribution"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.EggInfoDistribution", "name": "EggInfoDistribution", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.EggInfoDistribution", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.EggInfoDistribution", "pkg_resources.Distribution", "builtins.object"], "names": {".class": "SymbolTable", "_reload_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EggInfoDistribution._reload_version", "name": "_reload_version", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EggInfoDistribution.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EggInfoDistribution", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EggMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.ZipProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.EggMetadata", "name": "EggMetadata", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.EggMetadata", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.EggMetadata", "pkg_resources.ZipProvider", "pkg_resources.EggProvider", "pkg_resources.NullProvider", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "importer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EggMetadata.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "importer"], "arg_types": ["pkg_resources.EggMetadata", "zipimport.zipimporter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EggMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EggMetadata.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EggMetadata", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EggProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.NullProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.EggProvider", "name": "EggProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.EggProvider", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.EggProvider", "pkg_resources.NullProvider", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EggProvider.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "module"], "arg_types": ["pkg_resources.EggProvider", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ModuleLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EggProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_egg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EggProvider._set_egg", "name": "_set_egg", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.EggProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_egg of EggProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setup_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EggProvider._setup_prefix", "name": "_setup_prefix", "type": null}}, "egg_root": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.EggProvider.egg_root", "name": "egg_root", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EggProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EggProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EmptyProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.NullProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.EmptyProvider", "name": "EmptyProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.EmptyProvider", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.EmptyProvider", "pkg_resources.NullProvider", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EmptyProvider.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.EmptyProvider"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EmptyProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EmptyProvider._get", "name": "_get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.EmptyProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get of EmptyProvider", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.EmptyProvider._has", "name": "_has", "type": null}}, "_isdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.EmptyProvider._isdir", "name": "_isdir", "type": null}}, "_listdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EmptyProvider._listdir", "name": "_listdir", "type": null}}, "module_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pkg_resources.EmptyProvider.module_path", "name": "module_path", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EmptyProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EmptyProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EntryPoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.EntryPoint", "name": "EntryPoint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.EntryPoint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.EntryPoint", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "name", "module_name", "attrs", "extras", "dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EntryPoint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "name", "module_name", "attrs", "extras", "dist"], "arg_types": ["pkg_resources.EntryPoint", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EntryPoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EntryPoint.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.EntryPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of EntryPoint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EntryPoint.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.EntryPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of EntryPoint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_extras": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "extras_spec"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pkg_resources.EntryPoint._parse_extras", "name": "_parse_extras", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_inferred"], "fullname": "pkg_resources.EntryPoint._parse_extras", "name": "_parse_extras", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "extras_spec"], "arg_types": [{".class": "TypeType", "item": "pkg_resources.EntryPoint"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_extras", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "attrs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.EntryPoint.attrs", "name": "attrs", "type": null}}, "dist": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.EntryPoint.dist", "name": "dist", "type": null}}, "extras": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.EntryPoint.extras", "name": "extras", "type": null}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pkg_resources.EntryPoint.load", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "require", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pkg_resources.EntryPoint.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "require", "args", "kwargs"], "arg_types": ["pkg_resources.EntryPoint", "builtins.bool", {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of EntryPoint", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ResolvedEntryPoint"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "require", "env", "installer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.EntryPoint.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "require", "env", "installer"], "arg_types": ["pkg_resources.EntryPoint", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of EntryPoint", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ResolvedEntryPoint"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.EntryPoint.load", "name": "load", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "require", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.EntryPoint.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "require", "args", "kwargs"], "arg_types": ["pkg_resources.EntryPoint", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of EntryPoint", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ResolvedEntryPoint"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.EntryPoint.load", "name": "load", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "require", "env", "installer"], "arg_types": ["pkg_resources.EntryPoint", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of EntryPoint", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ResolvedEntryPoint"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "require", "args", "kwargs"], "arg_types": ["pkg_resources.EntryPoint", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of EntryPoint", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ResolvedEntryPoint"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "module_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.EntryPoint.module_name", "name": "module_name", "type": null}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.EntryPoint.name", "name": "name", "type": null}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "src", "dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pkg_resources.EntryPoint.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "src", "dist"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}}, "builtins.str", {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of EntryPoint", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_inferred"], "fullname": "pkg_resources.EntryPoint.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "src", "dist"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}}, "builtins.str", {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of EntryPoint", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}]}}}}, "parse_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "group", "lines", "dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pkg_resources.EntryPoint.parse_group", "name": "parse_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "group", "lines", "dist"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._NestedStr"}, {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_group of EntryPoint", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_inferred"], "fullname": "pkg_resources.EntryPoint.parse_group", "name": "parse_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "group", "lines", "dist"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._NestedStr"}, {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_group of EntryPoint", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}]}}}}, "parse_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "data", "dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pkg_resources.EntryPoint.parse_map", "name": "parse_map", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "data", "dist"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_map of EntryPoint", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_inferred"], "fullname": "pkg_resources.EntryPoint.parse_map", "name": "parse_map", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "data", "dist"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_map of EntryPoint", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}]}}}}, "pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.EntryPoint.pattern", "name": "pattern", "type": null}}, "require": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "env", "installer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EntryPoint.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "env", "installer"], "arg_types": ["pkg_resources.EntryPoint", {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require of EntryPoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.EntryPoint.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.EntryPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of EntryPoint", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ResolvedEntryPoint"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.EntryPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.EntryPoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Environment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.Environment", "name": "Environment", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.Environment", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Environment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Environment", "values": [], "variance": 0}, {".class": "UnionType", "items": ["pkg_resources.Distribution", "pkg_resources.Environment"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Environment", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Environment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Environment", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Environment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Environment", "values": [], "variance": 0}]}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.Environment", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of Environment", "ret_type": {".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iadd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment.__iadd__", "name": "__iadd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Environment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Environment", "values": [], "variance": 0}, {".class": "UnionType", "items": ["pkg_resources.Distribution", "pkg_resources.Environment"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iadd__ of Environment", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Environment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Environment", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Environment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Environment", "values": [], "variance": 0}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "search_path", "platform", "python"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "search_path", "platform", "python"], "arg_types": ["pkg_resources.Environment", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Environment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Environment"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of Environment", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_distmap": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pkg_resources.Environment._distmap", "name": "_distmap", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dist"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of Environment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "best_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment.best_match", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "req", "working_set", "installer", "replace_conflicting"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pkg_resources.Environment.best_match", "name": "best_match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "req", "working_set", "installer", "replace_conflicting"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", "pkg_resources.WorkingSet", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.best_match", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "best_match of Environment", "ret_type": {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.best_match", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "req", "working_set", "installer", "replace_conflicting"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.Environment.best_match", "name": "best_match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "req", "working_set", "installer", "replace_conflicting"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", "pkg_resources.WorkingSet", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.best_match#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "best_match of Environment", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.best_match#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.best_match#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Environment.best_match", "name": "best_match", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "req", "working_set", "installer", "replace_conflicting"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.Environment.best_match", "name": "best_match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "req", "working_set", "installer", "replace_conflicting"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", "pkg_resources.WorkingSet", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "best_match of Environment", "ret_type": {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Environment.best_match", "name": "best_match", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "req", "working_set", "installer", "replace_conflicting"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", "pkg_resources.WorkingSet", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.best_match#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "best_match of Environment", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.best_match#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.best_match#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "req", "working_set", "installer", "replace_conflicting"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", "pkg_resources.WorkingSet", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "best_match of Environment", "ret_type": {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "can_add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment.can_add", "name": "can_add", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dist"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_add of Environment", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "obtain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment.obtain", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "requirement", "installer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pkg_resources.Environment.obtain", "name": "obtain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "requirement", "installer"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Requirement"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.obtain", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obtain of Environment", "ret_type": {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.obtain", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "requirement", "installer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.Environment.obtain", "name": "obtain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "requirement", "installer"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.obtain#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obtain of Environment", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.obtain#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.obtain#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Environment.obtain", "name": "obtain", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "requirement", "installer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.Environment.obtain", "name": "obtain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "requirement", "installer"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Requirement"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obtain of Environment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Environment.obtain", "name": "obtain", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "requirement", "installer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.Environment.obtain", "name": "obtain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "requirement", "installer"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obtain of Environment", "ret_type": {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Environment.obtain", "name": "obtain", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "requirement", "installer"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.obtain#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obtain of Environment", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.obtain#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.Environment.obtain#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "requirement", "installer"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Requirement"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obtain of Environment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "requirement", "installer"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Requirement", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "obtain of Environment", "ret_type": {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "platform": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Environment.platform", "name": "platform", "type": null}}, "python": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Environment.python", "name": "python", "type": null}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dist"], "arg_types": ["pkg_resources.Environment", "pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of Environment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "search_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Environment.scan", "name": "scan", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "search_path"], "arg_types": ["pkg_resources.Environment", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scan of Environment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Environment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Environment", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExtractionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.ExtractionError", "name": "ExtractionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.ExtractionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.ExtractionError", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "cache_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pkg_resources.ExtractionError.cache_path", "name": "cache_path", "type": "builtins.str"}}, "manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pkg_resources.ExtractionError.manager", "name": "manager", "type": "pkg_resources.ResourceManager"}}, "original_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pkg_resources.ExtractionError.original_error", "name": "original_error", "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.ExtractionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.ExtractionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FileMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.EmptyProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.FileMetadata", "name": "FileMetadata", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.FileMetadata", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.FileMetadata", "pkg_resources.EmptyProvider", "pkg_resources.NullProvider", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.FileMetadata.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.FileMetadata", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FileMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_metadata_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.FileMetadata._get_metadata_path", "name": "_get_metadata_path", "type": null}}, "_warn_on_replacement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.FileMetadata._warn_on_replacement", "name": "_warn_on_replacement", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "arg_types": ["pkg_resources.FileMetadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn_on_replacement of FileMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.FileMetadata.get_metadata", "name": "get_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.FileMetadata", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_metadata of FileMetadata", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_metadata_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.FileMetadata.get_metadata_lines", "name": "get_metadata_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.FileMetadata", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_metadata_lines of FileMetadata", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.FileMetadata.has_metadata", "name": "has_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.FileMetadata", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_metadata of FileMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.FileMetadata.path", "name": "path", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.FileMetadata.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.FileMetadata", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IMetadataProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_metadata", 2], ["get_metadata_lines", 2], ["has_metadata", 2], ["metadata_isdir", 2], ["metadata_listdir", 2], ["run_script", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.IMetadataProvider", "name": "IMetadataProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pkg_resources.IMetadataProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.IMetadataProvider", "builtins.object"], "names": {".class": "SymbolTable", "get_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IMetadataProvider.get_metadata", "name": "get_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.IMetadataProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_metadata of IMetadataProvider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_metadata_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IMetadataProvider.get_metadata_lines", "name": "get_metadata_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.IMetadataProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_metadata_lines of IMetadataProvider", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IMetadataProvider.has_metadata", "name": "has_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.IMetadataProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_metadata of IMetadataProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "metadata_isdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IMetadataProvider.metadata_isdir", "name": "metadata_isdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.IMetadataProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metadata_isdir of IMetadataProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "metadata_listdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IMetadataProvider.metadata_listdir", "name": "metadata_listdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.IMetadataProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metadata_listdir of IMetadataProvider", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_script": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "script_name", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IMetadataProvider.run_script", "name": "run_script", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "script_name", "namespace"], "arg_types": ["pkg_resources.IMetadataProvider", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_script of IMetadataProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.IMetadataProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.IMetadataProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IResourceProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_metadata", 2], ["get_metadata_lines", 2], ["get_resource_filename", 2], ["get_resource_stream", 2], ["get_resource_string", 2], ["has_metadata", 2], ["has_resource", 2], ["metadata_isdir", 2], ["metadata_listdir", 2], ["resource_isdir", 2], ["resource_listdir", 2], ["run_script", 2]], "alt_promote": null, "bases": ["pkg_resources.IMetadataProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.IResourceProvider", "name": "IResourceProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pkg_resources.IResourceProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.IResourceProvider", "pkg_resources.IMetadataProvider", "builtins.object"], "names": {".class": "SymbolTable", "get_resource_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IResourceProvider.get_resource_filename", "name": "get_resource_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "arg_types": ["pkg_resources.IResourceProvider", "pkg_resources.ResourceManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_filename of IResourceProvider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IResourceProvider.get_resource_stream", "name": "get_resource_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "arg_types": ["pkg_resources.IResourceProvider", "pkg_resources.ResourceManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_stream of IResourceProvider", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ResourceStream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IResourceProvider.get_resource_string", "name": "get_resource_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "arg_types": ["pkg_resources.IResourceProvider", "pkg_resources.ResourceManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_string of IResourceProvider", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IResourceProvider.has_resource", "name": "has_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "arg_types": ["pkg_resources.IResourceProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_resource of IResourceProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource_isdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IResourceProvider.resource_isdir", "name": "resource_isdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "arg_types": ["pkg_resources.IResourceProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resource_isdir of IResourceProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource_listdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pkg_resources.IResourceProvider.resource_listdir", "name": "resource_listdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "arg_types": ["pkg_resources.IResourceProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resource_listdir of IResourceProvider", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.IResourceProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.IResourceProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "LoaderProtocol": {".class": "SymbolTableNode", "cross_ref": "_typeshed.importlib.LoaderProtocol", "kind": "Gdef", "module_public": false}, "MODULE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.MODULE", "name": "MODULE", "type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "MemoizedZipManifests": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.ZipManifests"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.MemoizedZipManifests", "name": "MemoizedZipManifests", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.MemoizedZipManifests", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.MemoizedZipManifests", "pkg_resources.ZipManifests", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.MemoizedZipManifests.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.MemoizedZipManifests", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of MemoizedZipManifests", "ret_type": {".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "manifest_mod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod", "name": "manifest_mod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["manifest", "mtime"]}}, "module_name": "pkg_resources", "mro": ["pkg_resources.MemoizedZipManifests.manifest_mod", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "manifest", "mtime"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "manifest", "mtime"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of manifest_mod", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of manifest_mod", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of manifest_mod", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of manifest_mod", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "manifest", "mtime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "manifest", "mtime"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of manifest_mod", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._NT", "id": -1, "name": "_NT", "namespace": "pkg_resources.MemoizedZipManifests.manifest_mod._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod._source", "name": "_source", "type": "builtins.str"}}, "manifest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod.manifest", "name": "manifest", "type": {".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "manifest-redefinition": {".class": "SymbolTableNode", "cross_ref": "pkg_resources.MemoizedZipManifests.manifest_mod.manifest", "kind": "<PERSON><PERSON><PERSON>"}, "mtime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod.mtime", "name": "mtime", "type": "builtins.float"}}, "mtime-redefinition": {".class": "SymbolTableNode", "cross_ref": "pkg_resources.MemoizedZipManifests.manifest_mod.mtime", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.manifest_mod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": "pkg_resources.MemoizedZipManifests.manifest_mod"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.MemoizedZipManifests.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.MemoizedZipManifests", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef", "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_public": false}, "NoDists": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.NoDists", "name": "NoDists", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.NoDists", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.NoDists", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NoDists.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.NoDists"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of NoDists", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fullpath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NoDists.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fullpath"], "arg_types": ["pkg_resources.NoDists", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of NoDists", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.NoDists.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.NoDists", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_public": false}, "NullProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.NullProvider", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.NullProvider", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "module"], "arg_types": ["pkg_resources.NullProvider", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ModuleLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NullProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "base", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider._fn", "name": "_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "base", "resource_name"], "arg_types": ["pkg_resources.NullProvider", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fn of NullProvider", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider._get", "name": "_get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.NullProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get of NullProvider", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_metadata_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider._get_metadata_path", "name": "_get_metadata_path", "type": null}}, "_has": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider._has", "name": "_has", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.NullProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has of NullProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_isdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider._isdir", "name": "_isdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.NullProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_isdir of NullProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_listdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider._listdir", "name": "_listdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.NullProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_listdir of NullProvider", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_resource_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pkg_resources.NullProvider._validate_resource_path", "name": "_validate_resource_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_resource_path of NullProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_inferred"], "fullname": "pkg_resources.NullProvider._validate_resource_path", "name": "_validate_resource_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_resource_path of NullProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "egg_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pkg_resources.NullProvider.egg_info", "name": "egg_info", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "egg_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pkg_resources.NullProvider.egg_name", "name": "egg_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.get_metadata", "name": "get_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.NullProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_metadata of NullProvider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_metadata_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.get_metadata_lines", "name": "get_metadata_lines", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.NullProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_metadata_lines of NullProvider", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.get_resource_filename", "name": "get_resource_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "arg_types": ["pkg_resources.NullProvider", "pkg_resources.ResourceManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_filename of NullProvider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.get_resource_stream", "name": "get_resource_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "arg_types": ["pkg_resources.NullProvider", "pkg_resources.ResourceManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_stream of NullProvider", "ret_type": "typing.BinaryIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.get_resource_string", "name": "get_resource_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "arg_types": ["pkg_resources.NullProvider", "pkg_resources.ResourceManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_string of NullProvider", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.has_metadata", "name": "has_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.NullProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_metadata of NullProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.has_resource", "name": "has_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "arg_types": ["pkg_resources.NullProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_resource of NullProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pkg_resources.NullProvider.loader", "name": "loader", "type": {".class": "UnionType", "items": ["_typeshed.importlib.LoaderProtocol", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "metadata_isdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.metadata_isdir", "name": "metadata_isdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.NullProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metadata_isdir of NullProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "metadata_listdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.metadata_listdir", "name": "metadata_listdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["pkg_resources.NullProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "metadata_listdir of NullProvider", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "module_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.NullProvider.module_path", "name": "module_path", "type": null}}, "resource_isdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.resource_isdir", "name": "resource_isdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "arg_types": ["pkg_resources.NullProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resource_isdir of NullProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource_listdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.resource_listdir", "name": "resource_listdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "arg_types": ["pkg_resources.NullProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resource_listdir of NullProvider", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_script": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "script_name", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.NullProvider.run_script", "name": "run_script", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "script_name", "namespace"], "arg_types": ["pkg_resources.NullProvider", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_script of NullProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.NullProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.NullProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PEP440Warning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.PEP440Warning", "name": "PEP440Warning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.PEP440Warning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.PEP440Warning", "builtins.RuntimeWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.PEP440Warning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.PEP440Warning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PY_MAJOR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.PY_MAJOR", "name": "PY_MAJOR", "type": null}}, "PathMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.DefaultProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.PathMetadata", "name": "PathMetadata", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.PathMetadata", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.PathMetadata", "pkg_resources.DefaultProvider", "pkg_resources.EggProvider", "pkg_resources.NullProvider", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "path", "egg_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.PathMetadata.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "path", "egg_info"], "arg_types": ["pkg_resources.PathMetadata", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PathMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.PathMetadata.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.PathMetadata", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PkgResourcesDeprecationWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Warning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.PkgResourcesDeprecationWarning", "name": "PkgResourcesDeprecationWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.PkgResourcesDeprecationWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.PkgResourcesDeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.PkgResourcesDeprecationWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.PkgResourcesDeprecationWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_public": false}, "Requirement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["packaging.requirements.Requirement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.Requirement", "name": "Requirement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.Requirement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.Requirement", "packaging.requirements.Requirement", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Requirement.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.Requirement", {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "TypeAliasType", "args": [], "type_ref": "packaging.specifiers.UnparsedVersion"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of Requirement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Requirement.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.Requirement", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Requirement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Requirement.__hash", "name": "__hash", "type": null}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Requirement.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.Requirement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of Requirement", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "requirement_string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Requirement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "requirement_string"], "arg_types": ["pkg_resources.Requirement", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Requirement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Requirement.__ne__", "name": "__ne__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.Requirement", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ne__ of Requirement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.Requirement.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Requirement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Requirement", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extras": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pkg_resources.Requirement.extras", "name": "extras", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "hashCmp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Requirement.hashCmp", "name": "hashCmp", "type": null}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Requirement.key", "name": "key", "type": null}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pkg_resources.Requirement.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of Requirement", "ret_type": "pkg_resources.Requirement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_inferred"], "fullname": "pkg_resources.Requirement.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of Requirement", "ret_type": "pkg_resources.Requirement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "project_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Requirement.project_name", "name": "project_name", "type": null}}, "specs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Requirement.specs", "name": "specs", "type": null}}, "unsafe_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.Requirement.unsafe_name", "name": "unsafe_name", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.Requirement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.Requirement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequirementParseError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["packaging.requirements.InvalidRequirement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.RequirementParseError", "name": "RequirementParseError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.RequirementParseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.RequirementParseError", "packaging.requirements.InvalidRequirement", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.RequirementParseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.RequirementParseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResolutionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.ResolutionError", "name": "ResolutionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.ResolutionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.ResolutionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResolutionError.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.ResolutionError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ResolutionError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.ResolutionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.ResolutionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.ResourceManager", "name": "ResourceManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.ResourceManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.ResourceManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResourceManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_warn_unsafe_extraction_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pkg_resources.ResourceManager._warn_unsafe_extraction_path", "name": "_warn_unsafe_extraction_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn_unsafe_extraction_path of ResourceManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_inferred"], "fullname": "pkg_resources.ResourceManager._warn_unsafe_extraction_path", "name": "_warn_unsafe_extraction_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn_unsafe_extraction_path of ResourceManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cached_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pkg_resources.ResourceManager.cached_files", "name": "cached_files", "type": {".class": "Instance", "args": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "cleanup_resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.cleanup_resources", "name": "cleanup_resources", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "force"], "arg_types": ["pkg_resources.ResourceManager", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cleanup_resources of ResourceManager", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extraction_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.extraction_error", "name": "extraction_error", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.ResourceManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extraction_error of ResourceManager", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extraction_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pkg_resources.ResourceManager.extraction_path", "name": "extraction_path", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_cache_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "archive_name", "names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.get_cache_path", "name": "get_cache_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "archive_name", "names"], "arg_types": ["pkg_resources.ResourceManager", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cache_path of ResourceManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "postprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tempname", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.postprocess", "name": "postprocess", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tempname", "filename"], "arg_types": ["pkg_resources.ResourceManager", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "postprocess of ResourceManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource_exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.resource_exists", "name": "resource_exists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "arg_types": ["pkg_resources.ResourceManager", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._PkgReqType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resource_exists of ResourceManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.resource_filename", "name": "resource_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "arg_types": ["pkg_resources.ResourceManager", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._PkgReqType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resource_filename of ResourceManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource_isdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.resource_isdir", "name": "resource_isdir", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "arg_types": ["pkg_resources.ResourceManager", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._PkgReqType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resource_isdir of ResourceManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource_listdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.resource_listdir", "name": "resource_listdir", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "arg_types": ["pkg_resources.ResourceManager", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._PkgReqType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resource_listdir of ResourceManager", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.resource_stream", "name": "resource_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "arg_types": ["pkg_resources.ResourceManager", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._PkgReqType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resource_stream of ResourceManager", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ResourceStream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.resource_string", "name": "resource_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "package_or_requirement", "resource_name"], "arg_types": ["pkg_resources.ResourceManager", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._PkgReqType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resource_string of ResourceManager", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_extraction_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ResourceManager.set_extraction_path", "name": "set_extraction_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pkg_resources.ResourceManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_extraction_path of ResourceManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.ResourceManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.ResourceManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SOURCE_DIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.SOURCE_DIST", "name": "SOURCE_DIST", "type": "builtins.int"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef", "module_public": false}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrOrBytesPath", "kind": "Gdef", "module_public": false}, "StrPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrPath", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnknownExtra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.ResolutionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.UnknownExtra", "name": "UnknownExtra", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.UnknownExtra", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.UnknownExtra", "pkg_resources.ResolutionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.UnknownExtra.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.UnknownExtra", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VersionConflict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.ResolutionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.VersionConflict", "name": "VersionConflict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.VersionConflict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.VersionConflict", "pkg_resources.ResolutionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.VersionConflict._template", "name": "_template", "type": "builtins.str"}}, "dist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.VersionConflict.dist", "name": "dist", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.VersionConflict"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dist of VersionConflict", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.VersionConflict.dist", "name": "dist", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.VersionConflict"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dist of VersionConflict", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.VersionConflict.report", "name": "report", "type": null}}, "req": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.VersionConflict.req", "name": "req", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.VersionConflict"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "req of VersionConflict", "ret_type": "pkg_resources.Requirement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.VersionConflict.req", "name": "req", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.VersionConflict"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "req of VersionConflict", "ret_type": "pkg_resources.Requirement", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "required_by"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.VersionConflict.with_context", "name": "with_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "required_by"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.VersionConflict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.VersionConflict", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["pkg_resources.Distribution", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_context of VersionConflict", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.VersionConflict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.VersionConflict", "values": [], "variance": 0}, "pkg_resources.ContextualVersionConflict"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.VersionConflict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.VersionConflict", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.VersionConflict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.VersionConflict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WRITE_SUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.WRITE_SUPPORT", "name": "WRITE_SUPPORT", "type": "builtins.bool"}}, "WorkingSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.WorkingSet", "name": "WorkingSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.WorkingSet", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pkg_resources.WorkingSet", "pkg_resources.Distribution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of WorkingSet", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pkg_resources.WorkingSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getstate__ of WorkingSet", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Distribution"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "entries"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "entries"], "arg_types": ["pkg_resources.WorkingSet", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WorkingSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.WorkingSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of WorkingSet", "ret_type": {".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "e_k_b_n_c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "e_k_b_n_c"], "arg_types": ["pkg_resources.WorkingSet", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setstate__ of WorkingSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_added_new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet._added_new", "name": "_added_new", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dist"], "arg_types": ["pkg_resources.WorkingSet", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_added_new of WorkingSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_from_requirements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "req_spec"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pkg_resources.WorkingSet._build_from_requirements", "name": "_build_from_requirements", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_inferred"], "fullname": "pkg_resources.WorkingSet._build_from_requirements", "name": "_build_from_requirements", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "req_spec"], "arg_types": [{".class": "TypeType", "item": "pkg_resources.WorkingSet"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_from_requirements", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_build_master": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pkg_resources.WorkingSet._build_master", "name": "_build_master", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_inferred"], "fullname": "pkg_resources.WorkingSet._build_master", "name": "_build_master", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pkg_resources.WorkingSet"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_master", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_resolve_dist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "req", "best", "replace_conflicting", "env", "installer", "required_by", "to_activate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet._resolve_dist", "name": "_resolve_dist", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "req", "best", "replace_conflicting", "env", "installer", "required_by", "to_activate"], "arg_types": ["pkg_resources.WorkingSet", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_dist of WorkingSet", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "dist", "entry", "insert", "replace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "dist", "entry", "insert", "replace"], "arg_types": ["pkg_resources.WorkingSet", "pkg_resources.Distribution", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of WorkingSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "entry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.add_entry", "name": "add_entry", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "entry"], "arg_types": ["pkg_resources.WorkingSet", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_entry of WorkingSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "by_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pkg_resources.WorkingSet.by_key", "name": "by_key", "type": {".class": "Instance", "args": ["builtins.str", "pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "callbacks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pkg_resources.WorkingSet.callbacks", "name": "callbacks", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Distribution"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "entries": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pkg_resources.WorkingSet.entries", "name": "entries", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "entry_keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pkg_resources.WorkingSet.entry_keys", "name": "entry_keys", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "req"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "req"], "arg_types": ["pkg_resources.WorkingSet", "pkg_resources.Requirement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find of WorkingSet", "ret_type": {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_plugins": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.find_plugins", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "plugin_env", "full_env", "installer", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pkg_resources.WorkingSet.find_plugins", "name": "find_plugins", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "plugin_env", "full_env", "installer", "fallback"], "arg_types": ["pkg_resources.WorkingSet", "pkg_resources.Environment", {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_plugins of WorkingSet", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["pkg_resources.Distribution", "builtins.Exception"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "plugin_env", "full_env", "installer", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.WorkingSet.find_plugins", "name": "find_plugins", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "plugin_env", "full_env", "installer", "fallback"], "arg_types": ["pkg_resources.WorkingSet", "pkg_resources.Environment", {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_plugins of WorkingSet", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["pkg_resources.Distribution", "builtins.Exception"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.WorkingSet.find_plugins", "name": "find_plugins", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 3, 5], "arg_names": ["self", "plugin_env", "full_env", "installer", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.WorkingSet.find_plugins", "name": "find_plugins", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 5], "arg_names": ["self", "plugin_env", "full_env", "installer", "fallback"], "arg_types": ["pkg_resources.WorkingSet", "pkg_resources.Environment", {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_plugins of WorkingSet", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["pkg_resources.Distribution", "builtins.Exception"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.WorkingSet.find_plugins", "name": "find_plugins", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "plugin_env", "full_env", "installer", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.WorkingSet.find_plugins", "name": "find_plugins", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "plugin_env", "full_env", "installer", "fallback"], "arg_types": ["pkg_resources.WorkingSet", "pkg_resources.Environment", {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_plugins of WorkingSet", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["pkg_resources.Distribution", "builtins.Exception"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.WorkingSet.find_plugins", "name": "find_plugins", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "plugin_env", "full_env", "installer", "fallback"], "arg_types": ["pkg_resources.WorkingSet", "pkg_resources.Environment", {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_plugins of WorkingSet", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["pkg_resources.Distribution", "builtins.Exception"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 5], "arg_names": ["self", "plugin_env", "full_env", "installer", "fallback"], "arg_types": ["pkg_resources.WorkingSet", "pkg_resources.Environment", {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_plugins of WorkingSet", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["pkg_resources.Distribution", "builtins.Exception"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.find_plugins#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "plugin_env", "full_env", "installer", "fallback"], "arg_types": ["pkg_resources.WorkingSet", "pkg_resources.Environment", {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_plugins of WorkingSet", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["pkg_resources.Distribution", "builtins.Exception"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "iter_entry_points": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "group", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.iter_entry_points", "name": "iter_entry_points", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "group", "name"], "arg_types": ["pkg_resources.WorkingSet", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_entry_points of WorkingSet", "ret_type": {".class": "Instance", "args": ["pkg_resources.EntryPoint"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "normalized_to_canonical_keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pkg_resources.WorkingSet.normalized_to_canonical_keys", "name": "normalized_to_canonical_keys", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "require": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "requirements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.require", "name": "require", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "requirements"], "arg_types": ["pkg_resources.WorkingSet", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._NestedStr"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "require of WorkingSet", "ret_type": {".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.resolve", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "requirements", "env", "installer", "replace_conflicting", "extras"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pkg_resources.WorkingSet.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "requirements", "env", "installer", "replace_conflicting", "extras"], "arg_types": ["pkg_resources.WorkingSet", {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of WorkingSet", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "requirements", "env", "installer", "replace_conflicting", "extras"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.WorkingSet.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "requirements", "env", "installer", "replace_conflicting", "extras"], "arg_types": ["pkg_resources.WorkingSet", {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of WorkingSet", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.WorkingSet.resolve", "name": "resolve", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 3, 5, 5], "arg_names": ["self", "requirements", "env", "installer", "replace_conflicting", "extras"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.WorkingSet.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 5, 5], "arg_names": ["self", "requirements", "env", "installer", "replace_conflicting", "extras"], "arg_types": ["pkg_resources.WorkingSet", {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of WorkingSet", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.WorkingSet.resolve", "name": "resolve", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "requirements", "env", "installer", "replace_conflicting", "extras"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.WorkingSet.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "requirements", "env", "installer", "replace_conflicting", "extras"], "arg_types": ["pkg_resources.WorkingSet", {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of WorkingSet", "ret_type": {".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.WorkingSet.resolve", "name": "resolve", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "requirements", "env", "installer", "replace_conflicting", "extras"], "arg_types": ["pkg_resources.WorkingSet", {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of WorkingSet", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 5, 5], "arg_names": ["self", "requirements", "env", "installer", "replace_conflicting", "extras"], "arg_types": ["pkg_resources.WorkingSet", {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "type_ref": "pkg_resources._StrictInstallerType"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of WorkingSet", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.WorkingSet.resolve#1", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "requirements", "env", "installer", "replace_conflicting", "extras"], "arg_types": ["pkg_resources.WorkingSet", {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["pkg_resources.Environment", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._InstallerType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of WorkingSet", "ret_type": {".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "run_script": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "requires", "script_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.run_script", "name": "run_script", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "requires", "script_name"], "arg_types": ["pkg_resources.WorkingSet", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_script of WorkingSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subscribe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "callback", "existing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.WorkingSet.subscribe", "name": "subscribe", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "callback", "existing"], "arg_types": ["pkg_resources.WorkingSet", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Distribution"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subscribe of WorkingSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.WorkingSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.WorkingSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZipManifests": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources.MemoizedZipManifests.manifest_mod"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.ZipManifests", "name": "ZipManifests", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipManifests", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.ZipManifests", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pkg_resources.ZipManifests.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "pkg_resources.ZipManifests"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of ZipManifests", "ret_type": {".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_inferred"], "fullname": "pkg_resources.ZipManifests.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "path"], "arg_types": [{".class": "TypeType", "item": "pkg_resources.ZipManifests"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of ZipManifests", "ret_type": {".class": "Instance", "args": ["builtins.str", "zipfile.ZipInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.ZipManifests.load", "name": "load", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.ZipManifests.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.ZipManifests", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZipProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pkg_resources.EggProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources.ZipProvider", "name": "ZipProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources.ZipProvider", "pkg_resources.EggProvider", "pkg_resources.NullProvider", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "module"], "arg_types": ["pkg_resources.ZipProvider", "pkg_resources._ZipLoaderModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ZipProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dirindex": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.ZipProvider._dirindex", "name": "_dirindex", "type": null}}, "_eager_to_zip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider._eager_to_zip", "name": "_eager_to_zip", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "arg_types": ["pkg_resources.ZipProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_eager_to_zip of ZipProvider", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "zip_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider._extract_resource", "name": "_extract_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "zip_path"], "arg_types": ["pkg_resources.ZipProvider", "pkg_resources.ResourceManager", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_resource of ZipProvider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_date_and_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["zip_stat"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pkg_resources.ZipProvider._get_date_and_size", "name": "_get_date_and_size", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_inferred"], "fullname": "pkg_resources.ZipProvider._get_date_and_size", "name": "_get_date_and_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["zip_stat"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_date_and_size", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_eager_resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider._get_eager_resources", "name": "_get_eager_resources", "type": null}}, "_has": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fspath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider._has", "name": "_has", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fspath"], "arg_types": ["pkg_resources.ZipProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_has of ZipProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider._index", "name": "_index", "type": null}}, "_is_current": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "zip_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider._is_current", "name": "_is_current", "type": null}}, "_isdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fspath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider._isdir", "name": "_isdir", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fspath"], "arg_types": ["pkg_resources.ZipProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_isdir of ZipProvider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_listdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fspath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider._listdir", "name": "_listdir", "type": null}}, "_parts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "zip_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider._parts", "name": "_parts", "type": null}}, "_resource_to_zip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider._resource_to_zip", "name": "_resource_to_zip", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "arg_types": ["pkg_resources.ZipProvider", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resource_to_zip of ZipProvider", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_zip_manifests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pkg_resources.ZipProvider._zip_manifests", "name": "_zip_manifests", "type": null}}, "_zipinfo_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fspath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider._zipinfo_name", "name": "_zipinfo_name", "type": null}}, "eagers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pkg_resources.ZipProvider.eagers", "name": "eagers", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_resource_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ZipProvider.get_resource_filename", "name": "get_resource_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "manager", "resource_name"], "arg_types": ["pkg_resources.ZipProvider", "pkg_resources.ResourceManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_filename of ZipProvider", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "pkg_resources.ZipProvider.loader", "name": "loader", "type": "zipimport.zipimporter"}}, "zip_pre": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.ZipProvider.zip_pre", "name": "zip_pre", "type": null}}, "zipinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pkg_resources.ZipProvider.zipinfo", "name": "zipinfo", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_inferred"], "fullname": "pkg_resources.ZipProvider.zipinfo", "name": "zipinfo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "zipinfo", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources.ZipProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources.ZipProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AdapterT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._AdapterT", "name": "_AdapterT", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "pkg_resources._DistFinderType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ProviderFactoryType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "pkg_resources._NSHandlerType"}], "variance": 0}}, "_DistFinderType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": 1, "name": "_T", "namespace": "pkg_resources._DistFinderType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "pkg_resources._DistFinderType", "line": 123, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": 1, "name": "_T", "namespace": "pkg_resources._DistFinderType", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_DistributionT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "name": "_DistributionT", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}}, "_EPDistType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pkg_resources._EPDistType", "line": 115, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._PkgReqType"}], "uses_pep604_syntax": false}}}, "_InstallerType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pkg_resources._InstallerType", "line": 113, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Requirement"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_LOCALE_ENCODING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources._LOCALE_ENCODING", "name": "_LOCALE_ENCODING", "type": null}}, "_MetadataType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pkg_resources._MetadataType", "line": 116, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["pkg_resources.IResourceProvider", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_ModuleLike": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pkg_resources._ModuleLike", "line": 120, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.object", "types.ModuleType"], "uses_pep604_syntax": false}}}, "_NSHandlerType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": 1, "name": "_T", "namespace": "pkg_resources._NSHandlerType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "pkg_resources._NSHandlerType", "line": 124, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": 1, "name": "_T", "namespace": "pkg_resources._NSHandlerType", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str", "builtins.str", "types.ModuleType"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_NestedStr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pkg_resources._NestedStr", "line": 111, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._NestedStr"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}}}, "_PEP440_FALLBACK": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources._PEP440_FALLBACK", "name": "_PEP440_FALLBACK", "type": null}}, "_PkgReqType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pkg_resources._PkgReqType", "line": 114, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "pkg_resources.Requirement"], "uses_pep604_syntax": false}}}, "_ProviderFactoryType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pkg_resources._ProviderFactoryType", "line": 122, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "pkg_resources.IResourceProvider", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ReqExtras": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["pkg_resources.Requirement", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources._ReqExtras", "name": "_ReqExtras", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pkg_resources._ReqExtras", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources._ReqExtras", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "markers_pass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "req", "extras"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._ReqExtras.markers_pass", "name": "markers_pass", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "req", "extras"], "arg_types": ["pkg_resources._ReqExtras", "pkg_resources.Requirement", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "markers_pass of _ReqExtras", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._ReqExtras.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources._ReqExtras", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ResolvedEntryPoint": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pkg_resources._ResolvedEntryPoint", "line": 117, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "_ResourceStream": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pkg_resources._ResourceStream", "line": 118, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "_StrictInstallerType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": 1, "name": "_DistributionT", "namespace": "pkg_resources._StrictInstallerType", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "column": 0, "fullname": "pkg_resources._StrictInstallerType", "line": 112, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pkg_resources.Requirement"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": 1, "name": "_DistributionT", "namespace": "pkg_resources._StrictInstallerType", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_ZipLoaderModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__loader__", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pkg_resources._ZipLoaderModule", "name": "_ZipLoaderModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pkg_resources._ZipLoaderModule", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pkg_resources", "mro": ["pkg_resources._ZipLoaderModule", "builtins.object"], "names": {".class": "SymbolTable", "__loader__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "pkg_resources._ZipLoaderModule.__loader__", "name": "__loader__", "type": "zipimport.zipimporter"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._ZipLoaderModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pkg_resources._ZipLoaderModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.__all__", "name": "__all__", "type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pkg_resources.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pkg_resources.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pkg_resources.__file__", "name": "__file__", "type": "builtins.str"}}, "__getstate__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getstate__", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pkg_resources.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pkg_resources.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pkg_resources.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__resource_manager": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.__resource_manager", "name": "__resource_manager", "type": null}}, "__setstate__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setstate__", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pkg_resources.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_always_object": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["classes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._always_object", "name": "_always_object", "type": null}}, "_bypass_ensure_directory": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._bypass_ensure_directory", "name": "_bypass_ensure_directory", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bypass_ensure_directory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_call_aside": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["f", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._call_aside", "name": "_call_aside", "type": null}}, "_cygwin_patch": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._cygwin_patch", "name": "_cygwin_patch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cygwin_patch", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_declare_state": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["vartype", "varname", "initial_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._declare_state", "name": "_declare_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["vartype", "varname", "initial_value"], "arg_types": ["builtins.str", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": -1, "name": "_T", "namespace": "pkg_resources._declare_state", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_declare_state", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": -1, "name": "_T", "namespace": "pkg_resources._declare_state", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": -1, "name": "_T", "namespace": "pkg_resources._declare_state", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_distributionImpl": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources._distributionImpl", "name": "_distributionImpl", "type": null}}, "_distribution_finders": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pkg_resources._distribution_finders", "name": "_distribution_finders", "type": {".class": "Instance", "args": ["builtins.type", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "pkg_resources._DistFinderType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_find_adapter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["registry", "ob"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._find_adapter", "name": "_find_adapter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["registry", "ob"], "arg_types": [{".class": "Instance", "args": ["builtins.type", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._AdapterT", "id": -1, "name": "_AdapterT", "namespace": "pkg_resources._find_adapter", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "pkg_resources._DistFinderType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ProviderFactoryType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "pkg_resources._NSHandlerType"}], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_adapter", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._AdapterT", "id": -1, "name": "_AdapterT", "namespace": "pkg_resources._find_adapter", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "pkg_resources._DistFinderType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ProviderFactoryType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "pkg_resources._NSHandlerType"}], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._AdapterT", "id": -1, "name": "_AdapterT", "namespace": "pkg_resources._find_adapter", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "pkg_resources._DistFinderType"}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ProviderFactoryType"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "pkg_resources._NSHandlerType"}], "variance": 0}]}}}, "_forgiving_version": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._forgiving_version", "name": "_forgiving_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["version"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_forgiving_version", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_ns": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["packageName", "path_item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._handle_ns", "name": "_handle_ns", "type": null}}, "_imp": {".class": "SymbolTableNode", "cross_ref": "_imp", "kind": "Gdef", "module_public": false}, "_initialize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["g"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pkg_resources._initialize", "name": "_initialize", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["g"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_initialize", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources._initialize", "name": "_initialize", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "_initialize_master_working_set": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pkg_resources._initialize_master_working_set", "name": "_initialize_master_working_set", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_initialize_master_working_set", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources._initialize_master_working_set", "name": "_initialize_master_working_set", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "_is_egg_path": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._is_egg_path", "name": "_is_egg_path", "type": null}}, "_is_unpacked_egg": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._is_unpacked_egg", "name": "_is_unpacked_egg", "type": null}}, "_is_zip_egg": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._is_zip_egg", "name": "_is_zip_egg", "type": null}}, "_macos_arch": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["machine"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._macos_arch", "name": "_macos_arch", "type": null}}, "_macos_vers": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pkg_resources._macos_vers", "name": "_macos_vers", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources._macos_vers", "name": "_macos_vers", "type": null}}}, "_mkstemp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._mkstemp", "name": "_mkstemp", "type": null}}, "_namespace_handlers": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pkg_resources._namespace_handlers", "name": "_namespace_handlers", "type": {".class": "Instance", "args": ["builtins.type", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "pkg_resources._NSHandlerType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_namespace_packages": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pkg_resources._namespace_packages", "name": "_namespace_packages", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_normalize_cached": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pkg_resources._normalize_cached", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_mypy_only"], "fullname": "pkg_resources._normalize_cached", "name": "_normalize_cached", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_cached", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "pkg_resources._normalize_cached", "name": "_normalize_cached", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_cached", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources._normalize_cached", "name": "_normalize_cached", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "pkg_resources._normalize_cached", "name": "_normalize_cached", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.BytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_cached", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources._normalize_cached", "name": "_normalize_cached", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_cached", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.BytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_cached", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_parents": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._parents", "name": "_parents", "type": null}}, "_provider_factories": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pkg_resources._provider_factories", "name": "_provider_factories", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.object"}, {".class": "TypeType", "item": "types.ModuleType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ProviderFactoryType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_read_utf8_with_fallback": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["file", "fallback_encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._read_utf8_with_fallback", "name": "_read_utf8_with_fallback", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["file", "fallback_encoding"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_utf8_with_fallback", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_rebuild_mod_path": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["orig_path", "package_name", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._rebuild_mod_path", "name": "_rebuild_mod_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["orig_path", "package_name", "module"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "types.ModuleType"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rebuild_mod_path", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_safe_segment": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["segment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._safe_segment", "name": "_safe_segment", "type": null}}, "_set_parent_ns": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["packageName"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._set_parent_ns", "name": "_set_parent_ns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["packageName"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_parent_ns", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sget_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._sget_dict", "name": "_sget_dict", "type": null}}, "_sget_none": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources._sget_none", "name": "_sget_none", "type": null}}, "_sget_object": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._sget_object", "name": "_sget_object", "type": null}}, "_sset_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["key", "ob", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._sset_dict", "name": "_sset_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["key", "ob", "state"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sset_dict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sset_none": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources._sset_none", "name": "_sset_none", "type": null}}, "_sset_object": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["key", "ob", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._sset_object", "name": "_sset_object", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["key", "ob", "state"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sset_object", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_state_vars": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pkg_resources._state_vars", "name": "_state_vars", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_user_cache_dir": {".class": "SymbolTableNode", "cross_ref": "platformdirs.user_cache_dir", "kind": "Gdef", "module_public": false}, "_version_from_file": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources._version_from_file", "name": "_version_from_file", "type": null}}, "add_activation_listener": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.add_activation_listener", "name": "add_activation_listener", "type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cleanup_resources": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.cleanup_resources", "name": "cleanup_resources", "type": null}}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "compatible_platforms": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["provided", "required"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.compatible_platforms", "name": "compatible_platforms", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["provided", "required"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compatible_platforms", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "darwinVersionString": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.darwinVersionString", "name": "darwinVersionString", "type": null}}, "declare_namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["packageName"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.declare_namespace", "name": "declare_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["packageName"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "declare_namespace", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dist_factory": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["path_item", "entry", "only"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.dist_factory", "name": "dist_factory", "type": null}}, "distributions_from_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.distributions_from_metadata", "name": "distributions_from_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "distributions_from_metadata", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop_comment": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pkg_resources.drop_comment", "name": "drop_comment", "type": {".class": "AnyType", "missing_import_name": "pkg_resources.drop_comment", "source_any": null, "type_of_any": 3}}}, "email": {".class": "SymbolTableNode", "cross_ref": "email", "kind": "Gdef", "module_public": false}, "empty_provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.empty_provider", "name": "empty_provider", "type": null}}, "ensure_directory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.ensure_directory", "name": "ensure_directory", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_directory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "errno": {".class": "SymbolTableNode", "cross_ref": "errno", "kind": "Gdef", "module_public": false}, "evaluate_marker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["text", "extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.evaluate_marker", "name": "evaluate_marker", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["text", "extra"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "evaluate_marker", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "file_ns_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["importer", "path_item", "packageName", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.file_ns_handler", "name": "file_ns_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["importer", "path_item", "packageName", "module"], "arg_types": ["builtins.object", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, "builtins.str", "types.ModuleType"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "file_ns_handler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_distributions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["path_item", "only"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.find_distributions", "name": "find_distributions", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["path_item", "only"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_distributions", "ret_type": {".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_eggs_in_zip": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["importer", "path_item", "only"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.find_eggs_in_zip", "name": "find_eggs_in_zip", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["importer", "path_item", "only"], "arg_types": ["zipimport.zipimporter", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_eggs_in_zip", "ret_type": {".class": "Instance", "args": ["pkg_resources.Distribution"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_nothing": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["importer", "path_item", "only"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.find_nothing", "name": "find_nothing", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["importer", "path_item", "only"], "arg_types": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_nothing", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_on_path": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["importer", "path_item", "only"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.find_on_path", "name": "find_on_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["importer", "path_item", "only"], "arg_types": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_on_path", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fixup_namespace_packages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["path_item", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.fixup_namespace_packages", "name": "fixup_namespace_packages", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["path_item", "parent"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fixup_namespace_packages", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "get_build_platform": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.get_build_platform", "name": "get_build_platform", "type": null}}, "get_default_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.get_default_cache", "name": "get_default_cache", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_cache", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_distribution": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pkg_resources.get_distribution", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pkg_resources.get_distribution", "name": "get_distribution", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dist"], "arg_types": [{".class": "UnionType", "items": ["pkg_resources.Distribution", {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._PkgReqType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_distribution", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.get_distribution", "name": "get_distribution", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dist"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.get_distribution#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_distribution", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.get_distribution#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.get_distribution#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.get_distribution", "name": "get_distribution", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dist"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.get_distribution", "name": "get_distribution", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dist"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._PkgReqType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_distribution", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.get_distribution", "name": "get_distribution", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["dist"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.get_distribution#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_distribution", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.get_distribution#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._DistributionT", "id": -1, "name": "_DistributionT", "namespace": "pkg_resources.get_distribution#0", "upper_bound": "pkg_resources.Distribution", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dist"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._PkgReqType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_distribution", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_entry_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["dist", "group", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.get_entry_info", "name": "get_entry_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["dist", "group", "name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._EPDistType"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_info", "ret_type": {".class": "UnionType", "items": ["pkg_resources.EntryPoint", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_entry_map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pkg_resources.get_entry_map", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["dist", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pkg_resources.get_entry_map", "name": "get_entry_map", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["dist", "group"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._EPDistType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_map", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["dist", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.get_entry_map", "name": "get_entry_map", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["dist", "group"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._EPDistType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_map", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "pkg_resources.EntryPoint"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.get_entry_map", "name": "get_entry_map", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["dist", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.get_entry_map", "name": "get_entry_map", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["dist", "group"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._EPDistType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_map", "ret_type": {".class": "Instance", "args": ["builtins.str", "pkg_resources.EntryPoint"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.get_entry_map", "name": "get_entry_map", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["dist", "group"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._EPDistType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_map", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "pkg_resources.EntryPoint"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["dist", "group"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._EPDistType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_entry_map", "ret_type": {".class": "Instance", "args": ["builtins.str", "pkg_resources.EntryPoint"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_importer": {".class": "SymbolTableNode", "cross_ref": "pkgutil.get_importer", "kind": "Gdef"}, "get_platform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.get_platform", "name": "get_platform", "type": null}}, "get_provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pkg_resources.get_provider", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["moduleOrReq"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pkg_resources.get_provider", "name": "get_provider", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["moduleOrReq"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pkg_resources.Requirement"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_provider", "ret_type": {".class": "UnionType", "items": ["pkg_resources.IResourceProvider", "pkg_resources.Distribution"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["moduleOrReq"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.get_provider", "name": "get_provider", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["moduleOrReq"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_provider", "ret_type": "pkg_resources.IResourceProvider", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.get_provider", "name": "get_provider", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["moduleOrReq"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.get_provider", "name": "get_provider", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["moduleOrReq"], "arg_types": ["pkg_resources.Requirement"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_provider", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.get_provider", "name": "get_provider", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["moduleOrReq"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_provider", "ret_type": "pkg_resources.IResourceProvider", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["moduleOrReq"], "arg_types": ["pkg_resources.Requirement"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_provider", "ret_type": "pkg_resources.Distribution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "get_supported_platform": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.get_supported_platform", "name": "get_supported_platform", "type": null}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef", "module_public": false}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef", "module_public": false}, "invalid_marker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.invalid_marker", "name": "invalid_marker", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalid_marker", "ret_type": {".class": "UnionType", "items": ["builtins.SyntaxError", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef", "module_public": false}, "isdir": {".class": "SymbolTableNode", "cross_ref": "genericpath.isdir", "kind": "Gdef", "module_public": false}, "issue_warning": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.issue_warning", "name": "issue_warning", "type": null}}, "iter_entry_points": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.iter_entry_points", "name": "iter_entry_points", "type": null}}, "join_continuation": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pkg_resources.join_continuation", "name": "join_continuation", "type": {".class": "AnyType", "missing_import_name": "pkg_resources.join_continuation", "source_any": null, "type_of_any": 3}}}, "load_entry_point": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["dist", "group", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.load_entry_point", "name": "load_entry_point", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["dist", "group", "name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._EPDistType"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_entry_point", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ResolvedEntryPoint"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "macosVersionString": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.macosVersionString", "name": "macosVersionString", "type": null}}, "mkdir": {".class": "SymbolTableNode", "cross_ref": "os.mkdir", "kind": "Gdef", "module_public": false}, "non_empty_lines": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.non_empty_lines", "name": "non_empty_lines", "type": null}}, "normalize_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pkg_resources.normalize_path", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pkg_resources.normalize_path", "name": "normalize_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_path", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.normalize_path", "name": "normalize_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_path", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.normalize_path", "name": "normalize_path", "type": null}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pkg_resources.normalize_path", "name": "normalize_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.BytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_path", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "pkg_resources.normalize_path", "name": "normalize_path", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_path", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filename"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.BytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_path", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "ntpath": {".class": "SymbolTableNode", "cross_ref": "ntpath", "kind": "Gdef", "module_public": false}, "null_ns_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["importer", "path_item", "packageName", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.null_ns_handler", "name": "null_ns_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["importer", "path_item", "packageName", "module"], "arg_types": ["builtins.object", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ModuleLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "null_ns_handler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "os_open": {".class": "SymbolTableNode", "cross_ref": "os.open", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "packaging": {".class": "SymbolTableNode", "cross_ref": "packaging", "kind": "Gdef", "module_public": false}, "parse_requirements": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["strs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.parse_requirements", "name": "parse_requirements", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["strs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._NestedStr"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_requirements", "ret_type": {".class": "Instance", "args": ["pkg_resources.Requirement"], "extra_attrs": null, "type_ref": "builtins.map"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pkg_resources.parse_version", "line": 144, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "packaging.version.Version"}}, "pkgutil": {".class": "SymbolTableNode", "cross_ref": "pkgu<PERSON>", "kind": "Gdef", "module_public": false}, "platform": {".class": "SymbolTableNode", "cross_ref": "platform", "kind": "Gdef", "module_public": false}, "plistlib": {".class": "SymbolTableNode", "cross_ref": "plistlib", "kind": "Gdef", "module_public": false}, "posixpath": {".class": "SymbolTableNode", "cross_ref": "posixpath", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "register_finder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["importer_type", "distribution_finder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.register_finder", "name": "register_finder", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["importer_type", "distribution_finder"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": -1, "name": "_T", "namespace": "pkg_resources.register_finder", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": -1, "name": "_T", "namespace": "pkg_resources.register_finder", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pkg_resources._DistFinderType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_finder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": -1, "name": "_T", "namespace": "pkg_resources.register_finder", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "register_loader_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["loader_type", "provider_factory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.register_loader_type", "name": "register_loader_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["loader_type", "provider_factory"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.object"}, {".class": "TypeType", "item": "types.ModuleType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._ProviderFactoryType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_loader_type", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_namespace_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["importer_type", "namespace_handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.register_namespace_handler", "name": "register_namespace_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["importer_type", "namespace_handler"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": -1, "name": "_T", "namespace": "pkg_resources.register_namespace_handler", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": -1, "name": "_T", "namespace": "pkg_resources.register_namespace_handler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pkg_resources._NSHandlerType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_namespace_handler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pkg_resources._T", "id": -1, "name": "_T", "namespace": "pkg_resources.register_namespace_handler", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "rename": {".class": "SymbolTableNode", "cross_ref": "os.rename", "kind": "Gdef", "module_public": false}, "require": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.require", "name": "require", "type": null}}, "resolve_egg_link": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.resolve_egg_link", "name": "resolve_egg_link", "type": null}}, "resource_exists": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.resource_exists", "name": "resource_exists", "type": null}}, "resource_filename": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.resource_filename", "name": "resource_filename", "type": null}}, "resource_isdir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.resource_isdir", "name": "resource_isdir", "type": null}}, "resource_listdir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.resource_listdir", "name": "resource_listdir", "type": null}}, "resource_stream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.resource_stream", "name": "resource_stream", "type": null}}, "resource_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.resource_string", "name": "resource_string", "type": null}}, "run_main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.run_main", "name": "run_main", "type": null}}, "run_script": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.run_script", "name": "run_script", "type": null}}, "safe_extra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.safe_extra", "name": "safe_extra", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["extra"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "safe_extra", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "safe_listdir": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.safe_listdir", "name": "safe_listdir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "safe_listdir", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "safe_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.safe_name", "name": "safe_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "safe_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "safe_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.safe_version", "name": "safe_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["version"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "safe_version", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_extraction_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.set_extraction_path", "name": "set_extraction_path", "type": null}}, "split": {".class": "SymbolTableNode", "cross_ref": "posixpath.split", "kind": "Gdef", "module_public": false}, "split_sections": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.split_sections", "name": "split_sections", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pkg_resources._NestedStr"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_sections", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stat": {".class": "SymbolTableNode", "cross_ref": "stat", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef", "module_public": false}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "to_filename": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pkg_resources.to_filename", "name": "to_filename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_filename", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_public": false}, "unlink": {".class": "SymbolTableNode", "cross_ref": "os.unlink", "kind": "Gdef", "module_public": false}, "utime": {".class": "SymbolTableNode", "cross_ref": "os.utime", "kind": "Gdef", "module_public": false}, "vendor_path": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.vendor_path", "name": "vendor_path", "type": null}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}, "working_set": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pkg_resources.working_set", "name": "working_set", "type": null}}, "yield_lines": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pkg_resources.yield_lines", "name": "yield_lines", "type": {".class": "AnyType", "missing_import_name": "pkg_resources.yield_lines", "source_any": null, "type_of_any": 3}}}, "zipfile": {".class": "SymbolTableNode", "cross_ref": "zipfile", "kind": "Gdef", "module_public": false}, "zipimport": {".class": "SymbolTableNode", "cross_ref": "zipimport", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pkg_resources\\__init__.py"}