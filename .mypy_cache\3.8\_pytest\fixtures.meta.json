{"data_mtime": 1753839573, "dep_lines": [42, 60, 67, 39, 40, 44, 45, 57, 61, 64, 65, 68, 71, 73, 83, 2, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 36, 38, 79, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 5, 10, 10, 10, 10, 5, 10, 10, 5, 10, 10, 5, 5, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.mark.structures", "_pytest.nodes", "_pytest._code", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.main", "_pytest.mark", "_pytest.outcomes", "_pytest.pathlib", "_pytest.scope", "_pytest.python", "__future__", "abc", "collections", "dataclasses", "functools", "inspect", "os", "pathlib", "sys", "types", "typing", "warnings", "_pytest", "exceptiongroup", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "logging", "_frozen_importlib", "_pytest._io.terminalwriter", "_pytest.warning_types", "_typeshed", "enum", "exceptiongroup._exceptions", "pluggy", "pluggy._manager", "typing_extensions"], "hash": "82631f92d8ea6460a5f41d611eeff4ad4d02070c", "id": "_pytest.fixtures", "ignore_all": true, "interface_hash": "48305e0d792c391c2eff48e0cfbf40dcb0e45655", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\fixtures.py", "plugin_data": null, "size": 73550, "suppressed": [], "version_id": "1.15.0"}