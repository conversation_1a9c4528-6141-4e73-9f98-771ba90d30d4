{".class": "MypyFile", "_fullname": "IPython.terminal.interactiveshell", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Any", "kind": "Gdef"}, "AppendAutoSuggestionInAnyLine": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine", "kind": "Gdef"}, "AutoSuggestFromHistory": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.auto_suggest.AutoSuggestFromHistory", "kind": "Gdef"}, "Bool": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Bool", "kind": "Gdef"}, "ClassicPrompts": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.prompts.ClassicPrompts", "kind": "Gdef"}, "ColorDepth": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.output.color_depth.ColorDepth", "kind": "Gdef"}, "CompleteStyle": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.shortcuts.prompt.CompleteStyle", "kind": "Gdef"}, "Condition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.Condition", "kind": "Gdef"}, "ConditionalProcessor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.ConditionalProcessor", "kind": "Gdef"}, "DEFAULT_BUFFER": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.enums.DEFAULT_BUFFER", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Dict", "kind": "Gdef"}, "DynamicStyle": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.DynamicStyle", "kind": "Gdef"}, "EditingMode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.enums.EditingMode", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Enum", "kind": "Gdef"}, "Float": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Float", "kind": "Gdef"}, "HasFocus": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.cli.HasFocus", "kind": "Gdef"}, "HighlightMatchingBracketProcessor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.HighlightMatchingBracketProcessor", "kind": "Gdef"}, "History": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.history.History", "kind": "Gdef"}, "IPythonPTCompleter": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.ptutils.IPythonPTCompleter", "kind": "Gdef"}, "IPythonPTLexer": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.ptutils.IPythonPTLexer", "kind": "Gdef"}, "Instance": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Instance", "kind": "Gdef"}, "Integer": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Integer", "kind": "Gdef"}, "InteractiveShell": {".class": "SymbolTableNode", "cross_ref": "IPython.core.interactiveshell.InteractiveShell", "kind": "Gdef"}, "InteractiveShellABC": {".class": "SymbolTableNode", "cross_ref": "IPython.core.interactiveshell.InteractiveShellABC", "kind": "Gdef"}, "IsDone": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.cli.IsDone", "kind": "Gdef"}, "KEYBINDING_FILTERS": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.filters.KEYBINDING_FILTERS", "kind": "Gdef"}, "KEY_BINDINGS": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.KEY_BINDINGS", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.List", "kind": "Gdef"}, "NavigableAutoSuggestFromHistory": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PTK3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.PTK3", "name": "PTK3", "type": "builtins.bool"}}, "Pdb": {".class": "SymbolTableNode", "cross_ref": "IPython.core.debugger.Pdb", "kind": "Gdef"}, "PromptSession": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.shortcuts.prompt.PromptSession", "kind": "Gdef"}, "Prompts": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.prompts.Prompts", "kind": "Gdef"}, "PtkHistoryAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.history.History"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.interactiveshell.PtkHistoryAdapter", "name": "PtkHistoryAdapter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.PtkHistoryAdapter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "IPython.terminal.interactiveshell", "mro": ["IPython.terminal.interactiveshell.PtkHistoryAdapter", "prompt_toolkit.history.History", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shell"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.PtkHistoryAdapter.__init__", "name": "__init__", "type": null}}, "_refresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.PtkHistoryAdapter._refresh", "name": "_refresh", "type": null}}, "append_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.PtkHistoryAdapter.append_string", "name": "append_string", "type": null}}, "load_history_strings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.PtkHistoryAdapter.load_history_strings", "name": "load_history_strings", "type": null}}, "shell": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.interactiveshell.PtkHistoryAdapter.shell", "name": "shell", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "store_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.PtkHistoryAdapter.store_string", "name": "store_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "string"], "arg_types": ["IPython.terminal.interactiveshell.PtkHistoryAdapter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "store_string of PtkHistoryAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.interactiveshell.PtkHistoryAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.interactiveshell.PtkHistoryAdapter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PygmentsTokens": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.pygments.PygmentsTokens", "kind": "Gdef"}, "RichPromptDisplayHook": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.prompts.RichPromptDisplayHook", "kind": "Gdef"}, "RuntimeBinding": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.RuntimeBinding", "kind": "Gdef"}, "Style": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.Style", "name": "Style", "type": {".class": "AnyType", "missing_import_name": "IPython.terminal.interactiveshell.Style", "source_any": null, "type_of_any": 3}}}, "TerminalInteractiveShell": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.interactiveshell.InteractiveShell"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell", "name": "TerminalInteractiveShell", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.terminal.interactiveshell", "mro": ["IPython.terminal.interactiveshell.TerminalInteractiveShell", "IPython.core.interactiveshell.InteractiveShell", "traitlets.config.configurable.SingletonConfigurable", "traitlets.config.configurable.LoggingConfigurable", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["IPython.terminal.interactiveshell.TerminalInteractiveShell", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TerminalInteractiveShell", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_autoformatter_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._autoformatter_changed", "name": "_autoformatter_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._autoformatter_changed", "name": "_autoformatter_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_autosuggestions_provider_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._autosuggestions_provider_changed", "name": "_autosuggestions_provider_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._autosuggestions_provider_changed", "name": "_autosuggestions_provider_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_displayhook_class_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._displayhook_class_default", "name": "_displayhook_class_default", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._displayhook_class_default", "name": "_displayhook_class_default", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_editing_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._editing_mode", "name": "_editing_mode", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._editing_mode", "name": "_editing_mode", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_extra_prompt_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._extra_prompt_options", "name": "_extra_prompt_options", "type": null}}, "_highlighting_style_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._highlighting_style_changed", "name": "_highlighting_style_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._highlighting_style_changed", "name": "_highlighting_style_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_inputhook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._inputhook", "name": "_inputhook", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_make_style_from_name_or_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name_or_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._make_style_from_name_or_cls", "name": "_make_style_from_name_or_cls", "type": null}}, "_merge_shortcuts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_shortcuts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._merge_shortcuts", "name": "_merge_shortcuts", "type": null}}, "_prompts_before": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._prompts_before", "name": "_prompts_before", "type": {".class": "UnionType", "items": ["IPython.terminal.prompts.Prompts", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_prompts_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._prompts_default", "name": "_prompts_default", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._prompts_default", "name": "_prompts_default", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_set_autosuggestions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._set_autosuggestions", "name": "_set_autosuggestions", "type": null}}, "_set_formatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "formatter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._set_formatter", "name": "_set_formatter", "type": null}}, "_shortcuts_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._shortcuts_changed", "name": "_shortcuts_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._shortcuts_changed", "name": "_shortcuts_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._style", "name": "_style", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_use_asyncio_inputhook": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._use_asyncio_inputhook", "name": "_use_asyncio_inputhook", "type": "builtins.bool"}}, "_validate_editing_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "proposal"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._validate_editing_mode", "name": "_validate_editing_mode", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell._validate_editing_mode", "name": "_validate_editing_mode", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "active_eventloop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.active_eventloop", "name": "active_eventloop", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ask_exit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.ask_exit", "name": "ask_exit", "type": null}}, "auto_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.auto_match", "name": "auto_match", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "auto_rewrite_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cmd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.auto_rewrite_input", "name": "auto_rewrite_input", "type": null}}, "auto_suggest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.auto_suggest", "name": "auto_suggest", "type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.AutoSuggestFromHistory", "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "autoformatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.autoformatter", "name": "autoformatter", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "autosuggestions_provider": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.autosuggestions_provider", "name": "autosuggestions_provider", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "color_depth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.color_depth", "name": "color_depth", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.color_depth", "name": "color_depth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.terminal.interactiveshell.TerminalInteractiveShell"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "color_depth of TerminalInteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "confirm_exit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.confirm_exit", "name": "confirm_exit", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "debugger_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.debugger_cls", "name": "debugger_cls", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.debugger_cls", "name": "debugger_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.terminal.interactiveshell.TerminalInteractiveShell"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debugger_cls of TerminalInteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "debugger_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.debugger_history", "name": "debugger_history", "type": {".class": "NoneType"}}}, "debugger_history_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.debugger_history_file", "name": "debugger_history_file", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "display_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.display_completions", "name": "display_completions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Enum"}}}, "editing_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.editing_mode", "name": "editing_mode", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "editor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.editor", "name": "editor", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "emacs_bindings_in_vi_insert_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.emacs_bindings_in_vi_insert_mode", "name": "emacs_bindings_in_vi_insert_mode", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "enable_gui": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "gui"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.enable_gui", "name": "enable_gui", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "gui"], "arg_types": ["IPython.terminal.interactiveshell.TerminalInteractiveShell", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_gui of TerminalInteractiveShell", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable_history_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.enable_history_search", "name": "enable_history_search", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "enable_win_unicode_console": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.enable_win_unicode_console", "name": "enable_win_unicode_console", "type": null}}, "extra_open_editor_shortcuts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.extra_open_editor_shortcuts", "name": "extra_open_editor_shortcuts", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "handle_return": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.handle_return", "name": "handle_return", "type": "traitlets.traitlets.Any"}}, "highlight_matching_brackets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.highlight_matching_brackets", "name": "highlight_matching_brackets", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "highlighting_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.highlighting_style", "name": "highlighting_style", "type": "traitlets.traitlets.Union"}}, "highlighting_style_overrides": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.highlighting_style_overrides", "name": "highlighting_style_overrides", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Dict"}}}, "init_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.init_alias", "name": "init_alias", "type": null}}, "init_display_formatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.init_display_formatter", "name": "init_display_formatter", "type": null}}, "init_io": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.init_io", "name": "init_io", "type": null}}, "init_magics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.init_magics", "name": "init_magics", "type": null}}, "init_prompt_toolkit_cli": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.init_prompt_toolkit_cli", "name": "init_prompt_toolkit_cli", "type": null}}, "init_term_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.init_term_title", "name": "init_term_title", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.init_term_title", "name": "init_term_title", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "inputhook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.inputhook", "name": "inputhook", "type": null}}, "interact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.interact", "name": "interact", "type": null}}, "keep_running": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.keep_running", "name": "keep_running", "type": "builtins.bool"}}, "mainloop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.mainloop", "name": "mainloop", "type": null}}, "mime_renderers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.mime_renderers", "name": "mime_renderers", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Dict"}}}, "modal_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.modal_cursor", "name": "modal_cursor", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "mouse_support": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.mouse_support", "name": "mouse_support", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "prompt_for_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.prompt_for_code", "name": "prompt_for_code", "type": null}}, "prompt_includes_vi_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.prompt_includes_vi_mode", "name": "prompt_includes_vi_mode", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "prompt_line_number_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.prompt_line_number_format", "name": "prompt_line_number_format", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "prompts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.prompts", "name": "prompts", "type": {".class": "Instance", "args": ["IPython.terminal.prompts.Prompts"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "prompts_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.prompts_class", "name": "prompts_class", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["shell"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["IPython.terminal.prompts.Prompts"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Prompts", "ret_type": "IPython.terminal.prompts.Prompts", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["shell"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["IPython.terminal.prompts.Prompts"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Prompts", "ret_type": "IPython.terminal.prompts.Prompts", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Type"}}}, "pt_app": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.pt_app", "name": "pt_app", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pt_complete_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.pt_complete_style", "name": "pt_complete_style", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.pt_complete_style", "name": "pt_complete_style", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.terminal.interactiveshell.TerminalInteractiveShell"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pt_complete_style of TerminalInteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reformat_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.reformat_handler", "name": "reformat_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "refresh_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.refresh_style", "name": "refresh_style", "type": null}}, "restore_term_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.restore_term_title", "name": "restore_term_title", "type": null}}, "rl_next_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.rl_next_input", "name": "rl_next_input", "type": {".class": "NoneType"}}}, "shortcuts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.shortcuts", "name": "shortcuts", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "simple_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.simple_prompt", "name": "simple_prompt", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "space_for_menu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.space_for_menu", "name": "space_for_menu", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Int"}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.style", "name": "style", "type": "prompt_toolkit.styles.base.DynamicStyle"}}, "switch_doctest_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.switch_doctest_mode", "name": "switch_doctest_mode", "type": null}}, "system": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.system", "name": "system", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cmd"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "term_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.term_title", "name": "term_title", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "term_title_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.term_title_format", "name": "term_title_format", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "timeoutlen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.timeoutlen", "name": "timeoutlen", "type": {".class": "Instance", "args": ["builtins.float", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Float"}}}, "true_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.true_color", "name": "true_color", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "ttimeoutlen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.ttimeoutlen", "name": "ttimeoutlen", "type": {".class": "Instance", "args": ["builtins.float", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Float"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.interactiveshell.TerminalInteractiveShell.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.interactiveshell.TerminalInteractiveShell", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TerminalMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.magics.TerminalMagics", "kind": "Gdef"}, "TerminalPdb": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.debugger.TerminalPdb", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.Token", "name": "Token", "type": {".class": "AnyType", "missing_import_name": "IPython.terminal.interactiveshell.Token", "source_any": null, "type_of_any": 3}}}, "Type": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Type", "kind": "Gdef"}, "Unicode": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Unicode", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Union", "kind": "Gdef"}, "UnionType": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_NoStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.interactiveshell._NoStyle", "name": "_NoStyle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "IPython.terminal.interactiveshell._NoStyle", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.terminal.interactiveshell", "mro": ["IPython.terminal.interactiveshell._NoStyle", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.interactiveshell._NoStyle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.interactiveshell._NoStyle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.interactiveshell.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.interactiveshell.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.interactiveshell.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.interactiveshell.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.interactiveshell.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.interactiveshell.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_backward_compat_continuation_prompt_tokens": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["method", "width", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell._backward_compat_continuation_prompt_tokens", "name": "_backward_compat_continuation_prompt_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["method", "width", "lineno"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_backward_compat_continuation_prompt_tokens", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_tty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell._is_tty", "name": "_is_tty", "type": "builtins.bool"}}, "_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "IPython.terminal.interactiveshell._name", "name": "_name", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "stdin"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "stdout"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "stderr"}, "type_ref": "builtins.str"}], "uses_pep604_syntax": false}}}, "_stream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell._stream", "name": "_stream", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_style_overrides_light_bg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell._style_overrides_light_bg", "name": "_style_overrides_light_bg", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "IPython.terminal.interactiveshell.Token", "source_any": {".class": "AnyType", "missing_import_name": "IPython.terminal.interactiveshell.Token", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_style_overrides_linux": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell._style_overrides_linux", "name": "_style_overrides_linux", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "IPython.terminal.interactiveshell.Token", "source_any": {".class": "AnyType", "missing_import_name": "IPython.terminal.interactiveshell.Token", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_use_simple_prompt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.interactiveshell._use_simple_prompt", "name": "_use_simple_prompt", "type": "builtins.bool"}}, "abbrev_cwd": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.process.abbrev_cwd", "kind": "Gdef"}, "add_binding": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.add_binding", "kind": "Gdef"}, "black_reformat_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text_before_cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.black_reformat_handler", "name": "black_reformat_handler", "type": null}}, "create_identifier": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.create_identifier", "kind": "Gdef"}, "create_ipython_shortcuts": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.create_ipython_shortcuts", "kind": "Gdef"}, "default": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.default", "kind": "Gdef"}, "filter_from_string": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.filters.filter_from_string", "kind": "Gdef"}, "get_asyncio_loop": {".class": "SymbolTableNode", "cross_ref": "IPython.core.async_helpers.get_asyncio_loop", "kind": "Gdef"}, "get_default_editor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.get_default_editor", "name": "get_default_editor", "type": null}}, "get_inputhook_name_and_func": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.pt_inputhooks.get_inputhook_name_and_func", "kind": "Gdef"}, "get_style_by_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.terminal.interactiveshell.get_style_by_name", "name": "get_style_by_name", "type": {".class": "AnyType", "missing_import_name": "IPython.terminal.interactiveshell.get_style_by_name", "source_any": null, "type_of_any": 3}}}, "input": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.py3compat.input", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "merge_styles": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style.merge_styles", "kind": "Gdef"}, "observe": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.observe", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "patch_stdout": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.patch_stdout.patch_stdout", "kind": "Gdef"}, "print_formatted_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.shortcuts.utils.print_formatted_text", "kind": "Gdef"}, "ptk_version": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.__version__", "kind": "Gdef"}, "restore_term_title": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.terminal.restore_term_title", "kind": "Gdef"}, "set_term_title": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.terminal.set_term_title", "kind": "Gdef"}, "style_from_pygments_cls": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.pygments.style_from_pygments_cls", "kind": "Gdef"}, "style_from_pygments_dict": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.pygments.style_from_pygments_dict", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "toggle_set_term_title": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.terminal.toggle_set_term_title", "kind": "Gdef"}, "validate": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.validate", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}, "yapf_reformat_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text_before_cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.interactiveshell.yapf_reformat_handler", "name": "yapf_reformat_handler", "type": null}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\terminal\\interactiveshell.py"}