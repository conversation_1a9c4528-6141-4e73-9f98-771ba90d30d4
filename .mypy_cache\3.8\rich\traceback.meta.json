{"data_mtime": 1753839578, "dep_lines": [29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 1, 3, 4, 5, 6, 7, 8, 9, 10, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 23, 24, 27], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["rich.pretty", "rich._loop", "rich.columns", "rich.console", "rich.constrain", "rich.highlighter", "rich.panel", "rich.scope", "rich.style", "rich.syntax", "rich.text", "rich.theme", "__future__", "linecache", "os", "platform", "sys", "dataclasses", "traceback", "types", "typing", "rich", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "logging", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "datetime", "enum", "rich.jupyter", "rich.segment", "typing_extensions"], "hash": "dfd85b7e49787ea9ded0e84d9ed522e2ecb73d26", "id": "rich.traceback", "ignore_all": true, "interface_hash": "aedec92732a222b71ba9aa8f6c67d926854de144", "mtime": 1723156628, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\rich\\traceback.py", "plugin_data": null, "size": 29529, "suppressed": ["pygments.lexers", "pygments.token", "pygments.util"], "version_id": "1.15.0"}