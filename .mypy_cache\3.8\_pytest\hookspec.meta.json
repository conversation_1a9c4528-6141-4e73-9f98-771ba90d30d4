{"data_mtime": 1753839573, "dep_lines": [24, 31, 16, 26, 27, 32, 34, 35, 37, 38, 42, 44, 45, 6, 8, 9, 14, 20, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 5, 25, 25, 5, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.deprecated", "_pytest.compat", "_pytest.config", "_pytest.fixtures", "_pytest.main", "_pytest.nodes", "_pytest.outcomes", "_pytest.python", "_pytest.reports", "_pytest.runner", "_pytest.terminal", "__future__", "pathlib", "typing", "pluggy", "pdb", "warnings", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "sys", "logging", "_frozen_importlib", "_pytest._code", "_pytest.warning_types", "abc", "bdb", "cmd", "enum", "pluggy._hooks", "pluggy._manager"], "hash": "e26bca0ab31a00e2658a90261d0ac367e02feab2", "id": "_pytest.hookspec", "ignore_all": true, "interface_hash": "c5bbf2435a29e8ccabb96239912c2f60e6e58d3e", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\hookspec.py", "plugin_data": null, "size": 42831, "suppressed": [], "version_id": "1.15.0"}