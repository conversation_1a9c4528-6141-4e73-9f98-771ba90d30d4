{"data_mtime": 1753839580, "dep_lines": [8, 9, 3, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 20, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30], "dependencies": ["IPython.utils.importstring", "IPython.utils.path", "os.path", "os", "tempfile", "warnings", "IPython", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "typing", "functools", "sys", "logging", "_frozen_importlib", "abc"], "hash": "b746d559d6467542da6c038aa6651b0874df9261", "id": "IPython.paths", "ignore_all": true, "interface_hash": "3f387a936020e1e101903ee0d2a1e0bc615e091e", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\paths.py", "plugin_data": null, "size": 4335, "suppressed": [], "version_id": "1.15.0"}