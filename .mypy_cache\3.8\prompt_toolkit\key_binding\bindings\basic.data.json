{".class": "MypyFile", "_fullname": "prompt_toolkit.key_binding.bindings.basic", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Condition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.Condition", "kind": "Gdef", "module_public": false}, "E": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "prompt_toolkit.key_binding.bindings.basic.E", "line": 23, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "prompt_toolkit.key_binding.key_processor.KeyPressEvent"}}, "KeyBindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "kind": "Gdef", "module_public": false}, "KeyPress": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_processor.KeyPress", "kind": "Gdef", "module_public": false}, "KeyPressEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_processor.KeyPressEvent", "kind": "Gdef", "module_public": false}, "Keys": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.keys.Keys", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.key_binding.bindings.basic.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.basic.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.basic.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.basic.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.basic.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.basic.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.key_binding.bindings.basic.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "emacs_insert_mode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.emacs_insert_mode", "kind": "Gdef", "module_public": false}, "get_app": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app", "kind": "Gdef", "module_public": false}, "get_by_name": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.named_commands.get_by_name", "kind": "Gdef", "module_public": false}, "has_selection": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.has_selection", "kind": "Gdef", "module_public": false}, "if_no_repeat": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.bindings.basic.if_no_repeat", "name": "if_no_repeat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "if_no_repeat", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "in_paste_mode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.in_paste_mode", "kind": "Gdef", "module_public": false}, "is_multiline": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.is_multiline", "kind": "Gdef", "module_public": false}, "load_basic_bindings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.key_binding.bindings.basic.load_basic_bindings", "name": "load_basic_bindings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_basic_bindings", "ret_type": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vi_insert_mode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.vi_insert_mode", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\key_binding\\bindings\\basic.py"}