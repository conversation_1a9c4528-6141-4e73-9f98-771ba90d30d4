{".class": "MypyFile", "_fullname": "IPython.core.magics.display", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DisplayMagics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.magic.Magics"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magics.display.DisplayMagics", "name": "DisplayMagics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magics.display.DisplayMagics", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.magics.display", "mro": ["IPython.core.magics.display.DisplayMagics", "IPython.core.magic.Magics", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "html": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.display.DisplayMagics.html", "name": "html", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.display.DisplayMagics.html", "name": "html", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "javascript": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.display.DisplayMagics.javascript", "name": "javascript", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.display.DisplayMagics.javascript", "name": "javascript", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "js": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.display.DisplayMagics.js", "name": "js", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.display.DisplayMagics.js", "name": "js", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "latex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.display.DisplayMagics.latex", "name": "latex", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.display.DisplayMagics.latex", "name": "latex", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "markdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.display.DisplayMagics.markdown", "name": "markdown", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.display.DisplayMagics.markdown", "name": "markdown", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "svg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.display.DisplayMagics.svg", "name": "svg", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.display.DisplayMagics.svg", "name": "svg", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magics.display.DisplayMagics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magics.display.DisplayMagics", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTML": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.HTML", "kind": "Gdef"}, "Javascript": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.Javascript", "kind": "Gdef"}, "Latex": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.Latex", "kind": "Gdef"}, "Magics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.Magics", "kind": "Gdef"}, "Markdown": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.Markdown", "kind": "Gdef"}, "SVG": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.SVG", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.display.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.display.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.display.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.display.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.display.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.display.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cell_magic": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.cell_magic", "kind": "Gdef"}, "display": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display_functions.display", "kind": "Gdef"}, "magic_arguments": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic_arguments", "kind": "Gdef"}, "magics_class": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.magics_class", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\magics\\display.py"}