{"data_mtime": 1753876885, "dep_lines": [7, 6, 1, 3, 4, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 30, 30, 30], "dependencies": ["urllib3.util.util", "urllib3.exceptions", "__future__", "re", "typing", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "51d396146d087d3f2d1ec80b00837381c16c6352", "id": "urllib3.util.url", "ignore_all": true, "interface_hash": "9c064a3a09d895a88ebfdc0b7e1e56581961d003", "mtime": 1750470649, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\urllib3\\util\\url.py", "plugin_data": null, "size": 15205, "suppressed": [], "version_id": "1.15.0"}