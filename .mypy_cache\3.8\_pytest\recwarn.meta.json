{"data_mtime": 1753839573, "dep_lines": [25, 26, 27, 4, 6, 7, 8, 9, 21, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 25, 10, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30], "dependencies": ["_pytest.deprecated", "_pytest.fixtures", "_pytest.outcomes", "__future__", "pprint", "re", "types", "typing", "typing_extensions", "warnings", "builtins", "json", "traitlets.utils.warnings", "functools", "os", "sys", "logging", "_frozen_importlib", "_pytest.config", "abc"], "hash": "43acb55ae21d6abf099f157dc6830158c8528c4f", "id": "_pytest.recwarn", "ignore_all": true, "interface_hash": "746d232b3026d75ad3fd8560cea1edf4d7b8ae3a", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\recwarn.py", "plugin_data": null, "size": 13227, "suppressed": [], "version_id": "1.15.0"}