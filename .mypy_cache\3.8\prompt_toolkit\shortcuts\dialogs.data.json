{".class": "MypyFile", "_fullname": "prompt_toolkit.shortcuts.dialogs", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AnyContainer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.AnyContainer", "kind": "Gdef", "module_public": false}, "AnyFormattedText": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText", "kind": "Gdef", "module_public": false}, "Application": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.application.Application", "kind": "Gdef", "module_public": false}, "BaseStyle": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.BaseStyle", "kind": "Gdef", "module_public": false}, "Box": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.base.Box", "kind": "Gdef", "module_public": false}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.buffer.Buffer", "kind": "Gdef", "module_public": false}, "Button": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.base.Button", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CheckboxList": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.base.CheckboxList", "kind": "Gdef", "module_public": false}, "Completer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.Completer", "kind": "Gdef", "module_public": false}, "D": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.Dimension", "kind": "Gdef", "module_public": false}, "Dialog": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.dialogs.Dialog", "kind": "Gdef", "module_public": false}, "FilterOrBool": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.FilterOrBool", "kind": "Gdef", "module_public": false}, "HSplit": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.HSplit", "kind": "Gdef", "module_public": false}, "KeyBindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "kind": "Gdef", "module_public": false}, "Label": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.base.Label", "kind": "Gdef", "module_public": false}, "Layout": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.layout.Layout", "kind": "Gdef", "module_public": false}, "ProgressBar": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.base.ProgressBar", "kind": "Gdef", "module_public": false}, "RadioList": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.base.RadioList", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TextArea": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.base.TextArea", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "ValidationToolbar": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.toolbars.ValidationToolbar", "kind": "Gdef", "module_public": false}, "Validator": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.validation.Validator", "kind": "Gdef", "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.shortcuts.dialogs.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.dialogs.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.dialogs.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.dialogs.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.dialogs.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.dialogs.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.shortcuts.dialogs.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_create_app": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["dialog", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.dialogs._create_app", "name": "_create_app", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["dialog", "style"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}, {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_app", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_return_none": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.dialogs._return_none", "name": "_return_none", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_return_none", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "button_dialog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1], "arg_names": ["title", "text", "buttons", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.dialogs.button_dialog", "name": "button_dialog", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["title", "text", "buttons", "style"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "id": -1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.dialogs.button_dialog", "upper_bound": "builtins.object", "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "button_dialog", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "id": -1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.dialogs.button_dialog", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "id": -1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.dialogs.button_dialog", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "checkboxlist_dialog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1], "arg_names": ["title", "text", "ok_text", "cancel_text", "values", "default_values", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.dialogs.checkboxlist_dialog", "name": "checkboxlist_dialog", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1], "arg_names": ["title", "text", "ok_text", "cancel_text", "values", "default_values", "style"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "id": -1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.dialogs.checkboxlist_dialog", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "id": -1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.dialogs.checkboxlist_dialog", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "checkboxlist_dialog", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "id": -1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.dialogs.checkboxlist_dialog", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "id": -1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.dialogs.checkboxlist_dialog", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "focus_next": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.focus.focus_next", "kind": "Gdef", "module_public": false}, "focus_previous": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.focus.focus_previous", "kind": "Gdef", "module_public": false}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "get_app": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app", "kind": "Gdef", "module_public": false}, "get_running_loop": {".class": "SymbolTableNode", "cross_ref": "_asyncio.get_running_loop", "kind": "Gdef", "module_public": false}, "input_dialog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["title", "text", "ok_text", "cancel_text", "completer", "validator", "password", "style", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.dialogs.input_dialog", "name": "input_dialog", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["title", "text", "ok_text", "cancel_text", "completer", "validator", "password", "style", "default"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["prompt_toolkit.completion.base.Completer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.validation.Validator", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "input_dialog", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_key_bindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.defaults.load_key_bindings", "kind": "Gdef", "module_public": false}, "merge_key_bindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.merge_key_bindings", "kind": "Gdef", "module_public": false}, "message_dialog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1], "arg_names": ["title", "text", "ok_text", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.dialogs.message_dialog", "name": "message_dialog", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["title", "text", "ok_text", "style"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "builtins.str", {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "message_dialog", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "progress_dialog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1], "arg_names": ["title", "text", "run_callback", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.dialogs.progress_dialog", "name": "progress_dialog", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["title", "text", "run_callback", "style"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "progress_dialog", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "radiolist_dialog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1], "arg_names": ["title", "text", "ok_text", "cancel_text", "values", "default", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.dialogs.radiolist_dialog", "name": "radiolist_dialog", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1], "arg_names": ["title", "text", "ok_text", "cancel_text", "values", "default", "style"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "id": -1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.dialogs.radiolist_dialog", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "id": -1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.dialogs.radiolist_dialog", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "radiolist_dialog", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "id": -1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.dialogs.radiolist_dialog", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.shortcuts.dialogs._T", "id": -1, "name": "_T", "namespace": "prompt_toolkit.shortcuts.dialogs.radiolist_dialog", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "run_in_executor_with_context": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.eventloop.utils.run_in_executor_with_context", "kind": "Gdef", "module_public": false}, "yes_no_dialog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1], "arg_names": ["title", "text", "yes_text", "no_text", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.shortcuts.dialogs.yes_no_dialog", "name": "yes_no_dialog", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1], "arg_names": ["title", "text", "yes_text", "no_text", "style"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["prompt_toolkit.styles.base.BaseStyle", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "yes_no_dialog", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "prompt_toolkit.application.application.Application"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\shortcuts\\dialogs.py"}