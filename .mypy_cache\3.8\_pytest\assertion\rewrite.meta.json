{"data_mtime": 1753839573, "dep_lines": [29, 33, 9, 10, 11, 32, 33, 34, 35, 36, 38, 279, 3, 5, 6, 7, 8, 9, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 875, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 303, 301], "dep_prios": [5, 5, 10, 10, 10, 5, 20, 5, 5, 5, 5, 20, 5, 10, 5, 10, 10, 20, 10, 10, 10, 10, 5, 10, 10, 10, 10, 5, 20, 5, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["_pytest._io.saferepr", "_pytest.assertion.util", "importlib.abc", "importlib.machinery", "importlib.util", "_pytest._version", "_pytest.assertion", "_pytest.config", "_pytest.main", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "__future__", "ast", "collections", "errno", "functools", "importlib", "io", "itertools", "marshal", "os", "pathlib", "struct", "sys", "tokenize", "types", "typing", "warnings", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "logging", "_collections_abc", "_frozen_importlib", "_frozen_importlib_external", "_pytest.nodes", "_typeshed", "abc", "pluggy", "pluggy._tracing", "typing_extensions"], "hash": "576ccebcfe68f9237a4a422a69f0e75de39232c8", "id": "_pytest.assertion.rewrite", "ignore_all": true, "interface_hash": "6be53b18d10227d6705ee335f1b83b6b64072a86", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\assertion\\rewrite.py", "plugin_data": null, "size": 48404, "suppressed": ["importlib.resources.readers", "importlib.readers"], "version_id": "1.15.0"}