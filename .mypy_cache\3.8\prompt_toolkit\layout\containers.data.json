{".class": "MypyFile", "_fullname": "prompt_toolkit.layout.containers", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_public": false}, "AnyContainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "prompt_toolkit.layout.containers.AnyContainer", "line": 163, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["prompt_toolkit.layout.containers.Container", "prompt_toolkit.layout.containers.MagicContainer"], "uses_pep604_syntax": false}}}, "AnyDimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.AnyDimension", "kind": "Gdef", "module_public": false}, "AnyFormattedText": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ColorColumn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.ColorColumn", "name": "ColorColumn", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ColorColumn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.ColorColumn", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "position", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ColorColumn.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "position", "style"], "arg_types": ["prompt_toolkit.layout.containers.ColorColumn", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColorColumn", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "position": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.ColorColumn.position", "name": "position", "type": "builtins.int"}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.ColorColumn.style", "name": "style", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.ColorColumn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.ColorColumn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConditionalContainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.containers.Container"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.ConditionalContainer", "name": "ConditionalContainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ConditionalContainer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.ConditionalContainer", "prompt_toolkit.layout.containers.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ConditionalContainer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "filter"], "arg_types": ["prompt_toolkit.layout.containers.ConditionalContainer", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConditionalContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ConditionalContainer.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.containers.ConditionalContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ConditionalContainer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.ConditionalContainer.content", "name": "content", "type": "prompt_toolkit.layout.containers.Container"}}, "filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.ConditionalContainer.filter", "name": "filter", "type": "prompt_toolkit.filters.base.Filter"}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ConditionalContainer.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.ConditionalContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of ConditionalC<PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ConditionalContainer.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "arg_types": ["prompt_toolkit.layout.containers.ConditionalContainer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of ConditionalContainer", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ConditionalContainer.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.containers.ConditionalContainer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of ConditionalContainer", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ConditionalContainer.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.ConditionalContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of ConditionalC<PERSON>r", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_to_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ConditionalContainer.write_to_screen", "name": "write_to_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "arg_types": ["prompt_toolkit.layout.containers.ConditionalContainer", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_to_screen of ConditionalContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.ConditionalContainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.ConditionalContainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Container": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_children", 1], ["preferred_height", 1], ["preferred_width", 1], ["reset", 1], ["write_to_screen", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.Container", "name": "Container", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "prompt_toolkit.layout.containers.Container", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.Container", "builtins.object"], "names": {".class": "SymbolTable", "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "prompt_toolkit.layout.containers.Container.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Container"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of Container", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.Container.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Container"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of Container", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_key_bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Container.get_key_bindings", "name": "get_key_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Container"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_key_bindings of Container", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_modal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Container.is_modal", "name": "is_modal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Container"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_modal of Container", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "prompt_toolkit.layout.containers.Container.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "arg_types": ["prompt_toolkit.layout.containers.Container", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of Container", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.Container.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "arg_types": ["prompt_toolkit.layout.containers.Container", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of Container", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "preferred_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "prompt_toolkit.layout.containers.Container.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.containers.Container", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of Container", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.Container.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.containers.Container", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of Container", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "prompt_toolkit.layout.containers.Container.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Container"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of Container", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.Container.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Container"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of Container", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "write_to_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "prompt_toolkit.layout.containers.Container.write_to_screen", "name": "write_to_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "arg_types": ["prompt_toolkit.layout.containers.Container", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_to_screen of Container", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.Container.write_to_screen", "name": "write_to_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "arg_types": ["prompt_toolkit.layout.containers.Container", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_to_screen of Container", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.Container.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.Container", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.Dimension", "kind": "Gdef", "module_public": false}, "DummyControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.DummyControl", "kind": "Gdef", "module_public": false}, "DynamicContainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.containers.Container"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.DynamicContainer", "name": "DynamicContainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.DynamicContainer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.DynamicContainer", "prompt_toolkit.layout.containers.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "get_container"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.DynamicContainer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "get_container"], "arg_types": ["prompt_toolkit.layout.containers.DynamicContainer", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DynamicContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_container": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.DynamicContainer._get_container", "name": "_get_container", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.DynamicContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_container of DynamicContainer", "ret_type": "prompt_toolkit.layout.containers.Container", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.DynamicContainer.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.DynamicContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of DynamicContainer", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_container": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.DynamicContainer.get_container", "name": "get_container", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_key_bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.DynamicContainer.get_key_bindings", "name": "get_key_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.DynamicContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_key_bindings of DynamicContainer", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_modal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.DynamicContainer.is_modal", "name": "is_modal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.DynamicContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_modal of DynamicContainer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.DynamicContainer.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "arg_types": ["prompt_toolkit.layout.containers.DynamicContainer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of DynamicContainer", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.DynamicContainer.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.containers.DynamicContainer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of DynamicContainer", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.DynamicContainer.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.DynamicContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of DynamicContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_to_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.DynamicContainer.write_to_screen", "name": "write_to_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "arg_types": ["prompt_toolkit.layout.containers.DynamicContainer", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_to_screen of DynamicContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.DynamicContainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.DynamicContainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_public": false}, "FilterOrBool": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.FilterOrBool", "kind": "Gdef", "module_public": false}, "Float": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.Float", "name": "Float", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Float", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.Float", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "content", "top", "right", "bottom", "left", "width", "height", "xcursor", "ycursor", "attach_to_window", "hide_when_covering_content", "allow_cover_cursor", "z_index", "transparent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Float.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "content", "top", "right", "bottom", "left", "width", "height", "xcursor", "ycursor", "attach_to_window", "hide_when_covering_content", "allow_cover_cursor", "z_index", "transparent"], "arg_types": ["prompt_toolkit.layout.containers.Float", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Float", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Float.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.containers.Float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Float", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allow_cover_cursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.allow_cover_cursor", "name": "allow_cover_cursor", "type": "builtins.bool"}}, "attach_to_window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.attach_to_window", "name": "attach_to_window", "type": {".class": "UnionType", "items": ["prompt_toolkit.layout.containers.Window", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "bottom": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.bottom", "name": "bottom", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.content", "name": "content", "type": "prompt_toolkit.layout.containers.Container"}}, "get_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Float.get_height", "name": "get_height", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_height of Float", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Float.get_width", "name": "get_width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_width of Float", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.height", "name": "height", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "hide_when_covering_content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.hide_when_covering_content", "name": "hide_when_covering_content", "type": "builtins.bool"}}, "left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.left", "name": "left", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.right", "name": "right", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "top": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.top", "name": "top", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "transparent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.transparent", "name": "transparent", "type": "prompt_toolkit.filters.base.Filter"}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.width", "name": "width", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "xcursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.xcursor", "name": "xcursor", "type": "builtins.bool"}}, "ycursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.ycursor", "name": "ycursor", "type": "builtins.bool"}}, "z_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Float.z_index", "name": "z_index", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.Float.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.Float", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FloatContainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.containers.Container"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.FloatContainer", "name": "FloatContainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.FloatContainer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.FloatContainer", "prompt_toolkit.layout.containers.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "content", "floats", "modal", "key_bindings", "style", "z_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.FloatContainer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "content", "floats", "modal", "key_bindings", "style", "z_index"], "arg_types": ["prompt_toolkit.layout.containers.FloatContainer", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}, {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FloatContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_area_is_empty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "screen", "write_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.FloatContainer._area_is_empty", "name": "_area_is_empty", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "screen", "write_position"], "arg_types": ["prompt_toolkit.layout.containers.FloatContainer", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.screen.WritePosition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_area_is_empty of FloatContainer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_draw_float": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "fl", "screen", "mouse_handlers", "write_position", "style", "erase_bg", "z_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.FloatContainer._draw_float", "name": "_draw_float", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "fl", "screen", "mouse_handlers", "write_position", "style", "erase_bg", "z_index"], "arg_types": ["prompt_toolkit.layout.containers.FloatContainer", "prompt_toolkit.layout.containers.Float", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_draw_float of FloatContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.FloatContainer.content", "name": "content", "type": "prompt_toolkit.layout.containers.Container"}}, "floats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.FloatContainer.floats", "name": "floats", "type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Float"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.FloatContainer.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.FloatContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of FloatContainer", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_key_bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.FloatContainer.get_key_bindings", "name": "get_key_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.FloatContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_key_bindings of FloatContainer", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_modal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.FloatContainer.is_modal", "name": "is_modal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.FloatContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_modal of FloatContainer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_bindings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.FloatContainer.key_bindings", "name": "key_bindings", "type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "modal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.FloatContainer.modal", "name": "modal", "type": "builtins.bool"}}, "preferred_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.FloatContainer.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "arg_types": ["prompt_toolkit.layout.containers.FloatContainer", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of FloatContainer", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.FloatContainer.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.containers.FloatContainer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of FloatContainer", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.FloatContainer.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.FloatContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of FloatContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.FloatContainer.style", "name": "style", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "write_to_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.FloatContainer.write_to_screen", "name": "write_to_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "arg_types": ["prompt_toolkit.layout.containers.FloatContainer", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_to_screen of FloatContainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "z_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.FloatContainer.z_index", "name": "z_index", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.FloatContainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.FloatContainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FormattedTextControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.FormattedTextControl", "kind": "Gdef", "module_public": false}, "GetLinePrefixCallable": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.GetLinePrefixCallable", "kind": "Gdef", "module_public": false}, "HSplit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.containers._Split"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.HSplit", "name": "HSplit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.HSplit", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.HSplit", "prompt_toolkit.layout.containers._Split", "prompt_toolkit.layout.containers.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "children", "window_too_small", "align", "padding", "padding_char", "padding_style", "width", "height", "z_index", "modal", "key_bindings", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.HSplit.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "children", "window_too_small", "align", "padding", "padding_char", "padding_style", "width", "height", "z_index", "modal", "key_bindings", "style"], "arg_types": ["prompt_toolkit.layout.containers.HSplit", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["prompt_toolkit.layout.containers.Container", {".class": "NoneType"}], "uses_pep604_syntax": true}, "prompt_toolkit.layout.containers.VerticalAlign", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HSplit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_all_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.HSplit._all_children", "name": "_all_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.HSplit"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_all_children of HSplit", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.HSplit._all_children", "name": "_all_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.HSplit"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_all_children of HSplit", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_children_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.HSplit._children_cache", "name": "_children_cache", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "prompt_toolkit.cache.SimpleCache"}}}, "_divide_heights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "write_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.HSplit._divide_heights", "name": "_divide_heights", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "write_position"], "arg_types": ["prompt_toolkit.layout.containers.HSplit", "prompt_toolkit.layout.screen.WritePosition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_divide_heights of HSplit", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remaining_space_window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.HSplit._remaining_space_window", "name": "_remaining_space_window", "type": "prompt_toolkit.layout.containers.Window"}}, "align": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.HSplit.align", "name": "align", "type": "prompt_toolkit.layout.containers.VerticalAlign"}}, "preferred_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.HSplit.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "arg_types": ["prompt_toolkit.layout.containers.HSplit", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of HSplit", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.HSplit.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.containers.HSplit", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of HSplit", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.HSplit.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.HSplit"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of HSplit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_to_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.HSplit.write_to_screen", "name": "write_to_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "arg_types": ["prompt_toolkit.layout.containers.HSplit", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_to_screen of HSplit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.HSplit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.HSplit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HorizontalAlign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.HorizontalAlign", "name": "HorizontalAlign", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "prompt_toolkit.layout.containers.HorizontalAlign", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.HorizontalAlign", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "CENTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.HorizontalAlign.CENTER", "name": "CENTER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "CENTER"}, "type_ref": "builtins.str"}}}, "JUSTIFY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.HorizontalAlign.JUSTIFY", "name": "JUSTIFY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "JUSTIFY"}, "type_ref": "builtins.str"}}}, "LEFT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.HorizontalAlign.LEFT", "name": "LEFT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "LEFT"}, "type_ref": "builtins.str"}}}, "RIGHT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.HorizontalAlign.RIGHT", "name": "RIGHT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "RIGHT"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.HorizontalAlign.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.HorizontalAlign", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KeyBindingsBase": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", "kind": "Gdef", "module_public": false}, "MagicContainer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__pt_container__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.MagicContainer", "name": "MagicContainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "prompt_toolkit.layout.containers.MagicContainer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.MagicContainer", "builtins.object"], "names": {".class": "SymbolTable", "__pt_container__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_mypy_only"], "fullname": "prompt_toolkit.layout.containers.MagicContainer.__pt_container__", "name": "__pt_container__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.MagicContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pt_container__ of MagicContainer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.MagicContainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.MagicContainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Margin": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.margins.Margin", "kind": "Gdef", "module_public": false}, "MouseEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.mouse_events.MouseEvent", "kind": "Gdef", "module_public": false}, "MouseEventType": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.mouse_events.MouseEventType", "kind": "Gdef", "module_public": false}, "MouseHandlers": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "kind": "Gdef", "module_public": false}, "NotImplementedOrNone": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.NotImplementedOrNone", "kind": "Gdef", "module_public": false}, "Point": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.data_structures.Point", "kind": "Gdef", "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef", "module_public": false}, "Screen": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.screen.Screen", "kind": "Gdef", "module_public": false}, "ScrollOffsets": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.ScrollOffsets", "name": "ScrollOffsets", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.ScrollOffsets", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "top", "bottom", "left", "right"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "top", "bottom", "left", "right"], "arg_types": ["prompt_toolkit.layout.containers.ScrollOffsets", {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScrollOffsets", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.containers.ScrollOffsets"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ScrollOffsets", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bottom": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets._bottom", "name": "_bottom", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "_left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets._left", "name": "_left", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "_right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets._right", "name": "_right", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "_top": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets._top", "name": "_top", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "bottom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets.bottom", "name": "bottom", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.ScrollOffsets"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bottom of ScrollOffsets", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets.bottom", "name": "bottom", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.ScrollOffsets"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bottom of ScrollOffsets", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "left": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets.left", "name": "left", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.ScrollOffsets"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "left of ScrollOffsets", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets.left", "name": "left", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.ScrollOffsets"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "left of ScrollOffsets", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "right": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets.right", "name": "right", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.ScrollOffsets"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "right of ScrollOffsets", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets.right", "name": "right", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.ScrollOffsets"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "right of ScrollOffsets", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "top": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets.top", "name": "top", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.ScrollOffsets"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "top of ScrollOffsets", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.ScrollOffsets.top", "name": "top", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.ScrollOffsets"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "top of ScrollOffsets", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.ScrollOffsets.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.ScrollOffsets", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "SimpleCache": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.cache.SimpleCache", "kind": "Gdef", "module_public": false}, "StyleAndTextTuples": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeGuard", "kind": "Gdef", "module_public": false}, "UIContent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.UIContent", "kind": "Gdef", "module_public": false}, "UIControl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.controls.UIControl", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "VSplit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.containers._Split"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.VSplit", "name": "VSplit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.VSplit", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.VSplit", "prompt_toolkit.layout.containers._Split", "prompt_toolkit.layout.containers.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "children", "window_too_small", "align", "padding", "padding_char", "padding_style", "width", "height", "z_index", "modal", "key_bindings", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.VSplit.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "children", "window_too_small", "align", "padding", "padding_char", "padding_style", "width", "height", "z_index", "modal", "key_bindings", "style"], "arg_types": ["prompt_toolkit.layout.containers.VSplit", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["prompt_toolkit.layout.containers.Container", {".class": "NoneType"}], "uses_pep604_syntax": true}, "prompt_toolkit.layout.containers.HorizontalAlign", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of VSplit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_all_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.VSplit._all_children", "name": "_all_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.VSplit"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_all_children of VSplit", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.VSplit._all_children", "name": "_all_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.VSplit"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_all_children of VSplit", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_children_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.VSplit._children_cache", "name": "_children_cache", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "prompt_toolkit.cache.SimpleCache"}}}, "_divide_widths": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.VSplit._divide_widths", "name": "_divide_widths", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["prompt_toolkit.layout.containers.VSplit", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_divide_widths of VSplit", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remaining_space_window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.VSplit._remaining_space_window", "name": "_remaining_space_window", "type": "prompt_toolkit.layout.containers.Window"}}, "align": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.VSplit.align", "name": "align", "type": "prompt_toolkit.layout.containers.HorizontalAlign"}}, "preferred_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.VSplit.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "arg_types": ["prompt_toolkit.layout.containers.VSplit", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of VSplit", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.VSplit.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.containers.VSplit", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of VSplit", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.VSplit.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.VSplit"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of VSplit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_to_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.VSplit.write_to_screen", "name": "write_to_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "arg_types": ["prompt_toolkit.layout.containers.VSplit", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_to_screen of VSplit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.VSplit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.VSplit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VerticalAlign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.VerticalAlign", "name": "VerticalAlign", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "prompt_toolkit.layout.containers.VerticalAlign", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.VerticalAlign", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BOTTOM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.VerticalAlign.BOTTOM", "name": "BOTTOM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "BOTTOM"}, "type_ref": "builtins.str"}}}, "CENTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.VerticalAlign.CENTER", "name": "CENTER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "CENTER"}, "type_ref": "builtins.str"}}}, "JUSTIFY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.VerticalAlign.JUSTIFY", "name": "JUSTIFY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "JUSTIFY"}, "type_ref": "builtins.str"}}}, "TOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.VerticalAlign.TOP", "name": "TOP", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "TOP"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.VerticalAlign.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.VerticalAlign", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Window": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.containers.Container"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.Window", "name": "Window", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.containers.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "content", "width", "height", "z_index", "dont_extend_width", "dont_extend_height", "ignore_content_width", "ignore_content_height", "left_margins", "right_margins", "scroll_offsets", "allow_scroll_beyond_bottom", "wrap_lines", "get_vertical_scroll", "get_horizontal_scroll", "always_hide_cursor", "cursorline", "cursorcolumn", "colorcolumns", "align", "style", "char", "get_line_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "content", "width", "height", "z_index", "dont_extend_width", "dont_extend_height", "ignore_content_width", "ignore_content_height", "left_margins", "right_margins", "scroll_offsets", "allow_scroll_beyond_bottom", "wrap_lines", "get_vertical_scroll", "get_horizontal_scroll", "always_hide_cursor", "cursorline", "cursorcolumn", "colorcolumns", "align", "style", "char", "get_line_prefix"], "arg_types": ["prompt_toolkit.layout.containers.Window", {".class": "UnionType", "items": ["prompt_toolkit.layout.controls.UIControl", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.layout.margins.Margin"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.layout.margins.Margin"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.layout.containers.ScrollOffsets", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.filters.base.FilterOrBool"}, {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "Instance", "args": ["prompt_toolkit.layout.containers.ColorColumn"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.ColorColumn"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["prompt_toolkit.layout.containers.WindowAlign", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "prompt_toolkit.layout.containers.WindowAlign", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.controls.GetLinePrefixCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Window", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "new_screen", "write_position", "parent_style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._apply_style", "name": "_apply_style", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "new_screen", "write_position", "parent_style"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.screen.WritePosition", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_style of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "ui_content", "new_screen", "write_position", "move_x", "width", "vertical_scroll", "horizontal_scroll", "wrap_lines", "highlight_lines", "vertical_scroll_2", "always_hide_cursor", "has_focus", "align", "get_line_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._copy_body", "name": "_copy_body", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "ui_content", "new_screen", "write_position", "move_x", "width", "vertical_scroll", "horizontal_scroll", "wrap_lines", "highlight_lines", "vertical_scroll_2", "always_hide_cursor", "has_focus", "align", "get_line_prefix"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.controls.UIContent", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.screen.WritePosition", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool", "prompt_toolkit.layout.containers.WindowAlign", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_body of Window", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy_margin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "margin_content", "new_screen", "write_position", "move_x", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._copy_margin", "name": "_copy_margin", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "margin_content", "new_screen", "write_position", "move_x", "width"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.controls.UIContent", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.screen.WritePosition", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_margin of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fill_bg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "screen", "write_position", "erase_bg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._fill_bg", "name": "_fill_bg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "screen", "write_position", "erase_bg"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.screen.WritePosition", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fill_bg of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_digraph_char": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._get_digraph_char", "name": "_get_digraph_char", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_digraph_char of Window", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_margin_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "margin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._get_margin_width", "name": "_get_margin_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "margin"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.margins.Margin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_margin_width of Window", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_total_margin_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._get_total_margin_width", "name": "_get_total_margin_width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_total_margin_width of Window", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_ui_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._get_ui_content", "name": "_get_ui_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "height"], "arg_types": ["prompt_toolkit.layout.containers.Window", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_ui_content of Window", "ret_type": "prompt_toolkit.layout.controls.UIContent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_highlight_cursorlines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "new_screen", "cpos", "x", "y", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._highlight_cursorlines", "name": "_highlight_cursorlines", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "new_screen", "cpos", "x", "y", "width", "height"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.screen.Screen", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_highlight_cursorlines of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_highlight_digraph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_screen"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._highlight_digraph", "name": "_highlight_digraph", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_screen"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.screen.Screen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_highlight_digraph of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_margin_width_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window._margin_width_cache", "name": "_margin_width_cache", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["prompt_toolkit.layout.margins.Margin", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.int"], "extra_attrs": null, "type_ref": "prompt_toolkit.cache.SimpleCache"}}}, "_merge_dimensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["dimension", "get_preferred", "dont_extend"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.Window._merge_dimensions", "name": "_merge_dimensions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["dimension", "get_preferred", "dont_extend"], "arg_types": [{".class": "UnionType", "items": ["prompt_toolkit.layout.dimension.Dimension", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_merge_dimensions of Window", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window._merge_dimensions", "name": "_merge_dimensions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["dimension", "get_preferred", "dont_extend"], "arg_types": [{".class": "UnionType", "items": ["prompt_toolkit.layout.dimension.Dimension", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_merge_dimensions of Window", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_mouse_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mouse_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._mouse_handler", "name": "_mouse_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mouse_event"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.mouse_events.MouseEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mouse_handler of Window", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scroll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ui_content", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._scroll", "name": "_scroll", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ui_content", "width", "height"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.controls.UIContent", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scroll of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scroll_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._scroll_down", "name": "_scroll_down", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scroll_down of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scroll_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._scroll_up", "name": "_scroll_up", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scroll_up of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scroll_when_linewrapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ui_content", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._scroll_when_linewrapping", "name": "_scroll_when_linewrapping", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ui_content", "width", "height"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.controls.UIContent", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scroll_when_linewrapping of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scroll_without_linewrapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ui_content", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._scroll_without_linewrapping", "name": "_scroll_without_linewrapping", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ui_content", "width", "height"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.controls.UIContent", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scroll_without_linewrapping of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_show_key_processor_key_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_screen"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._show_key_processor_key_buffer", "name": "_show_key_processor_key_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_screen"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.screen.Screen"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_show_key_processor_key_buffer of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ui_content_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window._ui_content_cache", "name": "_ui_content_cache", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "prompt_toolkit.layout.controls.UIContent"], "extra_attrs": null, "type_ref": "prompt_toolkit.cache.SimpleCache"}}}, "_write_to_screen_at_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window._write_to_screen_at_index", "name": "_write_to_screen_at_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_write_to_screen_at_index of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "align": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.align", "name": "align", "type": {".class": "UnionType", "items": ["prompt_toolkit.layout.containers.WindowAlign", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "prompt_toolkit.layout.containers.WindowAlign", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "allow_scroll_beyond_bottom": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.allow_scroll_beyond_bottom", "name": "allow_scroll_beyond_bottom", "type": "prompt_toolkit.filters.base.Filter"}}, "always_hide_cursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.always_hide_cursor", "name": "always_hide_cursor", "type": "prompt_toolkit.filters.base.Filter"}}, "char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.char", "name": "char", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "colorcolumns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.colorcolumns", "name": "colorcolumns", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.layout.containers.ColorColumn"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.ColorColumn"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.content", "name": "content", "type": "prompt_toolkit.layout.controls.UIControl"}}, "cursorcolumn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.cursorcolumn", "name": "cursorcolumn", "type": "prompt_toolkit.filters.base.Filter"}}, "cursorline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.cursorline", "name": "cursorline", "type": "prompt_toolkit.filters.base.Filter"}}, "dont_extend_height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.dont_extend_height", "name": "dont_extend_height", "type": "prompt_toolkit.filters.base.Filter"}}, "dont_extend_width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.dont_extend_width", "name": "dont_extend_width", "type": "prompt_toolkit.filters.base.Filter"}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of Window", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_horizontal_scroll": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.get_horizontal_scroll", "name": "get_horizontal_scroll", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_key_bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window.get_key_bindings", "name": "get_key_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_key_bindings of Window", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_line_prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.get_line_prefix", "name": "get_line_prefix", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.controls.GetLinePrefixCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_vertical_scroll": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.get_vertical_scroll", "name": "get_vertical_scroll", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.height", "name": "height", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}}}, "horizontal_scroll": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.horizontal_scroll", "name": "horizontal_scroll", "type": "builtins.int"}}, "ignore_content_height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.ignore_content_height", "name": "ignore_content_height", "type": "prompt_toolkit.filters.base.Filter"}}, "ignore_content_width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.ignore_content_width", "name": "ignore_content_width", "type": "prompt_toolkit.filters.base.Filter"}}, "left_margins": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.left_margins", "name": "left_margins", "type": {".class": "Instance", "args": ["prompt_toolkit.layout.margins.Margin"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "preferred_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window.preferred_height", "name": "preferred_height", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "width", "max_available_height"], "arg_types": ["prompt_toolkit.layout.containers.Window", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_height of Window", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preferred_width": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window.preferred_width", "name": "preferred_width", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_available_width"], "arg_types": ["prompt_toolkit.layout.containers.Window", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preferred_width of Window", "ret_type": "prompt_toolkit.layout.dimension.Dimension", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.render_info", "name": "render_info", "type": {".class": "UnionType", "items": ["prompt_toolkit.layout.containers.WindowRenderInfo", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.Window"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "right_margins": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.right_margins", "name": "right_margins", "type": {".class": "Instance", "args": ["prompt_toolkit.layout.margins.Margin"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "scroll_offsets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.scroll_offsets", "name": "scroll_offsets", "type": "prompt_toolkit.layout.containers.ScrollOffsets"}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.style", "name": "style", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "vertical_scroll": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.vertical_scroll", "name": "vertical_scroll", "type": "builtins.int"}}, "vertical_scroll_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.vertical_scroll_2", "name": "vertical_scroll_2", "type": "builtins.int"}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.width", "name": "width", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}}}, "wrap_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.wrap_lines", "name": "wrap_lines", "type": "prompt_toolkit.filters.base.Filter"}}, "write_to_screen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.Window.write_to_screen", "name": "write_to_screen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "screen", "mouse_handlers", "write_position", "parent_style", "erase_bg", "z_index"], "arg_types": ["prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.screen.Screen", "prompt_toolkit.layout.mouse_handlers.MouseHandlers", "prompt_toolkit.layout.screen.WritePosition", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_to_screen of Window", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "z_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.Window.z_index", "name": "z_index", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.Window.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.Window", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WindowAlign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.WindowAlign", "name": "WindowAlign", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "prompt_toolkit.layout.containers.WindowAlign", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.WindowAlign", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "CENTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.WindowAlign.CENTER", "name": "CENTER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "CENTER"}, "type_ref": "builtins.str"}}}, "LEFT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.WindowAlign.LEFT", "name": "LEFT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "LEFT"}, "type_ref": "builtins.str"}}}, "RIGHT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.WindowAlign.RIGHT", "name": "RIGHT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "RIGHT"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.WindowAlign.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.WindowAlign", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WindowRenderInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo", "name": "WindowRenderInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers.WindowRenderInfo", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "window", "ui_content", "horizontal_scroll", "vertical_scroll", "window_width", "window_height", "configured_scroll_offsets", "visible_line_to_row_col", "rowcol_to_yx", "x_offset", "y_offset", "wrap_lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "window", "ui_content", "horizontal_scroll", "vertical_scroll", "window_width", "window_height", "configured_scroll_offsets", "visible_line_to_row_col", "rowcol_to_yx", "x_offset", "y_offset", "wrap_lines"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo", "prompt_toolkit.layout.containers.Window", "prompt_toolkit.layout.controls.UIContent", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "prompt_toolkit.layout.containers.ScrollOffsets", {".class": "Instance", "args": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WindowRenderInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_rowcol_to_yx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo._rowcol_to_yx", "name": "_rowcol_to_yx", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_x_offset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo._x_offset", "name": "_x_offset", "type": "builtins.int"}}, "_y_offset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo._y_offset", "name": "_y_offset", "type": "builtins.int"}}, "applied_scroll_offsets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.applied_scroll_offsets", "name": "applied_scroll_offsets", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applied_scroll_offsets of WindowRenderInfo", "ret_type": "prompt_toolkit.layout.containers.ScrollOffsets", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.applied_scroll_offsets", "name": "applied_scroll_offsets", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applied_scroll_offsets of WindowRenderInfo", "ret_type": "prompt_toolkit.layout.containers.ScrollOffsets", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "bottom_visible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.bottom_visible", "name": "bottom_visible", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bottom_visible of WindowRenderInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.bottom_visible", "name": "bottom_visible", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bottom_visible of WindowRenderInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "center_visible_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "before_scroll_offset", "after_scroll_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.center_visible_line", "name": "center_visible_line", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "before_scroll_offset", "after_scroll_offset"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "center_visible_line of WindowRenderInfo", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "configured_scroll_offsets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.configured_scroll_offsets", "name": "configured_scroll_offsets", "type": "prompt_toolkit.layout.containers.ScrollOffsets"}}, "content_height": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.content_height", "name": "content_height", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "content_height of WindowRenderInfo", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.content_height", "name": "content_height", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "content_height of WindowRenderInfo", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cursor_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.cursor_position", "name": "cursor_position", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cursor_position of WindowRenderInfo", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.cursor_position", "name": "cursor_position", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cursor_position of WindowRenderInfo", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.data_structures.Point"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "displayed_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.displayed_lines", "name": "displayed_lines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "displayed_lines of WindowRenderInfo", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.displayed_lines", "name": "displayed_lines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "displayed_lines of WindowRenderInfo", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "first_visible_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "after_scroll_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.first_visible_line", "name": "first_visible_line", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "after_scroll_offset"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "first_visible_line of WindowRenderInfo", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "full_height_visible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.full_height_visible", "name": "full_height_visible", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "full_height_visible of WindowRenderInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.full_height_visible", "name": "full_height_visible", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "full_height_visible of WindowRenderInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_height_for_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.get_height_for_line", "name": "get_height_for_line", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lineno"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_height_for_line of WindowRenderInfo", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "input_line_to_visible_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.input_line_to_visible_line", "name": "input_line_to_visible_line", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "input_line_to_visible_line of WindowRenderInfo", "ret_type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.input_line_to_visible_line", "name": "input_line_to_visible_line", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "input_line_to_visible_line of WindowRenderInfo", "ret_type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "last_visible_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "before_scroll_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.last_visible_line", "name": "last_visible_line", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "before_scroll_offset"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "last_visible_line of WindowRenderInfo", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "top_visible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.top_visible", "name": "top_visible", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "top_visible of WindowRenderInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.top_visible", "name": "top_visible", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "top_visible of WindowRenderInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ui_content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.ui_content", "name": "ui_content", "type": "prompt_toolkit.layout.controls.UIContent"}}, "vertical_scroll": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.vertical_scroll", "name": "vertical_scroll", "type": "builtins.int"}}, "vertical_scroll_percentage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.vertical_scroll_percentage", "name": "vertical_scroll_percentage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vertical_scroll_percentage of WindowRenderInfo", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.vertical_scroll_percentage", "name": "vertical_scroll_percentage", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vertical_scroll_percentage of WindowRenderInfo", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visible_line_to_input_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.visible_line_to_input_line", "name": "visible_line_to_input_line", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visible_line_to_input_line of WindowRenderInfo", "ret_type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.visible_line_to_input_line", "name": "visible_line_to_input_line", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers.WindowRenderInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visible_line_to_input_line of WindowRenderInfo", "ret_type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visible_line_to_row_col": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.visible_line_to_row_col", "name": "visible_line_to_row_col", "type": {".class": "Instance", "args": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.window", "name": "window", "type": "prompt_toolkit.layout.containers.Window"}}, "window_height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.window_height", "name": "window_height", "type": "builtins.int"}}, "window_width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.window_width", "name": "window_width", "type": "builtins.int"}}, "wrap_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.wrap_lines", "name": "wrap_lines", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers.WindowRenderInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers.WindowRenderInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WritePosition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.screen.WritePosition", "kind": "Gdef", "module_public": false}, "_CHAR_CACHE": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.screen._CHAR_CACHE", "kind": "Gdef", "module_public": false}, "_Split": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["preferred_height", 1], ["preferred_width", 1], ["reset", 1], ["write_to_screen", 1]], "alt_promote": null, "bases": ["prompt_toolkit.layout.containers.Container"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.layout.containers._Split", "name": "_Split", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "prompt_toolkit.layout.containers._Split", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.layout.containers", "mro": ["prompt_toolkit.layout.containers._Split", "prompt_toolkit.layout.containers.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "children", "window_too_small", "padding", "padding_char", "padding_style", "width", "height", "z_index", "modal", "key_bindings", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers._Split.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "children", "window_too_small", "padding", "padding_char", "padding_style", "width", "height", "z_index", "modal", "key_bindings", "style"], "arg_types": ["prompt_toolkit.layout.containers._Split", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["prompt_toolkit.layout.containers.Container", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Split", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "children": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers._Split.children", "name": "children", "type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers._Split.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers._Split"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of _Split", "ret_type": {".class": "Instance", "args": ["prompt_toolkit.layout.containers.Container"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_key_bindings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers._Split.get_key_bindings", "name": "get_key_bindings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers._Split"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_key_bindings of _Split", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "height": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers._Split.height", "name": "height", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}}}, "is_modal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers._Split.is_modal", "name": "is_modal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.layout.containers._Split"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_modal of _Split", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_bindings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers._Split.key_bindings", "name": "key_bindings", "type": {".class": "UnionType", "items": ["prompt_toolkit.key_binding.key_bindings.KeyBindingsBase", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "modal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers._Split.modal", "name": "modal", "type": "builtins.bool"}}, "padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers._Split.padding", "name": "padding", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}}}, "padding_char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers._Split.padding_char", "name": "padding_char", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "padding_style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers._Split.padding_style", "name": "padding_style", "type": "builtins.str"}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers._Split.style", "name": "style", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers._Split.width", "name": "width", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}}}, "window_too_small": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers._Split.window_too_small", "name": "window_too_small", "type": "prompt_toolkit.layout.containers.Container"}}, "z_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.layout.containers._Split.z_index", "name": "z_index", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.layout.containers._Split.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.layout.containers._Split", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.containers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.containers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.containers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.containers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.containers.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.layout.containers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_in_insert_mode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.layout.containers._in_insert_mode", "name": "_in_insert_mode", "type": "prompt_toolkit.filters.base.Filter"}}, "_window_too_small": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers._window_too_small", "name": "_window_too_small", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_window_too_small", "ret_type": "prompt_toolkit.layout.containers.Window", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "emacs_insert_mode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.emacs_insert_mode", "kind": "Gdef", "module_public": false}, "explode_text_fragments": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.utils.explode_text_fragments", "kind": "Gdef", "module_public": false}, "fragment_list_to_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.utils.fragment_list_to_text", "kind": "Gdef", "module_public": false}, "fragment_list_width": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.utils.fragment_list_width", "kind": "Gdef", "module_public": false}, "get_app": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app", "kind": "Gdef", "module_public": false}, "get_cwidth": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.get_cwidth", "kind": "Gdef", "module_public": false}, "is_container": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.is_container", "name": "is_container", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_container", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max_layout_dimensions": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.max_layout_dimensions", "kind": "Gdef", "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_public": false}, "sum_layout_dimensions": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.sum_layout_dimensions", "kind": "Gdef", "module_public": false}, "take_using_weights": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.take_using_weights", "kind": "Gdef", "module_public": false}, "to_container": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["container"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.to_container", "name": "to_container", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["container"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_container", "ret_type": "prompt_toolkit.layout.containers.Container", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_dimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.to_dimension", "kind": "Gdef", "module_public": false}, "to_filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.utils.to_filter", "kind": "Gdef", "module_public": false}, "to_formatted_text": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.to_formatted_text", "kind": "Gdef", "module_public": false}, "to_int": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.to_int", "kind": "Gdef", "module_public": false}, "to_str": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.utils.to_str", "kind": "Gdef", "module_public": false}, "to_window": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["container"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.layout.containers.to_window", "name": "to_window", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["container"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_window", "ret_type": "prompt_toolkit.layout.containers.Window", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vi_insert_mode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.vi_insert_mode", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\layout\\containers.py"}