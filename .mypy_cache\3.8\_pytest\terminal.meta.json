{"data_mtime": 1753839573, "dep_lines": [37, 39, 41, 46, 34, 35, 36, 38, 40, 42, 49, 51, 57, 555, 7, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 30, 32, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 10, 5, 5, 5, 25, 20, 5, 10, 5, 10, 10, 5, 10, 5, 10, 10, 10, 5, 10, 10, 20, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest._io.wcwidth", "_pytest.assertion.util", "_pytest.config.argparsing", "_pytest.nodes", "_pytest.timing", "_pytest._code", "_pytest._io", "_pytest._version", "_pytest.config", "_pytest.pathlib", "_pytest.reports", "_pytest.main", "_pytest.warnings", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "collections", "dataclasses", "datetime", "functools", "inspect", "pathlib", "platform", "sys", "textwrap", "typing", "warnings", "pluggy", "_pytest", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "os", "logging", "_collections_abc", "_frozen_importlib", "_pytest._io.terminalwriter", "abc", "enum", "pluggy._hooks", "pluggy._manager", "types", "typing_extensions"], "hash": "9eeb83d157cfda19e23373ae07c0246c68794f92", "id": "_pytest.terminal", "ignore_all": true, "interface_hash": "34fc38559779d2098c1233cd860c1aca5d86e387", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\terminal.py", "plugin_data": null, "size": 57393, "suppressed": [], "version_id": "1.15.0"}