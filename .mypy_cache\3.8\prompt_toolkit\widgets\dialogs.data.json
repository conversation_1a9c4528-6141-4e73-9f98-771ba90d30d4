{".class": "MypyFile", "_fullname": "prompt_toolkit.widgets.dialogs", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnyContainer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.AnyContainer", "kind": "Gdef", "module_public": false}, "AnyDimension": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.AnyDimension", "kind": "Gdef", "module_public": false}, "AnyFormattedText": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText", "kind": "Gdef", "module_public": false}, "Box": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.base.Box", "kind": "Gdef", "module_public": false}, "Button": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.base.Button", "kind": "Gdef", "module_public": false}, "D": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.dimension.Dimension", "kind": "Gdef", "module_public": false}, "Dialog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.widgets.dialogs.Dialog", "name": "Dialog", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.widgets.dialogs.Dialog", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_toolkit.widgets.dialogs", "mro": ["prompt_toolkit.widgets.dialogs.Dialog", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "body", "title", "buttons", "modal", "width", "with_background"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.widgets.dialogs.Dialog.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "body", "title", "buttons", "modal", "width", "with_background"], "arg_types": ["prompt_toolkit.widgets.dialogs.Dialog", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}, {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["prompt_toolkit.widgets.base.Button"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.dimension.AnyDimension"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dialog", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__pt_container__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.widgets.dialogs.Dialog.__pt_container__", "name": "__pt_container__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.widgets.dialogs.Dialog"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pt_container__ of Dialog", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.widgets.dialogs.Dialog.body", "name": "body", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.containers.AnyContainer"}}}, "container": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.widgets.dialogs.Dialog.container", "name": "container", "type": {".class": "UnionType", "items": ["prompt_toolkit.widgets.base.Box", "prompt_toolkit.widgets.base.Shadow"], "uses_pep604_syntax": true}}}, "title": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.widgets.dialogs.Dialog.title", "name": "title", "type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.AnyFormattedText"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.widgets.dialogs.Dialog.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.widgets.dialogs.Dialog", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DynamicContainer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.DynamicContainer", "kind": "Gdef", "module_public": false}, "Frame": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.base.Frame", "kind": "Gdef", "module_public": false}, "HSplit": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.HSplit", "kind": "Gdef", "module_public": false}, "KeyBindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Shadow": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.widgets.base.Shadow", "kind": "Gdef", "module_public": false}, "VSplit": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.containers.VSplit", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.widgets.dialogs.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.widgets.dialogs.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.widgets.dialogs.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.widgets.dialogs.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.widgets.dialogs.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.widgets.dialogs.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.widgets.dialogs.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "focus_next": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.focus.focus_next", "kind": "Gdef", "module_public": false}, "focus_previous": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.focus.focus_previous", "kind": "Gdef", "module_public": false}, "has_completions": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.has_completions", "kind": "Gdef", "module_public": false}, "has_focus": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.has_focus", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\widgets\\dialogs.py"}