{"data_mtime": 1753839580, "dep_lines": [5, 6, 7, 8, 9, 14, 9, 13, 15, 16, 17, 18, 1, 2, 3, 11, 20, 60, 179, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [5, 5, 5, 5, 10, 5, 20, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 20, 10, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["IPython.core.debugger", "IPython.core.completer", "IPython.terminal.ptutils", "IPython.terminal.shortcuts", "IPython.terminal.embed", "prompt_toolkit.shortcuts.prompt", "IPython.terminal", "prompt_toolkit.application", "prompt_toolkit.enums", "prompt_toolkit.formatted_text", "prompt_toolkit.history", "concurrent.futures", "asyncio", "os", "sys", "pathlib", "prompt_toolkit", "types", "pdb", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "typing", "functools", "logging", "IPython.core", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "bdb", "cmd", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "enum", "prompt_toolkit.auto_suggest", "prompt_toolkit.clipboard", "prompt_toolkit.clipboard.base", "prompt_toolkit.completion", "prompt_toolkit.completion.base", "prompt_toolkit.cursor_shapes", "prompt_toolkit.filters", "prompt_toolkit.filters.base", "prompt_toolkit.formatted_text.base", "prompt_toolkit.formatted_text.pygments", "prompt_toolkit.input", "prompt_toolkit.input.base", "prompt_toolkit.key_binding", "prompt_toolkit.key_binding.key_bindings", "prompt_toolkit.layout", "prompt_toolkit.layout.processors", "prompt_toolkit.lexers", "prompt_toolkit.lexers.base", "prompt_toolkit.mouse_events", "prompt_toolkit.output", "prompt_toolkit.output.base", "prompt_toolkit.output.color_depth", "prompt_toolkit.shortcuts", "prompt_toolkit.styles", "prompt_toolkit.styles.base", "prompt_toolkit.styles.style_transformation", "prompt_toolkit.validation", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "typing_extensions"], "hash": "315140e77da3293839383a4b9606a94e36ead7c2", "id": "IPython.terminal.debugger", "ignore_all": true, "interface_hash": "087db45dc4aa7d3c9bccfb25ce3b32db5cfcf5c9", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\terminal\\debugger.py", "plugin_data": null, "size": 7106, "suppressed": ["pygments.token"], "version_id": "1.15.0"}