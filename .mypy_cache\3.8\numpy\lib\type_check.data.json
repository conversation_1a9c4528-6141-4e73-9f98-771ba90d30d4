{".class": "MypyFile", "_fullname": "numpy.lib.type_check", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Container": {".class": "SymbolTableNode", "cross_ref": "typing.Container", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like.DTypeLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "L": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NBitBase": {".class": "SymbolTableNode", "cross_ref": "numpy._typing.NBitBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NDArray": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.NDArray", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_64Bit": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._64Bit", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like._ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_DTypeLikeComplex": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._DTypeLikeComplex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_NBit1": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "name": "_NBit1", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}}, "_NBit2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "name": "_NBit2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}}, "_SCT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "name": "_SCT", "upper_bound": "numpy.generic", "values": [], "variance": 0}}, "_ScalarLike_co": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._scalars._ScalarLike_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SupportsDType": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like._SupportsDType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SupportsImag": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib.type_check._SupportsImag", "name": "_SupportsImag", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsImag", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy.lib.type_check._SupportsImag", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.lib.type_check", "mro": ["numpy.lib.type_check._SupportsImag", "builtins.object"], "names": {".class": "SymbolTable", "imag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.lib.type_check._SupportsImag.imag", "name": "imag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsImag", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsImag"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag of _SupportsImag", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsImag", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.lib.type_check._SupportsImag.imag", "name": "imag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsImag", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsImag"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag of _SupportsImag", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsImag", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SupportsImag.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsImag", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsImag"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "_SupportsReal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "numpy.lib.type_check._SupportsReal", "name": "_SupportsReal", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsReal", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "numpy.lib.type_check._SupportsReal", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "numpy.lib.type_check", "mro": ["numpy.lib.type_check._SupportsReal", "builtins.object"], "names": {".class": "SymbolTable", "real": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "numpy.lib.type_check._SupportsReal.real", "name": "real", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsReal", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsReal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real of _SupportsReal", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsReal", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "numpy.lib.type_check._SupportsReal.real", "name": "real", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsReal", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsReal"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real of _SupportsReal", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsReal", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SupportsReal.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "id": 1, "name": "_T_co", "namespace": "numpy.lib.type_check._SupportsReal", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsReal"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T_co": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T_co", "name": "_T_co", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.type_check.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.type_check.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.type_check.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.type_check.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.type_check.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.type_check.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.lib.type_check.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asfarray": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.asfarray", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.asfarray", "name": "asfarray", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "dtype"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": "builtins.float"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asfarray", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.asfarray", "name": "asfarray", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "dtype"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": "builtins.float"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asfarray", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.asfarray", "name": "asfarray", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "dtype"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeComplex"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asfarray", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.asfarray", "name": "asfarray", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "dtype"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeComplex"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asfarray", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.asfarray", "name": "asfarray", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "dtype"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asfarray", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.asfarray", "name": "asfarray", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "dtype"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asfarray", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "dtype"], "arg_types": ["builtins.object", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeType", "item": "builtins.float"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asfarray", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy.float64"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "dtype"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like._DTypeLikeComplex"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asfarray", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "dtype"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "asfarray", "ret_type": {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "bool_": {".class": "SymbolTableNode", "cross_ref": "numpy.bool_", "kind": "Gdef", "module_hidden": true, "module_public": false}, "common_type": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.common_type", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["arrays"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.common_type", "name": "common_type", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._64Bit"], "extra_attrs": null, "type_ref": "numpy.floating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.common_type", "name": "common_type", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._64Bit"], "extra_attrs": null, "type_ref": "numpy.floating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["arrays"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.common_type", "name": "common_type", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#1", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#1", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#1", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.common_type", "name": "common_type", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#1", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#1", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#1", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["arrays"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.common_type", "name": "common_type", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, "numpy._typing._64Bit"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.common_type", "name": "common_type", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, "numpy._typing._64Bit"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["arrays"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.common_type", "name": "common_type", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.common_type", "name": "common_type", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["arrays"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.common_type", "name": "common_type", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": ["numpy._typing._64Bit", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["numpy._typing._64Bit", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.common_type", "name": "common_type", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": ["numpy._typing._64Bit", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["numpy._typing._64Bit", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": ["numpy._typing._64Bit"], "extra_attrs": null, "type_ref": "numpy.floating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#1", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#1", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#1", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, "numpy._typing._64Bit"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.floating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#2", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type#3", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [2], "arg_names": ["arrays"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.integer"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_type", "ret_type": {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "UnionType", "items": ["numpy._typing._64Bit", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["numpy._typing._64Bit", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit2", "id": -2, "name": "_NBit2", "namespace": "numpy.lib.type_check.common_type", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}]}}}, "complexfloating": {".class": "SymbolTableNode", "cross_ref": "numpy.complexfloating", "kind": "Gdef", "module_hidden": true, "module_public": false}, "dtype": {".class": "SymbolTableNode", "cross_ref": "numpy.dtype", "kind": "Gdef", "module_hidden": true, "module_public": false}, "float64": {".class": "SymbolTableNode", "cross_ref": "numpy.float64", "kind": "Gdef", "module_hidden": true, "module_public": false}, "floating": {".class": "SymbolTableNode", "cross_ref": "numpy.floating", "kind": "Gdef", "module_hidden": true, "module_public": false}, "generic": {".class": "SymbolTableNode", "cross_ref": "numpy.generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "imag": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.imag", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.imag", "name": "imag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.imag#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsImag"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.imag#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.imag#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.imag", "name": "imag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.imag#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsImag"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.imag#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.imag#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.imag", "name": "imag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.imag", "name": "imag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.imag#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsImag"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.imag#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.imag#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "integer": {".class": "SymbolTableNode", "cross_ref": "numpy.integer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "iscomplex": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.iscomplex", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.iscomplex", "name": "iscomplex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iscomplex", "ret_type": "numpy.bool_", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.iscomplex", "name": "iscomplex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iscomplex", "ret_type": "numpy.bool_", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.iscomplex", "name": "iscomplex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iscomplex", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.iscomplex", "name": "iscomplex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iscomplex", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iscomplex", "ret_type": "numpy.bool_", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iscomplex", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "iscomplexobj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.iscomplexobj", "name": "iscomplexobj", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iscomplexobj", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isreal": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.isreal", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.isreal", "name": "isreal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isreal", "ret_type": "numpy.bool_", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.isreal", "name": "isreal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isreal", "ret_type": "numpy.bool_", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.isreal", "name": "isreal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isreal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.isreal", "name": "isreal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isreal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isreal", "ret_type": "numpy.bool_", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isreal", "ret_type": {".class": "TypeAliasType", "args": ["numpy.bool_"], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "isrealobj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.isrealobj", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy._typing._dtype_like._SupportsDType"}, {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mintypecode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["typechars", "typeset", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.mintypecode", "name": "mintypecode", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["typechars", "typeset", "default"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Container"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mintypecode", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nan_to_num": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.nan_to_num", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.nan_to_num", "name": "nan_to_num", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.nan_to_num", "name": "nan_to_num", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.nan_to_num", "name": "nan_to_num", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.nan_to_num", "name": "nan_to_num", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.nan_to_num", "name": "nan_to_num", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.nan_to_num", "name": "nan_to_num", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.nan_to_num", "name": "nan_to_num", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.nan_to_num", "name": "nan_to_num", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#0", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._scalars._ScalarLike_co"}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.nan_to_num#2", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["x", "copy", "nan", "posinf", "neginf"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.bool", "builtins.float", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nan_to_num", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "real": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.real", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.real", "name": "real", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.real#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsReal"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.real#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.real#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.real", "name": "real", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.real#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsReal"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.real#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.real#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.real", "name": "real", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.real", "name": "real", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.real#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.lib.type_check._SupportsReal"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.real#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._T", "id": -1, "name": "_T", "namespace": "numpy.lib.type_check.real#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "real_if_close": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.real_if_close", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.real_if_close", "name": "real_if_close", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real_if_close", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.real_if_close", "name": "real_if_close", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real_if_close", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.real_if_close", "name": "real_if_close", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.real_if_close#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real_if_close", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.real_if_close#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.real_if_close#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.real_if_close", "name": "real_if_close", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.real_if_close#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real_if_close", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.real_if_close#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.real_if_close#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.real_if_close", "name": "real_if_close", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real_if_close", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.real_if_close", "name": "real_if_close", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real_if_close", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real_if_close", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.floating"}], "type_ref": "numpy._typing._array_like.NDArray"}, {".class": "TypeAliasType", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "numpy.complexfloating"}], "type_ref": "numpy._typing._array_like.NDArray"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._NBit1", "id": -1, "name": "_NBit1", "namespace": "numpy.lib.type_check.real_if_close#0", "upper_bound": "numpy._typing.NBitBase", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.real_if_close#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like._ArrayLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real_if_close", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.real_if_close#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "numpy.lib.type_check._SCT", "id": -1, "name": "_SCT", "namespace": "numpy.lib.type_check.real_if_close#1", "upper_bound": "numpy.generic", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["a", "tol"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real_if_close", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "numpy._typing._array_like.NDArray"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "typename": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "numpy.lib.type_check.typename", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "S1"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "character"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "S1"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "character"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "?"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "?"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "signed char"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "signed char"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "B"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned char"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "B"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned char"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "h"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "short"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "h"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "short"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "H"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned short"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "H"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned short"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "i"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "i"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "I"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "I"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "l"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "l"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "q"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "long long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "q"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "long long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "Q"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned long long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "Q"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned long long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "f"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "single precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "f"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "single precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "d"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "double precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "d"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "double precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "g"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "long precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "g"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "long precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex single precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex single precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "D"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex double precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "D"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex double precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "G"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex long double precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "G"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex long double precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "S"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "string"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "S"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "string"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "U"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unicode"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "U"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unicode"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "V"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "void"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "V"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "void"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["char"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "O"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "object"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "numpy.lib.type_check.typename", "name": "typename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "O"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "object"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "S1"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "character"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "?"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "b"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "signed char"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "B"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned char"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "h"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "short"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "H"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned short"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "i"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "I"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "l"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "L"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "q"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "long long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "Q"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unsigned long long integer"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "f"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "single precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "d"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "double precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "g"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "long precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "F"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex single precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "D"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex double precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "G"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "complex long double precision"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "S"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "string"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "U"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "unicode"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "V"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "void"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["char"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "O"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typename", "ret_type": {".class": "LiteralType", "fallback": "builtins.str", "value": "object"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\numpy\\lib\\type_check.pyi"}