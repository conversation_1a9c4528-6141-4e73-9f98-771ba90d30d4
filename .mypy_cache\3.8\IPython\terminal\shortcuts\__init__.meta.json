{"data_mtime": 1753839580, "dep_lines": [19, 20, 27, 28, 29, 16, 18, 19, 23, 26, 30, 552, 553, 17, 24, 32, 9, 10, 11, 12, 13, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 5, 5, 5, 20, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["prompt_toolkit.key_binding.bindings.named_commands", "prompt_toolkit.key_binding.bindings.completion", "IPython.terminal.shortcuts.auto_match", "IPython.terminal.shortcuts.auto_suggest", "IPython.terminal.shortcuts.filters", "prompt_toolkit.application.current", "prompt_toolkit.key_binding.key_processor", "prompt_toolkit.key_binding.bindings", "prompt_toolkit.key_binding.vi_state", "IPython.core.getipython", "IPython.utils.decorators", "IPython.core.error", "IPython.lib.clipboard", "prompt_toolkit.key_binding", "prompt_toolkit.filters", "prompt_toolkit.enums", "os", "signal", "sys", "warnings", "dataclasses", "typing", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "logging", "IPython.utils", "_collections_abc", "_frozen_importlib", "abc", "prompt_toolkit", "prompt_toolkit.filters.base", "prompt_toolkit.key_binding.key_bindings"], "hash": "aa60f6ebeca5a663cb242e3050e8567ebd3cb20c", "id": "IPython.terminal.shortcuts", "ignore_all": true, "interface_hash": "2443ed6a874016c0744335cad026c3d4a3ddd320", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\terminal\\shortcuts\\__init__.py", "plugin_data": null, "size": 18452, "suppressed": [], "version_id": "1.15.0"}