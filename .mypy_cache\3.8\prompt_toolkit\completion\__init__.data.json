{".class": "MypyFile", "_fullname": "prompt_toolkit.completion", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CompleteEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.CompleteEvent", "kind": "Gdef"}, "Completer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.Completer", "kind": "Gdef"}, "Completion": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.Completion", "kind": "Gdef"}, "ConditionalCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.ConditionalCompleter", "kind": "Gdef"}, "DeduplicateCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.deduplicate.DeduplicateCompleter", "kind": "Gdef"}, "DummyCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.DummyCompleter", "kind": "Gdef"}, "DynamicCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.DynamicCompleter", "kind": "Gdef"}, "ExecutableCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.filesystem.ExecutableCompleter", "kind": "Gdef"}, "FuzzyCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.fuzzy_completer.FuzzyCompleter", "kind": "Gdef"}, "FuzzyWordCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.fuzzy_completer.FuzzyWordCompleter", "kind": "Gdef"}, "NestedCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.nested.NestedCompleter", "kind": "Gdef"}, "PathCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.filesystem.PathCompleter", "kind": "Gdef"}, "ThreadedCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.ThreadedCompleter", "kind": "Gdef"}, "WordCompleter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.word_completer.WordCompleter", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.completion.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.completion.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "get_common_complete_suffix": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.get_common_complete_suffix", "kind": "Gdef"}, "merge_completers": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.completion.base.merge_completers", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\completion\\__init__.py"}