{".class": "MypyFile", "_fullname": "numpy.polynomial", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Chebyshev": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.chebyshev.Chebyshev", "kind": "Gdef", "module_public": false}, "Hermite": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.hermite.Hermite", "kind": "Gdef", "module_public": false}, "HermiteE": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.hermite_e.<PERSON>", "kind": "Gdef", "module_public": false}, "Laguerre": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.laguerre.Laguerre", "kind": "Gdef", "module_public": false}, "Legendre": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.legendre.Legendre", "kind": "Gdef", "module_public": false}, "Polynomial": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.polynomial.Polynomial", "kind": "Gdef", "module_public": false}, "PytestTester": {".class": "SymbolTableNode", "cross_ref": "numpy._pytesttester.PytestTester", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "chebyshev": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.cheb<PERSON><PERSON>v", "kind": "Gdef", "module_public": false}, "hermite": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.hermite", "kind": "Gdef", "module_public": false}, "hermite_e": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.hermite_e", "kind": "Gdef", "module_public": false}, "laguerre": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.laguerre", "kind": "Gdef", "module_public": false}, "legendre": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.legendre", "kind": "Gdef", "module_public": false}, "polynomial": {".class": "SymbolTableNode", "cross_ref": "numpy.polynomial.polynomial", "kind": "Gdef", "module_public": false}, "set_default_printstyle": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "numpy.polynomial.set_default_printstyle", "name": "set_default_printstyle", "type": null}}, "test": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "numpy.polynomial.test", "name": "test", "type": "numpy._pytesttester.PytestTester"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\numpy\\polynomial\\__init__.pyi"}