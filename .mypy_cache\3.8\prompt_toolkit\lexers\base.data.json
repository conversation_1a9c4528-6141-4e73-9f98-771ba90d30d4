{".class": "MypyFile", "_fullname": "prompt_toolkit.lexers.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Document": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.document.Document", "kind": "Gdef", "module_public": false}, "DynamicLexer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.lexers.base.Lexer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.lexers.base.DynamicLexer", "name": "DynamicLexer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.base.DynamicLexer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.lexers.base", "mro": ["prompt_toolkit.lexers.base.DynamicLexer", "prompt_toolkit.lexers.base.Lexer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "get_lexer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.base.DynamicLexer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "get_lexer"], "arg_types": ["prompt_toolkit.lexers.base.DynamicLexer", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.lexers.base.Lexer", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DynamicLexer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dummy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.lexers.base.DynamicLexer._dummy", "name": "_dummy", "type": "prompt_toolkit.lexers.base.SimpleLexer"}}, "get_lexer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.lexers.base.DynamicLexer.get_lexer", "name": "get_lexer", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.lexers.base.Lexer", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invalidation_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.base.DynamicLexer.invalidation_hash", "name": "invalidation_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.lexers.base.DynamicLexer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidation_hash of DynamicLexer", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lex_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.base.DynamicLexer.lex_document", "name": "lex_document", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document"], "arg_types": ["prompt_toolkit.lexers.base.DynamicLexer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lex_document of DynamicLexer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.lexers.base.DynamicLexer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.lexers.base.DynamicLexer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Hashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef", "module_public": false}, "Lexer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["lex_document", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.lexers.base.Lexer", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "prompt_toolkit.lexers.base.Lexer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.lexers.base", "mro": ["prompt_toolkit.lexers.base.Lexer", "builtins.object"], "names": {".class": "SymbolTable", "invalidation_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.base.Lexer.invalidation_hash", "name": "invalidation_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_toolkit.lexers.base.Lexer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidation_hash of <PERSON><PERSON>", "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lex_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "prompt_toolkit.lexers.base.Lexer.lex_document", "name": "lex_document", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document"], "arg_types": ["prompt_toolkit.lexers.base.Lexer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lex_document of <PERSON><PERSON>", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.lexers.base.Lexer.lex_document", "name": "lex_document", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document"], "arg_types": ["prompt_toolkit.lexers.base.Lexer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lex_document of <PERSON><PERSON>", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.lexers.base.Lexer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.lexers.base.Lexer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleLexer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.lexers.base.Lexer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_toolkit.lexers.base.SimpleLexer", "name": "SimpleLexer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.base.SimpleLexer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "prompt_toolkit.lexers.base", "mro": ["prompt_toolkit.lexers.base.SimpleLexer", "prompt_toolkit.lexers.base.Lexer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.base.SimpleLexer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "style"], "arg_types": ["prompt_toolkit.lexers.base.SimpleLexer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SimpleLexer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lex_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.lexers.base.SimpleLexer.lex_document", "name": "lex_document", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document"], "arg_types": ["prompt_toolkit.lexers.base.SimpleLexer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lex_document of SimpleLexer", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_toolkit.lexers.base.SimpleLexer.style", "name": "style", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_toolkit.lexers.base.SimpleLexer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_toolkit.lexers.base.SimpleLexer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StyleAndTextTuples": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.formatted_text.base.StyleAndTextTuples", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.lexers.base.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.lexers.base.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\lexers\\base.py"}