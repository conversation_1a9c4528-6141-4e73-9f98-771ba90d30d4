#!/usr/bin/env python3
"""
Test script to verify DeepSeek-R1 thinking functionality
"""

import requests
import json
import time

def test_thinking_functionality():
    """Test the thinking functionality with DeepSeek-R1"""
    
    # Test data
    test_url = "http://localhost:11440/api/chat"
    test_payload = {
        "model": "deepseek-r1:8b",
        "messages": [
            {
                "role": "user",
                "content": "What is 8 + 5 * 3? Show your work step by step."
            }
        ],
        "stream": False
    }
    
    print("🧪 Testing DeepSeek-R1 Thinking Functionality")
    print("=" * 50)
    print(f"📡 Sending request to: {test_url}")
    print(f"🤖 Model: {test_payload['model']}")
    print(f"💬 Prompt: {test_payload['messages'][0]['content']}")
    print("\n⏳ Waiting for response...")
    
    try:
        # Send the request
        response = requests.post(test_url, json=test_payload, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            
            print("\n✅ Response received!")
            print("=" * 50)
            
            # Check if thinking content is present
            if result.get('thinking'):
                print("🤔 THINKING PROCESS DETECTED:")
                print("-" * 30)
                print(result['thinking'])
                print("-" * 30)
                print("\n💡 FINAL RESPONSE:")
                print(result['message']['content'])
                print("\n🎉 SUCCESS: Thinking functionality is working!")
                
                # Check for reasoning metrics
                if result.get('reasoning_tokens'):
                    print(f"🧠 Reasoning Tokens: {result['reasoning_tokens']}")
                if result.get('reasoning_duration'):
                    print(f"⏱️ Reasoning Duration: {result['reasoning_duration']}ms")
                    
            else:
                print("❌ NO THINKING CONTENT FOUND")
                print("📄 Raw response:")
                print(json.dumps(result, indent=2))
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON decode error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_thinking_functionality()
