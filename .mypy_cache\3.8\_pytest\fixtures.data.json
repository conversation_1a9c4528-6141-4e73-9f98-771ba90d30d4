{".class": "MypyFile", "_fullname": "_pytest.fixtures", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractSet": {".class": "SymbolTableNode", "cross_ref": "typing.AbstractSet", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseExceptionGroup": {".class": "SymbolTableNode", "cross_ref": "exceptiongroup._exceptions.BaseExceptionGroup", "kind": "Gdef"}, "CallSpec2": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.CallSpec2", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.Config", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExitCode": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.ExitCode", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef"}, "FixtureArgKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.fixtures.FixtureArgKey", "name": "FixtureArgKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureArgKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 173, "name": "argname", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 174, "name": "param_index", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 175, "name": "scoped_item_path", "type": {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 176, "name": "item_cls", "type": {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "_pytest.fixtures", "mro": ["_pytest.fixtures.FixtureArgKey", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "_pytest.fixtures.FixtureArgKey.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "argname", "param_index", "scoped_item_path", "item_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureArgKey.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "argname", "param_index", "scoped_item_path", "item_cls"], "arg_types": ["_pytest.fixtures.FixtureArgKey", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FixtureArgKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["argname", "param_index", "scoped_item_path", "item_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.fixtures.FixtureArgKey.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["argname", "param_index", "scoped_item_path", "item_cls"], "arg_types": ["builtins.str", "builtins.int", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "_pytest.fixtures.FixtureArgKey.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["argname", "param_index", "scoped_item_path", "item_cls"], "arg_types": ["builtins.str", "builtins.int", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "argname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.FixtureArgKey.argname", "name": "argname", "type": "builtins.str"}}, "item_cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.FixtureArgKey.item_cls", "name": "item_cls", "type": {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "param_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.FixtureArgKey.param_index", "name": "param_index", "type": "builtins.int"}}, "scoped_item_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.FixtureArgKey.scoped_item_path", "name": "scoped_item_path", "type": {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureArgKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.fixtures.FixtureArgKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FixtureDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.fixtures.FixtureDef", "name": "FixtureDef", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.fixtures.FixtureDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.fixtures", "mro": ["_pytest.fixtures.FixtureDef", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 5], "arg_names": ["self", "config", "baseid", "argname", "func", "scope", "params", "ids", "_ispytest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 5], "arg_names": ["self", "config", "baseid", "argname", "func", "scope", "params", "ids", "_ispytest"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, "_pytest.config.Config", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_pytest.fixtures._FixtureFunc"}, {".class": "UnionType", "items": ["_pytest.scope.Scope", {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FixtureDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureDef.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of FixtureDef", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_finalizers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureDef._finalizers", "name": "_finalizers", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureDef._scope", "name": "_scope", "type": "_pytest.scope.Scope"}}, "addfinalizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "finalizer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureDef.addfinalizer", "name": "addfinalizer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "finalizer"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addfinalizer of FixtureDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "argname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureDef.argname", "name": "argname", "type": "builtins.str"}}, "argnames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureDef.argnames", "name": "argnames", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "baseid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureDef.baseid", "name": "baseid", "type": "builtins.str"}}, "cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureDef.cache_key", "name": "cache_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, "_pytest.fixtures.SubRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cache_key of FixtureDef", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cached_result": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureDef.cached_result", "name": "cached_result", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_pytest.fixtures._FixtureCachedResult"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureDef.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, "_pytest.fixtures.SubRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of FixtureDef", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "finish": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureDef.finish", "name": "finish", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, "_pytest.fixtures.SubRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finish of FixtureDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureDef.func", "name": "func", "type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_pytest.fixtures._FixtureFunc"}}}, "has_location": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureDef.has_location", "name": "has_location", "type": "builtins.bool"}}, "ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureDef.ids", "name": "ids", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureDef.params", "name": "params", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureDef.scope", "name": "scope", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scope of FixtureDef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureDef.scope", "name": "scope", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scope of FixtureDef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureDef.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.FixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["FixtureValue"], "typeddict_type": null}}, "FixtureFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "name": "FixtureFunction", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}}, "FixtureFunctionMarker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.fixtures.FixtureFunctionMarker", "name": "FixtureFunctionMarker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.fixtures.FixtureFunctionMarker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1181, "name": "scope", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1182, "name": "params", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1183, "name": "autouse", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1184, "name": "ids", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1185, "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": true, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1187, "name": "_ispytest", "type": "builtins.bool"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "_pytest.fixtures", "mro": ["_pytest.fixtures.FixtureFunctionMarker", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "function"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureFunctionMarker.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "function"], "arg_types": ["_pytest.fixtures.FixtureFunctionMarker", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.FixtureFunctionMarker.__call__", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FixtureFunctionMarker", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.FixtureFunctionMarker.__call__", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.FixtureFunctionMarker.__call__", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "_pytest.fixtures.FixtureFunctionMarker.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "scope", "params", "autouse", "ids", "name", "_ispytest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureFunctionMarker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "scope", "params", "autouse", "ids", "name", "_ispytest"], "arg_types": ["_pytest.fixtures.FixtureFunctionMarker", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "session"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "package"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "module"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "class"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FixtureFunctionMarker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_ispytest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureFunctionMarker.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_ispytest"], "arg_types": ["_pytest.fixtures.FixtureFunctionMarker", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of FixtureFunctionMarker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["scope", "params", "autouse", "ids", "name", "_ispytest"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.fixtures.FixtureFunctionMarker.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["scope", "params", "autouse", "ids", "name", "_ispytest"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "session"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "package"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "module"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "class"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FixtureFunction<PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "_pytest.fixtures.FixtureFunctionMarker.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["scope", "params", "autouse", "ids", "name", "_ispytest"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "session"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "package"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "module"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "class"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FixtureFunction<PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_ispytest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureFunctionMarker.__post_init__", "name": "__post_init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_ispytest"], "arg_types": ["_pytest.fixtures.FixtureFunctionMarker", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__post_init__ of FixtureFunctionMarker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "autouse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "_pytest.fixtures.FixtureFunctionMarker.autouse", "name": "autouse", "type": "builtins.bool"}}, "ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "_pytest.fixtures.FixtureFunctionMarker.ids", "name": "ids", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "_pytest.fixtures.FixtureFunctionMarker.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.FixtureFunctionMarker.params", "name": "params", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.FixtureFunctionMarker.scope", "name": "scope", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunctionMarker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.fixtures.FixtureFunctionMarker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FixtureLookupError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.LookupError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.fixtures.FixtureLookupError", "name": "FixtureLookupError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.fixtures.FixtureLookupError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.fixtures", "mro": ["_pytest.fixtures.FixtureLookupError", "builtins.LookupError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "argname", "request", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureLookupError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "argname", "request", "msg"], "arg_types": ["_pytest.fixtures.FixtureLookupError", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "_pytest.fixtures.FixtureRequest", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FixtureLookupError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "argname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.fixtures.FixtureLookupError.argname", "name": "argname", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "fixturestack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.fixtures.FixtureLookupError.fixturestack", "name": "fixturestack", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "formatrepr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureLookupError.formatrepr", "name": "formatrepr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureLookupError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatrepr of FixtureLookupError", "ret_type": "_pytest.fixtures.FixtureLookupErrorRepr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "msg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.fixtures.FixtureLookupError.msg", "name": "msg", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.fixtures.FixtureLookupError.request", "name": "request", "type": "_pytest.fixtures.FixtureRequest"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureLookupError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.fixtures.FixtureLookupError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FixtureLookupErrorRepr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest._code.code.TerminalRepr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.fixtures.FixtureLookupErrorRepr", "name": "FixtureLookupErrorRepr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureLookupErrorRepr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.fixtures", "mro": ["_pytest.fixtures.FixtureLookupErrorRepr", "_pytest._code.code.TerminalRepr", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "filename", "firstlineno", "tblines", "errorstring", "argname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureLookupErrorRepr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "filename", "firstlineno", "tblines", "errorstring", "argname"], "arg_types": ["_pytest.fixtures.FixtureLookupErrorRepr", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FixtureLookupErrorRepr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "argname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.fixtures.FixtureLookupErrorRepr.argname", "name": "argname", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "errorstring": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.fixtures.FixtureLookupErrorRepr.errorstring", "name": "errorstring", "type": "builtins.str"}}, "filename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.fixtures.FixtureLookupErrorRepr.filename", "name": "filename", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}}}, "firstlineno": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.fixtures.FixtureLookupErrorRepr.firstlineno", "name": "firstlineno", "type": "builtins.int"}}, "tblines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.fixtures.FixtureLookupErrorRepr.tblines", "name": "tblines", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "toterminal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureLookupErrorRepr.toterminal", "name": "toterminal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tw"], "arg_types": ["_pytest.fixtures.FixtureLookupErrorRepr", "_pytest._io.terminalwriter.TerminalWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "toterminal of FixtureLookupErrorRepr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureLookupErrorRepr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.fixtures.FixtureLookupErrorRepr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FixtureManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.fixtures.FixtureManager", "name": "FixtureManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.fixtures", "mro": ["_pytest.fixtures.FixtureManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "session"], "arg_types": ["_pytest.fixtures.FixtureManager", "_pytest.main.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_arg2fixturedefs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureManager._arg2fixturedefs", "name": "_arg2fixturedefs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_getautousenames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager._getautousenames", "name": "_getautousenames", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["_pytest.fixtures.FixtureManager", "_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getautousenames of FixtureManager", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_getusefixturesnames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager._getusefixturesnames", "name": "_getusefixturesnames", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["_pytest.fixtures.FixtureManager", "_pytest.nodes.Item"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getusefixturesnames of FixtureManager", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_holderobjseen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureManager._holderobjseen", "name": "_holder<PERSON><PERSON><PERSON>n", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_matchfactories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fixturedefs", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager._matchfactories", "name": "_matchfactories", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fixturedefs", "node"], "arg_types": ["_pytest.fixtures.FixtureManager", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_matchfactories of FixtureManager", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_nodeid_autousenames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureManager._nodeid_autousenames", "name": "_nodeid_autousenames", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_register_fixture": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5], "arg_names": ["self", "name", "func", "nodeid", "scope", "params", "ids", "autouse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager._register_fixture", "name": "_register_fixture", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5], "arg_names": ["self", "name", "func", "nodeid", "scope", "params", "ids", "autouse"], "arg_types": ["_pytest.fixtures.FixtureManager", "builtins.str", {".class": "TypeAliasType", "args": ["builtins.object"], "type_ref": "_pytest.fixtures._FixtureFunc"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_pytest.scope.Scope", {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_fixture of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureManager.config", "name": "config", "type": "_pytest.config.Config"}}, "getfixtureclosure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "parentnode", "initialnames", "ignore_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager.getfixtureclosure", "name": "getfixtureclosure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "parentnode", "initialnames", "ignore_args"], "arg_types": ["_pytest.fixtures.FixtureManager", "_pytest.nodes.Node", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfixtureclosure of FixtureManager", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getfixturedefs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "argname", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager.getfixturedefs", "name": "getfixturedefs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "argname", "node"], "arg_types": ["_pytest.fixtures.FixtureManager", "builtins.str", "_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfixturedefs of FixtureManager", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getfixtureinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "func", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager.getfixtureinfo", "name": "getfixtureinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "func", "cls"], "arg_types": ["_pytest.fixtures.FixtureManager", "_pytest.nodes.Item", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.type", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfixtureinfo of FixtureManager", "ret_type": "_pytest.fixtures.FuncFixtureInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parsefactories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager.parsefactories", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "node_or_obj", "nodeid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "_pytest.fixtures.FixtureManager.parsefactories", "name": "parsefactories", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "node_or_obj", "nodeid"], "arg_types": ["_pytest.fixtures.FixtureManager", {".class": "UnionType", "items": ["_pytest.nodes.Node", "builtins.object"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "_pytest.compat.NotSetType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parsefactories of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node_or_obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.fixtures.FixtureManager.parsefactories", "name": "parsefactories", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node_or_obj"], "arg_types": ["_pytest.fixtures.FixtureManager", "_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parsefactories of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureManager.parsefactories", "name": "parsefactories", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node_or_obj"], "arg_types": ["_pytest.fixtures.FixtureManager", "_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parsefactories of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node_or_obj", "nodeid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.fixtures.FixtureManager.parsefactories", "name": "parsefactories", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node_or_obj", "nodeid"], "arg_types": ["_pytest.fixtures.FixtureManager", "builtins.object", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parsefactories of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureManager.parsefactories", "name": "parsefactories", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node_or_obj", "nodeid"], "arg_types": ["_pytest.fixtures.FixtureManager", "builtins.object", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parsefactories of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node_or_obj"], "arg_types": ["_pytest.fixtures.FixtureManager", "_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parsefactories of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node_or_obj", "nodeid"], "arg_types": ["_pytest.fixtures.FixtureManager", "builtins.object", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parsefactories of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "pytest_collection_modifyitems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager.pytest_collection_modifyitems", "name": "pytest_collection_modifyitems", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items"], "arg_types": ["_pytest.fixtures.FixtureManager", {".class": "Instance", "args": ["_pytest.nodes.Item"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_collection_modifyitems of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_generate_tests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metafunc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager.pytest_generate_tests", "name": "pytest_generate_tests", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "metafunc"], "arg_types": ["_pytest.fixtures.FixtureManager", "_pytest.python.Metafunc"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_generate_tests of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_plugin_registered": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "plugin", "plugin_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureManager.pytest_plugin_registered", "name": "pytest_plugin_registered", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "plugin", "plugin_name"], "arg_types": ["_pytest.fixtures.FixtureManager", "builtins.object", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_plugin_registered of FixtureManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.fixtures.FixtureManager.session", "name": "session", "type": "_pytest.main.Session"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.fixtures.FixtureManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FixtureRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_check_scope", 1], ["_scope", 1], ["addfinalizer", 1], ["node", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.fixtures.FixtureRequest", "name": "FixtureRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "_pytest.fixtures.FixtureRequest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "_pytest.fixtures", "mro": ["_pytest.fixtures.FixtureRequest", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": ["self", "pyfuncitem", "fixturename", "arg2fixturedefs", "fixture_defs", "_ispytest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureRequest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5], "arg_names": ["self", "pyfuncitem", "fixturename", "arg2fixturedefs", "fixture_defs", "_ispytest"], "arg_types": ["_pytest.fixtures.FixtureRequest", "_pytest.python.Function", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FixtureRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_arg2fixturedefs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest._arg2fixturedefs", "name": "_arg2fixturedefs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_check_fixturedef_without_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fixturedef"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureRequest._check_fixturedef_without_param", "name": "_check_fixturedef_without_param", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fixturedef"], "arg_types": ["_pytest.fixtures.FixtureRequest", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_fixturedef_without_param of FixtureRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "requested_fixturedef", "requested_scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "_pytest.fixtures.FixtureRequest._check_scope", "name": "_check_scope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "requested_fixturedef", "requested_scope"], "arg_types": ["_pytest.fixtures.FixtureRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.PseudoFixtureDef"}], "uses_pep604_syntax": true}, "_pytest.scope.Scope"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_scope of FixtureRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest._check_scope", "name": "_check_scope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "requested_fixturedef", "requested_scope"], "arg_types": ["_pytest.fixtures.FixtureRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.PseudoFixtureDef"}], "uses_pep604_syntax": true}, "_pytest.scope.Scope"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_scope of FixtureRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_fixture_defs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest._fixture_defs", "name": "_fixture_defs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fixturemanager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureRequest._fixturemanager", "name": "_fixturemanager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fixturemanager of FixtureRequest", "ret_type": "_pytest.fixtures.FixtureManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest._fixturemanager", "name": "_fixturemanager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fixturemanager of FixtureRequest", "ret_type": "_pytest.fixtures.FixtureManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_active_fixturedef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "argname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureRequest._get_active_fixturedef", "name": "_get_active_fixturedef", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "argname"], "arg_types": ["_pytest.fixtures.FixtureRequest", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_active_fixturedef of FixtureRequest", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.PseudoFixtureDef"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_fixturestack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureRequest._get_fixturestack", "name": "_get_fixturestack", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_fixturestack of FixtureRequest", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iter_chain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureRequest._iter_chain", "name": "_iter_chain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_iter_chain of FixtureRequest", "ret_type": {".class": "Instance", "args": ["_pytest.fixtures.SubRequest"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pyfuncitem": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest._pyfuncitem", "name": "_pyfuncitem", "type": "_pytest.python.Function"}}, "_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "_pytest.fixtures.FixtureRequest._scope", "name": "_scope", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scope of FixtureRequest", "ret_type": "_pytest.scope.Scope", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest._scope", "name": "_scope", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scope of FixtureRequest", "ret_type": "_pytest.scope.Scope", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "addfinalizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "finalizer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "_pytest.fixtures.FixtureRequest.addfinalizer", "name": "addfinalizer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "finalizer"], "arg_types": ["_pytest.fixtures.FixtureRequest", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addfinalizer of FixtureRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.addfinalizer", "name": "addfinalizer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "finalizer"], "arg_types": ["_pytest.fixtures.FixtureRequest", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addfinalizer of FixtureRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "applymarker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "marker"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureRequest.applymarker", "name": "applymarker", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "marker"], "arg_types": ["_pytest.fixtures.FixtureRequest", {".class": "UnionType", "items": ["builtins.str", "_pytest.mark.structures.MarkDecorator"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "applymarker of FixtureRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureRequest.cls", "name": "cls", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.cls", "name": "cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cls of FixtureRequest", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureRequest.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config of FixtureRequest", "ret_type": "_pytest.config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config of FixtureRequest", "ret_type": "_pytest.config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fixturename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.fixturename", "name": "fixturename", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "fixturenames": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureRequest.fixturenames", "name": "fixturenames", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fixturenames of FixtureRequest", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.fixturenames", "name": "fixturenames", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fixturenames of FixtureRequest", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureRequest.function", "name": "function", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.function", "name": "function", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "function of FixtureRequest", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getfixturevalue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "argname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureRequest.getfixturevalue", "name": "getfixturevalue", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "argname"], "arg_types": ["_pytest.fixtures.FixtureRequest", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfixturevalue of FixtureRequest", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureRequest.instance", "name": "instance", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.instance", "name": "instance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance of FixtureRequest", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "keywords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureRequest.keywords", "name": "keywords", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keywords of FixtureRequest", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.keywords", "name": "keywords", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keywords of FixtureRequest", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureRequest.module", "name": "module", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.module", "name": "module", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module of FixtureRequest", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "_pytest.fixtures.FixtureRequest.node", "name": "node", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.node", "name": "node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "node of FixtureRequest", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "param": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.param", "name": "param", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureRequest.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of FixtureRequest", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of FixtureRequest", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "raiseerror": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FixtureRequest.raiseerror", "name": "raiseerror", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "arg_types": ["_pytest.fixtures.FixtureRequest", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raiseerror of FixtureRequest", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureRequest.scope", "name": "scope", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scope of FixtureRequest", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.scope", "name": "scope", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scope of FixtureRequest", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.FixtureRequest.session", "name": "session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session of FixtureRequest", "ret_type": "_pytest.main.Session", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.FixtureRequest.session", "name": "session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session of FixtureRequest", "ret_type": "_pytest.main.Session", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.fixtures.FixtureRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FixtureValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "name": "FixtureValue", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "FormattedExcinfo": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.FormattedExcinfo", "kind": "Gdef"}, "FuncFixtureInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.fixtures.FuncFixtureInfo", "name": "FuncFixtureInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FuncFixtureInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 313, "name": "argnames", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 317, "name": "initialnames", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 320, "name": "names_closure", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 325, "name": "name2fixturedefs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "_pytest.fixtures", "mro": ["_pytest.fixtures.FuncFixtureInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "_pytest.fixtures.FuncFixtureInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "argnames", "initialnames", "names_closure", "name2fixturedefs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FuncFixtureInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "argnames", "initialnames", "names_closure", "name2fixturedefs"], "arg_types": ["_pytest.fixtures.FuncFixtureInfo", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FuncFixtureInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["argnames", "initialnames", "names_closure", "name2fixturedefs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.fixtures.FuncFixtureInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["argnames", "initialnames", "names_closure", "name2fixturedefs"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FuncFixtureInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "_pytest.fixtures.FuncFixtureInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["argnames", "initialnames", "names_closure", "name2fixturedefs"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of FuncFixtureInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "_pytest.fixtures.FuncFixtureInfo.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "argnames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.FuncFixtureInfo.argnames", "name": "argnames", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "initialnames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.FuncFixtureInfo.initialnames", "name": "initialnames", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "name2fixturedefs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.FuncFixtureInfo.name2fixturedefs", "name": "name2fixturedefs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "names_closure": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.FuncFixtureInfo.names_closure", "name": "names_closure", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "prune_dependency_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.FuncFixtureInfo.prune_dependency_tree", "name": "prune_dependency_tree", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.FuncFixtureInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prune_dependency_tree of FuncFixtureInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FuncFixtureInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.fixtures.FuncFixtureInfo", "values": [], "variance": 0}, "slots": ["argnames", "initialnames", "name2fixturedefs", "names_closure"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Function": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.Function", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "HIGH_SCOPES": {".class": "SymbolTableNode", "cross_ref": "_pytest.scope.HIGH_SCOPES", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "MARKED_FIXTURE": {".class": "SymbolTableNode", "cross_ref": "_pytest.deprecated.MARKED_FIXTURE", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Mark": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.Mark", "kind": "Gdef"}, "MarkDecorator": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.MarkDecorator", "kind": "Gdef"}, "Metafunc": {".class": "SymbolTableNode", "cross_ref": "_pytest.python.Metafunc", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NOTSET": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.NOTSET", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "NotSetType": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.NotSetType", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "typing.OrderedDict", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures._V", "id": 1, "name": "_V", "namespace": "_pytest.fixtures.OrderedSet", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "_pytest.fixtures.OrderedSet", "line": 180, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures._V", "id": 1, "name": "_V", "namespace": "_pytest.fixtures.OrderedSet", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ParameterSet": {".class": "SymbolTableNode", "cross_ref": "_pytest.mark.structures.ParameterSet", "kind": "Gdef"}, "Parser": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.argparsing.Parser", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PseudoFixtureDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.fixtures.PseudoFixtureDef", "name": "PseudoFixtureDef", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.PseudoFixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.PseudoFixtureDef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 117, "name": "cached_result", "type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.PseudoFixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_pytest.fixtures._FixtureCachedResult"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 118, "name": "_scope", "type": "_pytest.scope.Scope"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "_pytest.fixtures", "mro": ["_pytest.fixtures.PseudoFixtureDef", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "_pytest.fixtures.PseudoFixtureDef.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cached_result", "_scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.PseudoFixtureDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cached_result", "_scope"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.PseudoFixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.PseudoFixtureDef"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.PseudoFixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_pytest.fixtures._FixtureCachedResult"}, "_pytest.scope.Scope"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PseudoFixtureDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["cached_result", "_scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.fixtures.PseudoFixtureDef.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["cached_result", "_scope"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.PseudoFixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_pytest.fixtures._FixtureCachedResult"}, "_pytest.scope.Scope"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of PseudoFixtureDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "_pytest.fixtures.PseudoFixtureDef.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["cached_result", "_scope"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.PseudoFixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_pytest.fixtures._FixtureCachedResult"}, "_pytest.scope.Scope"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of PseudoFixtureDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.PseudoFixtureDef._scope", "name": "_scope", "type": "_pytest.scope.Scope"}}, "cached_result": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "_pytest.fixtures.PseudoFixtureDef.cached_result", "name": "cached_result", "type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.PseudoFixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_pytest.fixtures._FixtureCachedResult"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.PseudoFixtureDef.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures.PseudoFixtureDef", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.PseudoFixtureDef"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["FixtureValue"], "typeddict_type": null}}, "Scope": {".class": "SymbolTableNode", "cross_ref": "_pytest.scope.Scope", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "_pytest.main.Session", "kind": "Gdef"}, "Source": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.source.Source", "kind": "Gdef"}, "SubRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.fixtures.FixtureRequest"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.fixtures.SubRequest", "name": "SubRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.fixtures.SubRequest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "_pytest.fixtures", "mro": ["_pytest.fixtures.SubRequest", "_pytest.fixtures.FixtureRequest", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 5], "arg_names": ["self", "request", "scope", "param", "param_index", "fixturedef", "_ispytest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.SubRequest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 5], "arg_names": ["self", "request", "scope", "param", "param_index", "fixturedef", "_ispytest"], "arg_types": ["_pytest.fixtures.SubRequest", "_pytest.fixtures.FixtureRequest", "_pytest.scope.Scope", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SubRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.SubRequest.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["_pytest.fixtures.SubRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of SubRequest", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "requested_fixturedef", "requested_scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.SubRequest._check_scope", "name": "_check_scope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "requested_fixturedef", "requested_scope"], "arg_types": ["_pytest.fixtures.SubRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.PseudoFixtureDef"}], "uses_pep604_syntax": true}, "_pytest.scope.Scope"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_scope of SubRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fixturedef": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.SubRequest._fixturedef", "name": "_fixturedef", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}}}, "_format_fixturedef_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fixturedef"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.SubRequest._format_fixturedef_line", "name": "_format_fixturedef_line", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fixturedef"], "arg_types": ["_pytest.fixtures.SubRequest", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_fixturedef_line of SubRequest", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parent_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.SubRequest._parent_request", "name": "_parent_request", "type": "_pytest.fixtures.FixtureRequest"}}, "_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.SubRequest._scope", "name": "_scope", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.SubRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scope of SubRequest", "ret_type": "_pytest.scope.Scope", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.SubRequest._scope", "name": "_scope", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.SubRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scope of SubRequest", "ret_type": "_pytest.scope.Scope", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_scope_field": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.SubRequest._scope_field", "name": "_scope_field", "type": "_pytest.scope.Scope"}}, "addfinalizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "finalizer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.SubRequest.addfinalizer", "name": "addfinalizer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "finalizer"], "arg_types": ["_pytest.fixtures.SubRequest", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addfinalizer of SubRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.SubRequest.node", "name": "node", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.SubRequest.node", "name": "node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.SubRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "node of SubRequest", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "param_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_final", "explicit_self_type", "is_inferred"], "fullname": "_pytest.fixtures.SubRequest.param_index", "name": "param_index", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.SubRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.fixtures.SubRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TEST_OUTCOME": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.TEST_OUTCOME", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TerminalRepr": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.TerminalRepr", "kind": "Gdef"}, "TerminalWriter": {".class": "SymbolTableNode", "cross_ref": "_pytest._io.terminalwriter.TerminalWriter", "kind": "Gdef"}, "TopRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_pytest.fixtures.FixtureRequest"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.fixtures.TopRequest", "name": "TopRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.fixtures.TopRequest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "_pytest.fixtures", "mro": ["_pytest.fixtures.TopRequest", "_pytest.fixtures.FixtureRequest", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "pyfuncitem", "_ispytest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.TopRequest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "pyfuncitem", "_ispytest"], "arg_types": ["_pytest.fixtures.TopRequest", "_pytest.python.Function", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TopRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.TopRequest.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["_pytest.fixtures.TopRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of TopRequest", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "requested_fixturedef", "requested_scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.TopRequest._check_scope", "name": "_check_scope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "requested_fixturedef", "requested_scope"], "arg_types": ["_pytest.fixtures.TopRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.PseudoFixtureDef"}], "uses_pep604_syntax": true}, "_pytest.scope.Scope"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_scope of TopRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fillfixtures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.TopRequest._fillfixtures", "name": "_fillfixtures", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.TopRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fillfixtures of TopRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.TopRequest._scope", "name": "_scope", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.TopRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scope of TopRequest", "ret_type": "_pytest.scope.Scope", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.TopRequest._scope", "name": "_scope", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.TopRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_scope of TopRequest", "ret_type": "_pytest.scope.Scope", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "addfinalizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "finalizer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.TopRequest.addfinalizer", "name": "addfinalizer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "finalizer"], "arg_types": ["_pytest.fixtures.TopRequest", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "addfinalizer of TopRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.fixtures.TopRequest.node", "name": "node", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.fixtures.TopRequest.node", "name": "node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_pytest.fixtures.TopRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "node of TopRequest", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.TopRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.fixtures.TopRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "YIELD_FIXTURE": {".class": "SymbolTableNode", "cross_ref": "_pytest.deprecated.YIELD_FIXTURE", "kind": "Gdef"}, "_FixtureCachedResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures._FixtureCachedResult", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "_pytest.fixtures._FixtureCachedResult", "line": 97, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures._FixtureCachedResult", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.object", {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "NoneType"}, "builtins.object", {".class": "TupleType", "implicit": false, "items": ["builtins.BaseException", {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}}}, "_FixtureFunc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures._FixtureFunc", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "_pytest.fixtures._FixtureFunc", "line": 93, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures._FixtureFunc", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": 1, "name": "FixtureValue", "namespace": "_pytest.fixtures._FixtureFunc", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}}}, "_PYTEST_DIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.fixtures._PYTEST_DIR", "name": "_PYTEST_DIR", "type": "pathlib.Path"}}, "_PluggyPlugin": {".class": "SymbolTableNode", "cross_ref": "_pytest.config._PluggyPlugin", "kind": "Gdef"}, "_PytestWrapper": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat._PytestWrapper", "kind": "Gdef"}, "_ScopeName": {".class": "SymbolTableNode", "cross_ref": "_pytest.scope._ScopeName", "kind": "Gdef"}, "_V": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures._V", "name": "_V", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.fixtures.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.fixtures.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.fixtures.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.fixtures.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.fixtures.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.fixtures.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_eval_scope_callable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["scope_callable", "fixture_name", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures._eval_scope_callable", "name": "_eval_scope_callable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["scope_callable", "fixture_name", "config"], "arg_types": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_eval_scope_callable", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_direct_parametrize_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures._get_direct_parametrize_args", "name": "_get_direct_parametrize_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": ["_pytest.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_direct_parametrize_args", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pretty_fixture_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["invocation_dir", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures._pretty_fixture_path", "name": "_pretty_fixture_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["invocation_dir", "func"], "arg_types": ["pathlib.Path", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pretty_fixture_path", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pytest": {".class": "SymbolTableNode", "cross_ref": "_pytest", "kind": "Gdef"}, "_show_fixtures_per_test": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures._show_fixtures_per_test", "name": "_show_fixtures_per_test", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "session"], "arg_types": ["_pytest.config.Config", "_pytest.main.Session"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_show_fixtures_per_test", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_showfixtures_main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures._showfixtures_main", "name": "_showfixtures_main", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "session"], "arg_types": ["_pytest.config.Config", "_pytest.main.Session"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_showfixtures_main", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_teardown_yield_fixture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fixturefunc", "it"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures._teardown_yield_fixture", "name": "_teardown_yield_fixture", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fixturefunc", "it"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_teardown_yield_fixture", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "absolutepath": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.absolutepath", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.assert_never", "kind": "Gdef"}, "bestrelpath": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.bestrelpath", "kind": "Gdef"}, "call_fixture_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["fixturefunc", "request", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.call_fixture_func", "name": "call_fixture_func", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["fixturefunc", "request", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": -1, "name": "FixtureValue", "namespace": "_pytest.fixtures.call_fixture_func", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_pytest.fixtures._FixtureFunc"}, "_pytest.fixtures.FixtureRequest", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_fixture_func", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": -1, "name": "FixtureValue", "namespace": "_pytest.fixtures.call_fixture_func", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": -1, "name": "FixtureValue", "namespace": "_pytest.fixtures.call_fixture_func", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "check_ispytest": {".class": "SymbolTableNode", "cross_ref": "_pytest.deprecated.check_ispytest", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "deduplicate_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["seqs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.deduplicate_names", "name": "deduplicate_names", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["seqs"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deduplicate_names", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "fail": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.fail", "kind": "Gdef"}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef"}, "fixture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.fixture", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5], "arg_names": ["fixture_function", "scope", "params", "autouse", "ids", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "_pytest.fixtures.fixture", "name": "fixture", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5], "arg_names": ["fixture_function", "scope", "params", "autouse", "ids", "name"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fixture", "ret_type": {".class": "UnionType", "items": ["_pytest.fixtures.FixtureFunctionMarker", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["fixture_function", "scope", "params", "autouse", "ids", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.fixtures.fixture", "name": "fixture", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["fixture_function", "scope", "params", "autouse", "ids", "name"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture#0", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fixture", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture#0", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture#0", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.fixtures.fixture", "name": "fixture", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["fixture_function", "scope", "params", "autouse", "ids", "name"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture#0", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fixture", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture#0", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture#0", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5], "arg_names": ["fixture_function", "scope", "params", "autouse", "ids", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.fixtures.fixture", "name": "fixture", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5], "arg_names": ["fixture_function", "scope", "params", "autouse", "ids", "name"], "arg_types": [{".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fixture", "ret_type": "_pytest.fixtures.FixtureFunctionMarker", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.fixtures.fixture", "name": "fixture", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5], "arg_names": ["fixture_function", "scope", "params", "autouse", "ids", "name"], "arg_types": [{".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fixture", "ret_type": "_pytest.fixtures.FixtureFunctionMarker", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["fixture_function", "scope", "params", "autouse", "ids", "name"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture#0", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fixture", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture#0", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.fixture#0", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5], "arg_names": ["fixture_function", "scope", "params", "autouse", "ids", "name"], "arg_types": [{".class": "NoneType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "_pytest.config.Config"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.scope._ScopeName"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fixture", "ret_type": "_pytest.fixtures.FixtureFunctionMarker", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_parametrized_fixture_argkeys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["item", "scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.get_parametrized_fixture_argkeys", "name": "get_parametrized_fixture_argkeys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["item", "scope"], "arg_types": ["_pytest.nodes.Item", "_pytest.scope.Scope"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_parametrized_fixture_argkeys", "ret_type": {".class": "Instance", "args": ["_pytest.fixtures.FixtureArgKey"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_real_func": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.get_real_func", "kind": "Gdef"}, "get_real_method": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.get_real_method", "kind": "Gdef"}, "get_scope_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["node", "scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.get_scope_node", "name": "get_scope_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["node", "scope"], "arg_types": ["_pytest.nodes.Node", "_pytest.scope.Scope"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_scope_node", "ret_type": {".class": "UnionType", "items": ["_pytest.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_scope_package": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["node", "fixturedef"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.get_scope_package", "name": "get_scope_package", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["node", "fixturedef"], "arg_types": ["_pytest.nodes.Item", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_scope_package", "ret_type": {".class": "UnionType", "items": ["_pytest.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getfixturemarker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.getfixturemarker", "name": "getfixturemarker", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfixturemarker", "ret_type": {".class": "UnionType", "items": ["_pytest.fixtures.FixtureFunctionMarker", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getfslineno": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.getfslineno", "kind": "Gdef"}, "getfuncargnames": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.getfuncargnames", "kind": "Gdef"}, "getimfunc": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.getimfunc", "kind": "Gdef"}, "getlocation": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.getlocation", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_generator": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.is_generator", "kind": "Gdef"}, "nodes": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "pytest_addoption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.pytest_addoption", "name": "pytest_addoption", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["parser"], "arg_types": ["_pytest.config.argparsing.Parser"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_addoption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_cmdline_main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.pytest_cmdline_main", "name": "pytest_cmdline_main", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_cmdline_main", "ret_type": {".class": "UnionType", "items": ["builtins.int", "_pytest.config.ExitCode", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_fixture_setup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fixturedef", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.pytest_fixture_setup", "name": "pytest_fixture_setup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fixturedef", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": -1, "name": "FixtureValue", "namespace": "_pytest.fixtures.pytest_fixture_setup", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, "_pytest.fixtures.SubRequest"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_fixture_setup", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": -1, "name": "FixtureValue", "namespace": "_pytest.fixtures.pytest_fixture_setup", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": -1, "name": "FixtureValue", "namespace": "_pytest.fixtures.pytest_fixture_setup", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "pytest_sessionstart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.pytest_sessionstart", "name": "pytest_sessionstart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["_pytest.main.Session"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_sessionstart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytestconfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.fixtures.pytestconfig", "name": "pytestconfig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytestconfig", "ret_type": "_pytest.config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.fixtures.pytestconfig", "name": "pytestconfig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytestconfig", "ret_type": "_pytest.config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reorder_items": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.reorder_items", "name": "reorder_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["items"], "arg_types": [{".class": "Instance", "args": ["_pytest.nodes.Item"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reorder_items", "ret_type": {".class": "Instance", "args": ["_pytest.nodes.Item"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reorder_items_atscope": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["items", "argkeys_by_item", "items_by_argkey", "scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.reorder_items_atscope", "name": "reorder_items_atscope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["items", "argkeys_by_item", "items_by_argkey", "scope"], "arg_types": [{".class": "TypeAliasType", "args": ["_pytest.nodes.Item"], "type_ref": "_pytest.fixtures.OrderedSet"}, {".class": "Instance", "args": ["_pytest.scope.Scope", {".class": "Instance", "args": ["_pytest.nodes.Item", {".class": "TypeAliasType", "args": ["_pytest.fixtures.FixtureArgKey"], "type_ref": "_pytest.fixtures.OrderedSet"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["_pytest.scope.Scope", {".class": "Instance", "args": ["_pytest.fixtures.FixtureArgKey", {".class": "Instance", "args": ["_pytest.nodes.Item", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "collections.OrderedDict"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "_pytest.scope.Scope"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reorder_items_atscope", "ret_type": {".class": "TypeAliasType", "args": ["_pytest.nodes.Item"], "type_ref": "_pytest.fixtures.OrderedSet"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve_fixture_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["fixturedef", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.resolve_fixture_function", "name": "resolve_fixture_function", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["fixturedef", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": -1, "name": "FixtureValue", "namespace": "_pytest.fixtures.resolve_fixture_function", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.fixtures.FixtureDef"}, "_pytest.fixtures.FixtureRequest"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_fixture_function", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": -1, "name": "FixtureValue", "namespace": "_pytest.fixtures.resolve_fixture_function", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_pytest.fixtures._FixtureFunc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureValue", "id": -1, "name": "FixtureValue", "namespace": "_pytest.fixtures.resolve_fixture_function", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "safe_getattr": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.safe_getattr", "kind": "Gdef"}, "safe_isclass": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.safe_isclass", "kind": "Gdef"}, "show_fixtures_per_test": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.show_fixtures_per_test", "name": "show_fixtures_per_test", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show_fixtures_per_test", "ret_type": {".class": "UnionType", "items": ["builtins.int", "_pytest.config.ExitCode"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "showfixtures": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.showfixtures", "name": "showfixtures", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "showfixtures", "ret_type": {".class": "UnionType", "items": ["builtins.int", "_pytest.config.ExitCode"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skip": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.skip", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "wrap_function_to_error_out_if_called_directly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["function", "fixture_marker"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.wrap_function_to_error_out_if_called_directly", "name": "wrap_function_to_error_out_if_called_directly", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["function", "fixture_marker"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.wrap_function_to_error_out_if_called_directly", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "_pytest.fixtures.FixtureFunctionMarker"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_function_to_error_out_if_called_directly", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.wrap_function_to_error_out_if_called_directly", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.fixtures.FixtureFunction", "id": -1, "name": "FixtureFunction", "namespace": "_pytest.fixtures.wrap_function_to_error_out_if_called_directly", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}, "write_docstring": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["tw", "doc", "indent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.write_docstring", "name": "write_docstring", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["tw", "doc", "indent"], "arg_types": ["_pytest._io.terminalwriter.TerminalWriter", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_docstring", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "yield_fixture": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 2, 5, 5, 5, 5, 5], "arg_names": ["fixture_function", "args", "scope", "params", "autouse", "ids", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.fixtures.yield_fixture", "name": "yield_fixture", "type": null}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\fixtures.py"}