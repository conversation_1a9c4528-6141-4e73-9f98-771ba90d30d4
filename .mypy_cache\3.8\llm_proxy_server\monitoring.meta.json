{"data_mtime": 1753887093, "dep_lines": [17, 18, 19, 20, 21, 22, 23, 24, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["time", "json", "os", "datetime", "typing", "collections", "pathlib", "structlog", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing_extensions"], "hash": "eef75f68beb4e457b31d54ca355019376f915881", "id": "llm_proxy_server.monitoring", "ignore_all": true, "interface_hash": "415a163a0c80779ea198968bf456ba3475fae74c", "mtime": 1753831879, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\llm_proxy_server\\monitoring.py", "plugin_data": null, "size": 28724, "suppressed": [], "version_id": "1.15.0"}