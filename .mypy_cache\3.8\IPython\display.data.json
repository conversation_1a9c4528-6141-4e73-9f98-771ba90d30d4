{".class": "MypyFile", "_fullname": "IPython.display", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Audio": {".class": "SymbolTableNode", "cross_ref": "IPython.lib.display.Audio", "kind": "Gdef"}, "Code": {".class": "SymbolTableNode", "cross_ref": "IPython.lib.display.Code", "kind": "Gdef"}, "DisplayHandle": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display_functions.DisplayHandle", "kind": "Gdef"}, "DisplayObject": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.DisplayObject", "kind": "Gdef"}, "FileLink": {".class": "SymbolTableNode", "cross_ref": "IPython.lib.display.FileLink", "kind": "Gdef"}, "FileLinks": {".class": "SymbolTableNode", "cross_ref": "IPython.lib.display.FileLinks", "kind": "Gdef"}, "GeoJSON": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.GeoJSON", "kind": "Gdef"}, "HTML": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.HTML", "kind": "Gdef"}, "IFrame": {".class": "SymbolTableNode", "cross_ref": "IPython.lib.display.IFrame", "kind": "Gdef"}, "Image": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.Image", "kind": "Gdef"}, "JSON": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.JSON", "kind": "Gdef"}, "Javascript": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.Javascript", "kind": "Gdef"}, "Latex": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.Latex", "kind": "Gdef"}, "Markdown": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.Markdown", "kind": "Gdef"}, "Math": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.Math", "kind": "Gdef"}, "Pretty": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.Pretty", "kind": "Gdef"}, "ProgressBar": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.ProgressBar", "kind": "Gdef"}, "SVG": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.SVG", "kind": "Gdef"}, "ScribdDocument": {".class": "SymbolTableNode", "cross_ref": "IPython.lib.display.ScribdDocument", "kind": "Gdef"}, "TextDisplayObject": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.TextDisplayObject", "kind": "Gdef"}, "Video": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.Video", "kind": "Gdef"}, "VimeoVideo": {".class": "SymbolTableNode", "cross_ref": "IPython.lib.display.VimeoVideo", "kind": "Gdef"}, "YouTubeVideo": {".class": "SymbolTableNode", "cross_ref": "IPython.lib.display.YouTubeVideo", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.display.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.display.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.display.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.display.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.display.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.display.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "clear_output": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display_functions.clear_output", "kind": "Gdef"}, "display": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display_functions.display", "kind": "Gdef"}, "display_html": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.display_html", "kind": "Gdef"}, "display_javascript": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.display_javascript", "kind": "Gdef"}, "display_jpeg": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.display_jpeg", "kind": "Gdef"}, "display_json": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.display_json", "kind": "Gdef"}, "display_latex": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.display_latex", "kind": "Gdef"}, "display_markdown": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.display_markdown", "kind": "Gdef"}, "display_pdf": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.display_pdf", "kind": "Gdef"}, "display_png": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.display_png", "kind": "Gdef"}, "display_pretty": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.display_pretty", "kind": "Gdef"}, "display_svg": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.display_svg", "kind": "Gdef"}, "publish_display_data": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display_functions.publish_display_data", "kind": "Gdef"}, "set_matplotlib_close": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.set_matplotlib_close", "kind": "Gdef"}, "set_matplotlib_formats": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display.set_matplotlib_formats", "kind": "Gdef"}, "update_display": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display_functions.update_display", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\display.py"}