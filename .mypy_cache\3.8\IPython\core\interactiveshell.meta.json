{"data_mtime": 1753839580, "dep_lines": [93, 94, 96, 97, 97, 97, 97, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 116, 117, 120, 121, 121, 121, 121, 122, 124, 125, 126, 127, 128, 129, 172, 2266, 2267, 2369, 2682, 3657, 96, 118, 119, 121, 3872, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 78, 96, 153, 941, 2990, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 136, 3672, 41, 136], "dep_prios": [5, 5, 10, 10, 5, 10, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 5, 5, 20, 20, 10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 5, 5, 20, 5, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20, 5, 20], "dependencies": ["traitlets.config.configurable", "traitlets.utils.importstring", "IPython.core.hooks", "IPython.core.magic", "IPython.core.oinspect", "IPython.core.page", "IPython.core.prefilter", "IPython.core.ultratb", "IPython.core.alias", "IPython.core.autocall", "IPython.core.builtin_trap", "IPython.core.compilerop", "IPython.core.debugger", "IPython.core.display_trap", "IPython.core.displayhook", "IPython.core.displaypub", "IPython.core.error", "IPython.core.events", "IPython.core.extensions", "IPython.core.formatters", "IPython.core.history", "IPython.core.inputtransformer2", "IPython.core.logger", "IPython.core.macro", "IPython.core.payload", "IPython.core.profiledir", "IPython.core.usage", "IPython.testing.skipdoctest", "IPython.utils.PyColorize", "IPython.utils.io", "IPython.utils.openpy", "IPython.utils.py3compat", "IPython.utils.decorators", "IPython.utils.ipstruct", "IPython.utils.path", "IPython.utils.process", "IPython.utils.strdispatch", "IPython.utils.syspathcontext", "IPython.utils.text", "IPython.core.async_helpers", "IPython.core.completer", "IPython.core.completerlib", "IPython.core.magics", "IPython.utils._process_win32", "IPython.core.pylabtools", "IPython.core", "IPython.display", "IPython.paths", "IPython.utils", "urllib.request", "abc", "ast", "atexit", "bdb", "builtins", "functools", "inspect", "os", "re", "runpy", "shutil", "subprocess", "sys", "tempfile", "traceback", "types", "warnings", "io", "logging", "pathlib", "typing", "traitlets", "IPython", "exceptiongroup", "site", "nbformat", "json", "traitlets.utils.warnings", "pprint", "IPython.core.display_functions", "IPython.core.magics.auto", "IPython.core.magics.basic", "IPython.core.magics.code", "IPython.core.magics.config", "IPython.core.magics.display", "IPython.core.magics.execution", "IPython.core.magics.extension", "IPython.core.magics.history", "IPython.core.magics.logging", "IPython.core.magics.namespace", "IPython.core.magics.osm", "IPython.core.magics.packaging", "IPython.core.magics.pylab", "IPython.core.magics.script", "IPython.testing", "IPython.utils.colorable", "IPython.utils.coloransi", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "_warnings", "cmd", "codeop", "enum", "pdb", "string", "traitlets.config", "traitlets.config.loader", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel", "typing_extensions"], "hash": "16a254e4f588514a2cd1ca502b3acd5187db749c", "id": "IPython.core.interactiveshell", "ignore_all": true, "interface_hash": "9fabf6ee6568fdad709c6b28a43b5218ae6ea46f", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\interactiveshell.py", "plugin_data": null, "size": 154681, "suppressed": ["docrepr.sphinxify", "matplotlib_inline.backend_inline", "pickleshare", "docrepr"], "version_id": "1.15.0"}