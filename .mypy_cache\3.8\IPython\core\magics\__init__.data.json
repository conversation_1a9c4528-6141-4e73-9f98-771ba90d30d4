{".class": "MypyFile", "_fullname": "IPython.core.magics", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.basic.AsyncMagics", "kind": "Gdef"}, "AutoMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.auto.AutoMagics", "kind": "Gdef"}, "BasicMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.basic.BasicMagics", "kind": "Gdef"}, "CodeMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.code.CodeMagics", "kind": "Gdef"}, "ConfigMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.config.ConfigMagics", "kind": "Gdef"}, "DisplayMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.display.DisplayMagics", "kind": "Gdef"}, "ExecutionMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.execution.ExecutionMagics", "kind": "Gdef"}, "ExtensionMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.extension.ExtensionMagics", "kind": "Gdef"}, "HistoryMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.history.HistoryMagics", "kind": "Gdef"}, "LoggingMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.logging.LoggingMagics", "kind": "Gdef"}, "MacroToEdit": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.code.MacroToEdit", "kind": "Gdef"}, "Magics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.Magics", "kind": "Gdef"}, "NamespaceMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.namespace.NamespaceMagics", "kind": "Gdef"}, "OSMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.osm.OSMagics", "kind": "Gdef"}, "PackagingMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.packaging.PackagingMagics", "kind": "Gdef"}, "PylabMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.pylab.PylabMagics", "kind": "Gdef"}, "ScriptMagics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.script.ScriptMagics", "kind": "Gdef"}, "UserMagics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.magic.Magics"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magics.UserMagics", "name": "UserMagics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magics.UserMagics", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.magics", "mro": ["IPython.core.magics.UserMagics", "IPython.core.magic.Magics", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magics.UserMagics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magics.UserMagics", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "magics_class": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.magics_class", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\magics\\__init__.py"}