{".class": "MypyFile", "_fullname": "IPython.terminal.shortcuts.auto_match", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "KeyPressEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_processor.KeyPressEvent", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_match.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_match.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_match.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_match.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_match.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_match.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "auto_match_parens": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.auto_match.auto_match_parens", "name": "auto_match_parens", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "auto_match_parens_raw_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.auto_match.auto_match_parens_raw_string", "name": "auto_match_parens_raw_string", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "braces": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.braces", "name": "braces", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "braces", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "brackets": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.brackets", "name": "brackets", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "brackets", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_pair": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.delete_pair", "name": "delete_pair", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_pair", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "docstring_double_quotes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.docstring_double_quotes", "name": "docstring_double_quotes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "docstring_double_quotes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "docstring_single_quotes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.docstring_single_quotes", "name": "docstring_single_quotes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "docstring_single_quotes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "double_quote": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.double_quote", "name": "double_quote", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "double_quote", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parenthesis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.parenthesis", "name": "parenthesis", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parenthesis", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "raw_string_braces": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.raw_string_braces", "name": "raw_string_braces", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw_string_braces", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "raw_string_bracket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.raw_string_bracket", "name": "raw_string_bracket", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw_string_bracket", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "raw_string_parenthesis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.raw_string_parenthesis", "name": "raw_string_parenthesis", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw_string_parenthesis", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "single_quote": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.single_quote", "name": "single_quote", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "single_quote", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skip_over": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_match.skip_over", "name": "skip_over", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skip_over", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\terminal\\shortcuts\\auto_match.py"}