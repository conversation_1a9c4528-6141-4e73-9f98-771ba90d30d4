{".class": "MypyFile", "_fullname": "IPython.core.magic", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Bool": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Bool", "kind": "Gdef"}, "Bunch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magic.Bunch", "name": "Bunch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magic.Bunch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.magic", "mro": ["IPython.core.magic.Bunch", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magic.Bunch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magic.Bunch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Configurable": {".class": "SymbolTableNode", "cross_ref": "traitlets.config.configurable.Configurable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Dict", "kind": "Gdef"}, "ESC_MAGIC": {".class": "SymbolTableNode", "cross_ref": "IPython.core.inputtransformer2.ESC_MAGIC", "kind": "Gdef"}, "ESC_MAGIC2": {".class": "SymbolTableNode", "cross_ref": "IPython.core.inputtransformer2.ESC_MAGIC2", "kind": "Gdef"}, "GetoptError": {".class": "SymbolTableNode", "cross_ref": "getopt.GetoptError", "kind": "Gdef"}, "Instance": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Instance", "kind": "Gdef"}, "MAGIC_NO_VAR_EXPAND_ATTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.MAGIC_NO_VAR_EXPAND_ATTR", "name": "MAGIC_NO_VAR_EXPAND_ATTR", "type": "builtins.str"}}, "MAGIC_OUTPUT_CAN_BE_SILENCED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.MAGIC_OUTPUT_CAN_BE_SILENCED", "name": "MAGIC_OUTPUT_CAN_BE_SILENCED", "type": "builtins.str"}}, "MagicAlias": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magic.MagicAlias", "name": "MagicAlias", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicAlias", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.magic", "mro": ["IPython.core.magic.MagicAlias", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicAlias.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "shell", "magic_name", "magic_kind", "magic_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicAlias.__init__", "name": "__init__", "type": null}}, "_in_call": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magic.MagicAlias._in_call", "name": "_in_call", "type": "builtins.bool"}}, "magic_kind": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magic.MagicAlias.magic_kind", "name": "magic_kind", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "magic_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magic.MagicAlias.magic_name", "name": "magic_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "magic_params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magic.MagicAlias.magic_params", "name": "magic_params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pretty_target": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magic.MagicAlias.pretty_target", "name": "pretty_target", "type": "builtins.str"}}, "shell": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magic.MagicAlias.shell", "name": "shell", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magic.MagicAlias.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magic.MagicAlias", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Magics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["traitlets.config.configurable.Configurable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magic.Magics", "name": "Magics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magic.Magics", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.magic", "mro": ["IPython.core.magic.Magics", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "shell", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.Magics.__init__", "name": "__init__", "type": null}}, "arg_err": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.Magics.arg_err", "name": "arg_err", "type": null}}, "default_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fn", "optstr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.Magics.default_option", "name": "default_option", "type": null}}, "format_latex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "strng"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.Magics.format_latex", "name": "format_latex", "type": null}}, "magics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.Magics.magics", "name": "magics", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "options_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.Magics.options_table", "name": "options_table", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "parse_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "arg_str", "opt_str", "long_opts", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.Magics.parse_options", "name": "parse_options", "type": null}}, "registered": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.Magics.registered", "name": "registered", "type": "builtins.bool"}}, "shell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.Magics.shell", "name": "shell", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magic.Magics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magic.Magics", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MagicsManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["traitlets.config.configurable.Configurable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magic.MagicsManager", "name": "MagicsManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicsManager", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.magic", "mro": ["IPython.core.magic.MagicsManager", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "shell", "config", "user_magics", "traits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicsManager.__init__", "name": "__init__", "type": null}}, "_auto_magic_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magic.MagicsManager._auto_magic_changed", "name": "_auto_magic_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magic.MagicsManager._auto_magic_changed", "name": "_auto_magic_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_auto_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.MagicsManager._auto_status", "name": "_auto_status", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "auto_magic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.MagicsManager.auto_magic", "name": "auto_magic", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "auto_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicsManager.auto_status", "name": "auto_status", "type": null}}, "lazy_magics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.MagicsManager.lazy_magics", "name": "lazy_magics", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Dict"}}}, "lsmagic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicsManager.lsmagic", "name": "lsmagic", "type": null}}, "lsmagic_docs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "brief", "missing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicsManager.lsmagic_docs", "name": "lsmagic_docs", "type": null}}, "magics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.MagicsManager.magics", "name": "magics", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Dict"}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "magic_objects"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicsManager.register", "name": "register", "type": null}}, "register_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "alias_name", "magic_name", "magic_kind", "magic_params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicsManager.register_alias", "name": "register_alias", "type": null}}, "register_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "func", "magic_kind", "magic_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicsManager.register_function", "name": "register_function", "type": null}}, "register_lazy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "fully_qualified_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.MagicsManager.register_lazy", "name": "register_lazy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "fully_qualified_name"], "arg_types": ["IPython.core.magic.MagicsManager", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_lazy of MagicsManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "registry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.MagicsManager.registry", "name": "registry", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Dict"}}}, "shell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.MagicsManager.shell", "name": "shell", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "user_magics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.MagicsManager.user_magics", "name": "user_magics", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magic.MagicsManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magic.MagicsManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Struct": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.ipstruct.Struct", "kind": "Gdef"}, "UsageError": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.UsageError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magic.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magic.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magic.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magic.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magic.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magic.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_docstring_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic._docstring_template", "name": "_docstring_template", "type": "builtins.str"}}, "_function_magic_marker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["magic_kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic._function_magic_marker", "name": "_function_magic_marker", "type": null}}, "_method_magic_marker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["magic_kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic._method_magic_marker", "name": "_method_magic_marker", "type": null}}, "arg_split": {".class": "SymbolTableNode", "cross_ref": "IPython.utils._process_win32.arg_split", "kind": "Gdef"}, "cell_magic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.cell_magic", "name": "cell_magic", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "compress_dhist": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.compress_dhist", "name": "compress_dhist", "type": null}}, "dedent": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.text.dedent", "kind": "Gdef"}, "error": {".class": "SymbolTableNode", "cross_ref": "logging.error", "kind": "Gdef"}, "getopt": {".class": "SymbolTableNode", "cross_ref": "getopt.getopt", "kind": "Gdef"}, "line_cell_magic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.line_cell_magic", "name": "line_cell_magic", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "line_magic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.line_magic", "name": "line_magic", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "magic_escapes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.magic_escapes", "name": "magic_escapes", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "magic_kinds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.magic_kinds", "name": "magic_kinds", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "magic_spec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.magic_spec", "name": "magic_spec", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "magics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "IPython.core.magic.magics", "name": "magics", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "magics_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.magics_class", "name": "magics_class", "type": null}}, "needs_local_scope": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.needs_local_scope", "name": "needs_local_scope", "type": null}}, "no_var_expand": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["magic_func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.no_var_expand", "name": "no_var_expand", "type": null}}, "observe": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.observe", "kind": "Gdef"}, "oinspect": {".class": "SymbolTableNode", "cross_ref": "IPython.core.oinspect", "kind": "Gdef"}, "on_off": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.on_off", "name": "on_off", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "output_can_be_silenced": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["magic_func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.output_can_be_silenced", "name": "output_can_be_silenced", "type": null}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "record_magic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["dct", "magic_kind", "magic_name", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.record_magic", "name": "record_magic", "type": null}}, "register_cell_magic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.register_cell_magic", "name": "register_cell_magic", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "register_line_cell_magic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.register_line_cell_magic", "name": "register_line_cell_magic", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "register_line_magic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magic.register_line_magic", "name": "register_line_magic", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "validate_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["magic_kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magic.validate_type", "name": "validate_type", "type": null}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\magic.py"}