{"data_mtime": 1753839571, "dep_lines": [21, 22, 23, 24, 25, 25, 13, 14, 15, 16, 17, 26, 32, 665, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 19], "dep_prios": [5, 5, 5, 5, 10, 20, 10, 10, 10, 10, 5, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["traitlets.config.configurable", "IPython.core.getipython", "IPython.utils.sentinel", "IPython.utils.dir2", "IPython.lib.pretty", "IPython.lib", "abc", "sys", "traceback", "warnings", "io", "traitlets", "typing", "numpy", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "os", "logging", "IPython.utils", "_collections_abc", "_frozen_importlib", "_typeshed", "numpy.core", "numpy.core.arrayprint", "traitlets.config", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel", "types", "typing_extensions"], "hash": "f2f3472f315dd0452c13a782b83279326868b472", "id": "IPython.core.formatters", "ignore_all": true, "interface_hash": "3f96fdacde1eea0b1525dd13b689d93737d1d381", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\formatters.py", "plugin_data": null, "size": 35016, "suppressed": ["decorator"], "version_id": "1.15.0"}