{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "bcf06b29b7aa2cd6864b36c998fcab7b", "files": {"z_bdae19fbfd4018c1___init___py": {"hash": "a81c5dabedf7d78f04974fd1a3ee0cac", "index": {"url": "z_bdae19fbfd4018c1___init___py.html", "file": "llm_proxy_server\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_auth_py": {"hash": "be45a3c03a38adbe9b882b84062fa22f", "index": {"url": "z_bdae19fbfd4018c1_auth_py.html", "file": "llm_proxy_server\\auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 50, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_config_py": {"hash": "6234521cb122e73d0860fec0f2cce5ce", "index": {"url": "z_bdae19fbfd4018c1_config_py.html", "file": "llm_proxy_server\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_config_api_py": {"hash": "ca03fe78f9746d42dca5550351477a79", "index": {"url": "z_bdae19fbfd4018c1_config_api_py.html", "file": "llm_proxy_server\\config_api.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_config_manager_py": {"hash": "638a464572a9e348800cada5f64cd6b4", "index": {"url": "z_bdae19fbfd4018c1_config_manager_py.html", "file": "llm_proxy_server\\config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 314, "n_excluded": 0, "n_missing": 284, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_load_balancer_py": {"hash": "e622a374596f2c18d9dc88dd2197c73c", "index": {"url": "z_bdae19fbfd4018c1_load_balancer_py.html", "file": "llm_proxy_server\\load_balancer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 144, "n_excluded": 0, "n_missing": 111, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_main_py": {"hash": "507e35ba83f574a557dac30c41b10df0", "index": {"url": "z_bdae19fbfd4018c1_main_py.html", "file": "llm_proxy_server\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 331, "n_excluded": 3, "n_missing": 244, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_models_py": {"hash": "321586f5b6364d5747f9bdfc32ba0aa5", "index": {"url": "z_bdae19fbfd4018c1_models_py.html", "file": "llm_proxy_server\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 125, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_monitoring_py": {"hash": "ce3c9527262af1e07d95b60ab62a0552", "index": {"url": "z_bdae19fbfd4018c1_monitoring_py.html", "file": "llm_proxy_server\\monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 331, "n_excluded": 0, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_proxy_manager_py": {"hash": "4b3ba2c2cd59be447e46d65962be8753", "index": {"url": "z_bdae19fbfd4018c1_proxy_manager_py.html", "file": "llm_proxy_server\\proxy_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 412, "n_excluded": 0, "n_missing": 371, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}