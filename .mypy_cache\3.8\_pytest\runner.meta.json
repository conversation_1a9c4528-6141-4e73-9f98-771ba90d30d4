{"data_mtime": 1753839573, "dep_lines": [24, 27, 19, 23, 28, 29, 33, 43, 44, 4, 6, 7, 8, 9, 10, 11, 23, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 25, 25, 5, 10, 10, 10, 10, 10, 5, 20, 5, 5, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.reports", "_pytest.timing", "_pytest.deprecated", "_pytest.nodes", "_pytest.outcomes", "_pytest.main", "_pytest.terminal", "__future__", "bdb", "dataclasses", "os", "sys", "types", "typing", "_pytest", "exceptiongroup", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "functools", "logging", "_frozen_importlib", "_pytest._code", "_pytest.config", "_typeshed", "abc"], "hash": "303a5220f31390226458771bc0f6335fd4f1635a", "id": "_pytest.runner", "ignore_all": true, "interface_hash": "e176f2b9177cfba484b52f3c593314df2a7f7eaf", "mtime": 1750470852, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\_pytest\\runner.py", "plugin_data": null, "size": 19436, "suppressed": [], "version_id": "1.15.0"}