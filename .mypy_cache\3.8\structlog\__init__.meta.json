{"data_mtime": 1753839581, "dep_lines": [9, 9, 9, 9, 9, 9, 9, 9, 9, 20, 21, 31, 32, 33, 41, 46, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30], "dependencies": ["structlog.contextvars", "structlog.dev", "structlog.processors", "structlog.stdlib", "structlog.testing", "structlog.threadlocal", "structlog.tracebacks", "structlog.types", "structlog.typing", "structlog._base", "structlog._config", "structlog._generic", "structlog._native", "structlog._output", "structlog.exceptions", "structlog.twisted", "__future__", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "typing", "functools", "os", "sys", "logging", "_frozen_importlib", "abc", "types"], "hash": "10acf19c931251b8205fda6382983e6df8615daf", "id": "structlog", "ignore_all": true, "interface_hash": "dc55690435867a5c5ffe81bd8ccf79c786169ce4", "mtime": 1750470697, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\structlog\\__init__.py", "plugin_data": null, "size": 2911, "suppressed": [], "version_id": "1.15.0"}