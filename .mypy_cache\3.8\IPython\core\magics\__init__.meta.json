{"data_mtime": 1753839580, "dep_lines": [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.magics.auto", "IPython.core.magics.basic", "IPython.core.magics.code", "IPython.core.magics.config", "IPython.core.magics.display", "IPython.core.magics.execution", "IPython.core.magics.extension", "IPython.core.magics.history", "IPython.core.magics.logging", "IPython.core.magics.namespace", "IPython.core.magics.osm", "IPython.core.magics.packaging", "IPython.core.magics.pylab", "IPython.core.magics.script", "IPython.core.magic", "builtins", "re", "json", "traitlets.utils.warnings", "pprint", "typing", "functools", "os", "sys", "logging", "_frozen_importlib", "abc", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets"], "hash": "93941931adc89a10fcd9830f1a8a22f623b8b195", "id": "IPython.core.magics", "ignore_all": true, "interface_hash": "399387c2878cfba4407691c86092e37258ca8f50", "mtime": 1708667830, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\IPython\\core\\magics\\__init__.py", "plugin_data": null, "size": 1619, "suppressed": [], "version_id": "1.15.0"}