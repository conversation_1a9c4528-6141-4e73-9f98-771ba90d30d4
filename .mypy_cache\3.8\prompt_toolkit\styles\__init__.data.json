{".class": "MypyFile", "_fullname": "prompt_toolkit.styles", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ANSI_COLOR_NAMES": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.ANSI_COLOR_NAMES", "kind": "Gdef"}, "AdjustBrightnessStyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.AdjustBrightnessStyleTransformation", "kind": "Gdef"}, "Attrs": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.Attrs", "kind": "Gdef"}, "BaseStyle": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.BaseStyle", "kind": "Gdef"}, "ConditionalStyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.ConditionalStyleTransformation", "kind": "Gdef"}, "DEFAULT_ATTRS": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.DEFAULT_ATTRS", "kind": "Gdef"}, "DummyStyle": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.DummyStyle", "kind": "Gdef"}, "DummyStyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.DummyStyleTransformation", "kind": "Gdef"}, "DynamicStyle": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.base.DynamicStyle", "kind": "Gdef"}, "DynamicStyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.DynamicStyleTransformation", "kind": "Gdef"}, "NAMED_COLORS": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.named_colors.NAMED_COLORS", "kind": "Gdef"}, "Priority": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style.Priority", "kind": "Gdef"}, "ReverseStyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.ReverseStyleTransformation", "kind": "Gdef"}, "SetDefaultColorStyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.SetDefaultColorStyleTransformation", "kind": "Gdef"}, "Style": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style.Style", "kind": "Gdef"}, "StyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.StyleTransformation", "kind": "Gdef"}, "SwapLightAndDarkStyleTransformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.SwapLightAndDarkStyleTransformation", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.styles.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "default_pygments_style": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.defaults.default_pygments_style", "kind": "Gdef"}, "default_ui_style": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.defaults.default_ui_style", "kind": "Gdef"}, "merge_style_transformations": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style_transformation.merge_style_transformations", "kind": "Gdef"}, "merge_styles": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style.merge_styles", "kind": "Gdef"}, "parse_color": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style.parse_color", "kind": "Gdef"}, "pygments_token_to_classname": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.pygments.pygments_token_to_classname", "kind": "Gdef"}, "style_from_pygments_cls": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.pygments.style_from_pygments_cls", "kind": "Gdef"}, "style_from_pygments_dict": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.pygments.style_from_pygments_dict", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\prompt_toolkit\\styles\\__init__.py"}